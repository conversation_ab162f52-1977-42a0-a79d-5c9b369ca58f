#!/usr/bin/env python3
"""
BJT ESD Parameter Extractor - De<PERSON> Script
Demonstrates the complete workflow without GUI
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

def load_test_data():
    """Load the test data from 1.csv"""
    print("Loading test data from 1.csv...")
    
    if not os.path.exists("1.csv"):
        print("Error: 1.csv not found!")
        return None, None
    
    try:
        df = pd.read_csv("1.csv")
        voltage = df.iloc[:, 0].values
        current = np.abs(df.iloc[:, 1].values)  # Use absolute values
        
        print(f"✓ Loaded {len(voltage)} data points")
        print(f"  Voltage range: {voltage.min():.3f} to {voltage.max():.3f} V")
        print(f"  Current range: {current.min():.3e} to {current.max():.3e} A")
        
        return voltage, current
        
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None

def extract_parameters(voltage, current):
    """Extract BJT ESD parameters from I-V data"""
    print("\nExtracting BJT ESD parameters...")
    
    try:
        # Find different regions
        log_current = np.log10(current + 1e-15)
        d_log_i = np.gradient(log_current, voltage)
        
        # Leakage region (first 20% of data or until rapid increase)
        leakage_end = min(len(voltage) // 5, 50)
        for i in range(10, len(d_log_i)):
            if d_log_i[i] > 0.5:  # Rapid increase threshold
                leakage_end = i
                break
        
        # Trigger voltage (where derivative is maximum)
        trigger_idx = np.argmax(d_log_i[leakage_end:]) + leakage_end
        vt1 = voltage[trigger_idx]
        
        # Holding voltage (where current reaches significant level)
        vh_idx = len(voltage) - 1
        for i in range(trigger_idx, len(current)):
            if current[i] > 1e-3:  # 1mA threshold
                vh_idx = i
                break
        vh = voltage[vh_idx]
        
        # Leakage current (average of leakage region)
        i_leak = np.mean(current[:leakage_end])
        
        # Exponential factor from trigger region slope
        k = np.mean(d_log_i[trigger_idx:vh_idx]) * np.log(10)
        k = max(1.0, min(10.0, k))  # Clamp to reasonable range
        
        # On resistance from high current region
        if vh_idx < len(voltage) - 5:
            v_high = voltage[vh_idx:]
            i_high = current[vh_idx:]
            
            # Linear fit for resistance
            if len(v_high) > 3:
                slope = np.polyfit(v_high[-5:], i_high[-5:], 1)[0]
                ron = 1.0 / slope if slope > 0 else 2.0
                ron = max(0.1, min(10.0, ron))
            else:
                ron = 2.0
        else:
            ron = 2.0
        
        # Other parameters
        i_offset = current[vh_idx] if vh_idx < len(current) else 0.05
        isb = np.max(current) * 0.1  # 10% of max current
        vsb = vh
        
        parameters = {
            'I_leak': i_leak,
            'Vt1': vt1,
            'k': k,
            'Ron': ron,
            'Vh': vh,
            'I_offset': i_offset,
            'Isb': isb,
            'Vsb': vsb
        }
        
        print("✓ Extracted parameters:")
        for param, value in parameters.items():
            if 'I_' in param or 'Isb' in param:
                print(f"  {param}: {value:.6e}")
            else:
                print(f"  {param}: {value:.3f}")
        
        return parameters
        
    except Exception as e:
        print(f"Error extracting parameters: {e}")
        return None

def calculate_model_current(voltage, parameters):
    """Calculate model current using BJT ESD equations"""
    i_leak = parameters['I_leak']
    vt1 = parameters['Vt1']
    k = parameters['k']
    ron = parameters['Ron']
    vh = parameters['Vh']
    i_offset = parameters['I_offset']
    isb = parameters['Isb']
    vsb = parameters['Vsb']
    
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < vt1:
            # Leakage region
            current[i] = i_leak * np.exp(v / 1.0)
        elif v < vh:
            # Trigger region
            current[i] = i_leak * np.exp(k * (v - vt1) / vt1)
        else:
            # Snapback region
            linear_term = (v - vsb) / ron if ron > 0 else 0
            exp_term = isb * np.exp(-(v - vsb)) if v > vsb else isb
            current[i] = i_offset + linear_term + exp_term
    
    return current

def plot_results(voltage, measured_current, model_current, parameters):
    """Plot measurement data and model fit"""
    print("\nGenerating comparison plot...")
    
    try:
        plt.figure(figsize=(12, 8))
        
        # Plot measured data
        plt.semilogy(voltage, measured_current, 'bo-', markersize=4, 
                    linewidth=1.5, label='Measurement Data', alpha=0.7)
        
        # Plot model
        plt.semilogy(voltage, model_current, 'r-', linewidth=2, 
                    label='BJT ESD Model', alpha=0.9)
        
        # Add parameter annotations
        plt.axvline(parameters['Vt1'], color='g', linestyle='--', alpha=0.7, 
                   label=f"Trigger Voltage = {parameters['Vt1']:.2f}V")
        plt.axvline(parameters['Vh'], color='orange', linestyle='--', alpha=0.7, 
                   label=f"Holding Voltage = {parameters['Vh']:.2f}V")
        
        plt.xlabel('Voltage (V)', fontsize=12)
        plt.ylabel('Current (A)', fontsize=12)
        plt.title('BJT ESD Device I-V Characteristics\nMeasurement vs Model', 
                 fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, voltage.max() * 1.05)
        plt.ylim(1e-12, measured_current.max() * 10)
        
        # Save plot
        plt.savefig('bjt_esd_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✓ Plot saved as bjt_esd_comparison.png")
        
    except Exception as e:
        print(f"Error creating plot: {e}")

def generate_spice_model(parameters):
    """Generate HSPICE-compatible SPICE model"""
    print("\nGenerating SPICE model...")
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    spice_content = f"""* BJT ESD Device SPICE Model (.ckt file)
* Generated by BJT ESD Parameter Extractor
* Date: {timestamp}

* Model Parameters:
* I_leak = {parameters['I_leak']:.6e} A (Leakage Current)
* Vt1 = {parameters['Vt1']:.3f} V (Trigger Voltage)
* k = {parameters['k']:.3f} (Exponential Factor)
* Ron = {parameters['Ron']:.3f} Ohm (On Resistance)
* Vh = {parameters['Vh']:.3f} V (Holding Voltage)
* I_offset = {parameters['I_offset']:.6f} A (Current Offset)
* Isb = {parameters['Isb']:.6f} A (Snapback Current)
* Vsb = {parameters['Vsb']:.3f} V (Snapback Voltage)

* BJT ESD Device Subcircuit
.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak={parameters['I_leak']:.6e}
.param Vt1={parameters['Vt1']:.3f}
.param k={parameters['k']:.3f}
.param Ron={parameters['Ron']:.3f}
.param Vh={parameters['Vh']:.3f}
.param I_offset={parameters['I_offset']:.6f}
.param Isb={parameters['Isb']:.6f}
.param Vsb={parameters['Vsb']:.3f}

* Main ESD protection structure
* Using diode + resistor model for HSPICE compatibility
D_main anode n_main D_ESD_MAIN
R_main n_main cathode {{Ron}}

* Parallel leakage path
R_leak anode cathode {{1/(I_leak*1e6)}}

* Diode model parameters
.model D_ESD_MAIN D(
+ IS={{I_leak*1e6}}
+ N={{k}}
+ RS=0.05
+ BV={{Vh*1.05}}
+ IBV={{I_offset*0.1}}
+ CJO=1e-12
+ TT=1e-12
+ )

.ends bjt_esd_device

* Usage Example:
* X_esd node_anode node_cathode bjt_esd_device

* Test Circuit for HSPICE Simulation
.subckt test_circuit
* Voltage source for DC sweep
Vin n1 0 DC 0
* ESD device under test
X_esd n1 0 bjt_esd_device
* DC analysis
.dc Vin 0 20 0.1
* Print current through voltage source
.print dc V(n1) I(Vin)
.probe dc V(n1) I(Vin)
.ends test_circuit

* HSPICE simulation options
.option post=2
.option accurate
.option gmin=1e-15
.option abstol=1e-15
.option reltol=1e-6

* To run this simulation:
* hspice bjt_esd_model.ckt -o bjt_esd_results.lis

.end
"""
    
    try:
        with open("bjt_esd_model.ckt", "w") as f:
            f.write(spice_content)
        
        print("✓ SPICE model saved to bjt_esd_model.ckt")
        print("✓ Ready for HSPICE simulation")
        
        return True
        
    except Exception as e:
        print(f"Error saving SPICE model: {e}")
        return False

def create_hspice_script():
    """Create HSPICE simulation script"""
    script_content = """@echo off
echo Running HSPICE simulation for BJT ESD model...
echo.

if not exist bjt_esd_model.ckt (
    echo Error: bjt_esd_model.ckt not found!
    pause
    exit /b 1
)

echo Starting HSPICE simulation...
hspice bjt_esd_model.ckt -o bjt_esd_results.lis

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Simulation completed successfully!
    echo ✓ Results saved to bjt_esd_results.lis
    echo.
    echo You can now compare the simulation results with measurement data.
) else (
    echo.
    echo ✗ Simulation failed!
    echo Check HSPICE installation and model file.
)

echo.
pause
"""
    
    try:
        with open("run_hspice.bat", "w") as f:
            f.write(script_content)
        
        print("✓ HSPICE script saved to run_hspice.bat")
        
    except Exception as e:
        print(f"Error creating HSPICE script: {e}")

def main():
    """Main demo function"""
    print("BJT ESD Parameter Extractor - Demo")
    print("=" * 50)
    print("This demo shows the complete parameter extraction workflow")
    print("using the provided test data (1.csv)")
    print()
    
    # Load test data
    voltage, current = load_test_data()
    if voltage is None:
        return False
    
    # Extract parameters
    parameters = extract_parameters(voltage, current)
    if parameters is None:
        return False
    
    # Calculate model current
    print("\nCalculating model current...")
    model_current = calculate_model_current(voltage, parameters)
    
    # Calculate fit quality
    log_measured = np.log10(current + 1e-15)
    log_model = np.log10(model_current + 1e-15)
    rmse = np.sqrt(np.mean((log_measured - log_model) ** 2))
    print(f"✓ Model fit RMSE (log scale): {rmse:.4f}")
    
    # Plot results
    plot_results(voltage, current, model_current, parameters)
    
    # Generate SPICE model
    success = generate_spice_model(parameters)
    if not success:
        return False
    
    # Create HSPICE script
    create_hspice_script()
    
    print("\n" + "=" * 50)
    print("✓ Demo completed successfully!")
    print("\nGenerated files:")
    print("  - bjt_esd_model.ckt     (SPICE model)")
    print("  - bjt_esd_comparison.png (Comparison plot)")
    print("  - run_hspice.bat        (HSPICE simulation script)")
    print("\nNext steps:")
    print("  1. Review the comparison plot")
    print("  2. Run HSPICE simulation: run_hspice.bat")
    print("  3. Use the GUI application: python main.py")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
