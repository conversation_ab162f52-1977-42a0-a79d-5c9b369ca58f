#!/usr/bin/env python3
"""
Demo script for BJT ESD Parameter Extractor
Shows basic usage without GUI
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.bjt_esd_model import BJTESDModel
from utils.data_loader import DataLoader

def demo_parameter_extraction():
    """
    Demonstrate parameter extraction from sample data
    """
    print("BJT ESD Parameter Extractor - Demo")
    print("=" * 40)
    
    # Initialize components
    model = BJTESDModel()
    loader = DataLoader()
    
    # Load sample data
    if not os.path.exists("1.csv"):
        print("Error: Sample data file '1.csv' not found")
        return False
    
    print("Loading sample data...")
    voltage, current, metadata = loader.load_data("1.csv")
    print(f"Loaded {len(voltage)} data points")
    
    if metadata:
        print(f"Found metadata with {len(metadata)} parameters")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
    
    # Display initial parameters
    print("\nInitial model parameters:")
    initial_params = model.get_parameters()
    param_info = model.get_parameter_info()
    
    for name, value in initial_params.items():
        info = param_info.get(name, {})
        unit = info.get('unit', '')
        description = info.get('description', name)
        print(f"  {description}: {value:.6g} {unit}")
    
    # Fit parameters
    print("\nFitting parameters to experimental data...")
    fitted_params = model.fit_parameters(voltage, current)
    
    r_squared = fitted_params.get('r_squared', 0)
    print(f"Parameter fitting completed with R² = {r_squared:.6f}")
    
    # Display fitted parameters
    print("\nFitted parameters:")
    for name, value in fitted_params.items():
        if name != 'r_squared':
            info = param_info.get(name, {})
            unit = info.get('unit', '')
            description = info.get('description', name)
            print(f"  {description}: {value:.6g} {unit}")
    
    # Generate fitted curve
    fitted_current = model.current_equation(voltage, **fitted_params)
    
    # Create comparison plot
    plt.figure(figsize=(12, 8))
    
    # Linear scale plot
    plt.subplot(2, 1, 1)
    plt.plot(voltage, current, 'bo', markersize=3, alpha=0.7, label='Experimental Data')
    plt.plot(voltage, fitted_current, 'r-', linewidth=2, label='Fitted Model')
    plt.xlabel('Voltage (V)')
    plt.ylabel('Current (A)')
    plt.title('BJT ESD Device I-V Characteristics (Linear Scale)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # Log scale plot
    plt.subplot(2, 1, 2)
    plt.semilogy(voltage, current, 'bo', markersize=3, alpha=0.7, label='Experimental Data')
    plt.semilogy(voltage, fitted_current, 'r-', linewidth=2, label='Fitted Model')
    plt.xlabel('Voltage (V)')
    plt.ylabel('Current (A)')
    plt.title('BJT ESD Device I-V Characteristics (Log Scale)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    
    # Save plot
    output_file = "bjt_esd_fitting_result.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"\nPlot saved as: {output_file}")
    
    # Show plot
    plt.show()
    
    return True

def demo_model_regions():
    """
    Demonstrate different operating regions of the BJT ESD model
    """
    print("\nDemonstrating BJT ESD Model Regions")
    print("=" * 40)
    
    model = BJTESDModel()
    
    # Create voltage range
    voltage = np.linspace(0, 20, 1000)
    
    # Get default parameters
    params = model.get_parameters()
    
    # Calculate current
    current = model.current_equation(voltage, **params)
    
    # Create detailed plot showing regions
    plt.figure(figsize=(14, 10))
    
    # Main I-V plot
    plt.subplot(2, 2, 1)
    plt.semilogy(voltage, current, 'b-', linewidth=2)
    plt.axvline(params['Vt1'], color='r', linestyle='--', alpha=0.7, label=f"Vt1 = {params['Vt1']:.1f}V")
    plt.axvline(params['Vh'], color='g', linestyle='--', alpha=0.7, label=f"Vh = {params['Vh']:.1f}V")
    plt.xlabel('Voltage (V)')
    plt.ylabel('Current (A)')
    plt.title('BJT ESD Model - Operating Regions')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # Region 1: Leakage
    plt.subplot(2, 2, 2)
    mask1 = voltage < params['Vt1']
    plt.semilogy(voltage[mask1], current[mask1], 'b-', linewidth=2)
    plt.xlabel('Voltage (V)')
    plt.ylabel('Current (A)')
    plt.title('Region 1: Leakage (V < Vt1)')
    plt.grid(True, alpha=0.3)
    
    # Region 2: Trigger
    plt.subplot(2, 2, 3)
    mask2 = (voltage >= params['Vt1']) & (voltage < params['Vh'])
    plt.semilogy(voltage[mask2], current[mask2], 'r-', linewidth=2)
    plt.xlabel('Voltage (V)')
    plt.ylabel('Current (A)')
    plt.title('Region 2: Trigger (Vt1 ≤ V < Vh)')
    plt.grid(True, alpha=0.3)
    
    # Region 3: Snapback
    plt.subplot(2, 2, 4)
    mask3 = voltage >= params['Vh']
    plt.plot(voltage[mask3], current[mask3], 'g-', linewidth=2)
    plt.xlabel('Voltage (V)')
    plt.ylabel('Current (A)')
    plt.title('Region 3: Snapback (V ≥ Vh)')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    output_file = "bjt_esd_model_regions.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Model regions plot saved as: {output_file}")
    
    # Show plot
    plt.show()

def main():
    """
    Run demo
    """
    try:
        # Run parameter extraction demo
        if demo_parameter_extraction():
            print("\n" + "=" * 40)
            
            # Run model regions demo
            demo_model_regions()
            
            print("\nDemo completed successfully!")
            print("\nTo run the full GUI application, use: python main.py")
        else:
            print("Demo failed - please check error messages above")
            return False
            
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
