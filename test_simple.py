#!/usr/bin/env python3
"""
Simple test for BJT ESD Parameter Extractor
"""

import sys
import os
import numpy as np
import pandas as pd

def test_basic_functionality():
    """Test basic functionality without complex formatting"""
    print("BJT ESD Parameter Extractor - Simple Test")
    print("=" * 50)

    # Test 1: Load CSV data
    print("Test 1: Loading CSV data...")
    try:
        if os.path.exists("1.csv"):
            # Read CSV file manually to handle comments and parameters
            voltage_list = []
            current_list = []

            with open("1.csv", "r") as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                # Skip empty lines, comments, and parameter lines
                if not line or line.startswith('#') or ',' not in line:
                    continue

                # Skip header line
                if 'Voltage' in line or 'Current' in line:
                    continue

                # Try to parse voltage and current
                try:
                    parts = line.split(',')
                    if len(parts) >= 2:
                        v = float(parts[0])
                        i = float(parts[1])
                        voltage_list.append(v)
                        current_list.append(abs(i))
                except ValueError:
                    # Skip lines that can't be parsed as numbers
                    continue

            voltage = np.array(voltage_list)
            current = np.array(current_list)

            print("✓ CSV loaded successfully")
            print("  Data points: {}".format(len(voltage)))
            print("  Voltage range: {:.3f} to {:.3f} V".format(float(voltage.min()), float(voltage.max())))
            print("  Current range: {:.3e} to {:.3e} A".format(float(current.min()), float(current.max())))
        else:
            print("✗ File 1.csv not found")
            return False
    except Exception as e:
        print("✗ Error loading CSV: {}".format(e))
        return False

    # Test 2: Basic parameter extraction
    print("\nTest 2: Basic parameter extraction...")
    try:
        # Simple parameter extraction
        i_leak = np.mean(current[:20])  # Average of first 20 points

        # Find trigger voltage (where current increases rapidly)
        log_current = np.log10(current + 1e-15)
        d_log_i = np.gradient(log_current, voltage)
        trigger_idx = np.argmax(d_log_i)
        vt1 = voltage[trigger_idx]

        # Find holding voltage (where current > 1mA)
        vh_idx = len(voltage) - 1
        for i, curr in enumerate(current):
            if curr > 1e-3:
                vh_idx = i
                break
        vh = voltage[vh_idx]

        # Simple resistance calculation
        if vh_idx < len(voltage) - 5:
            v_end = voltage[-5:]
            i_end = current[-5:]
            slope = (i_end[-1] - i_end[0]) / (v_end[-1] - v_end[0])
            ron = 1.0 / slope if slope > 0 else 2.0
        else:
            ron = 2.0

        parameters = {
            'I_leak': i_leak,
            'Vt1': vt1,
            'k': 3.0,
            'Ron': ron,
            'Vh': vh,
            'I_offset': 0.05,
            'Isb': 0.04,
            'Vsb': vh
        }

        print("✓ Parameters extracted:")
        for param, value in parameters.items():
            if 'I_' in param:
                print("  {}: {:.6e}".format(param, float(value)))
            else:
                print("  {}: {:.3f}".format(param, float(value)))

    except Exception as e:
        print("✗ Error extracting parameters: {}".format(e))
        return False

    # Test 3: Generate simple SPICE model
    print("\nTest 3: Generating SPICE model...")
    try:
        spice_content = """* BJT ESD Device SPICE Model
* Generated by BJT ESD Parameter Extractor

.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak={:.6e}
.param Vt1={:.3f}
.param k={:.3f}
.param Ron={:.3f}
.param Vh={:.3f}
.param I_offset={:.6f}
.param Isb={:.6f}
.param Vsb={:.3f}

* Simple diode + resistor model
D_main anode n_main D_ESD
R_main n_main cathode {:.3f}

.model D_ESD D(
+ IS={:.3e}
+ N={:.3f}
+ RS=0.1
+ BV={:.3f}
+ IBV={:.6f}
+ )

.ends bjt_esd_device

* Test circuit
.subckt test_circuit
Vin n1 0 DC 0
X_esd n1 0 bjt_esd_device
.dc Vin 0 20 0.1
.print dc I(Vin)
.ends test_circuit

.end
""".format(
            float(parameters['I_leak']),
            float(parameters['Vt1']),
            float(parameters['k']),
            float(parameters['Ron']),
            float(parameters['Vh']),
            float(parameters['I_offset']),
            float(parameters['Isb']),
            float(parameters['Vsb']),
            float(parameters['Ron']),
            float(parameters['I_leak'] * 1e6),
            float(parameters['k']),
            float(parameters['Vh'] * 1.05),
            float(parameters['I_offset'] * 0.1)
        )

        with open("bjt_esd_simple.ckt", "w") as f:
            f.write(spice_content)

        print("✓ SPICE model saved to bjt_esd_simple.ckt")

    except Exception as e:
        print("✗ Error generating SPICE model: {}".format(e))
        return False

    # Test 4: Test imports
    print("\nTest 4: Testing module imports...")
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

        from data.data_loader import DataLoader
        print("✓ DataLoader import OK")

        from data.parameter_extractor import ParameterExtractor
        print("✓ ParameterExtractor import OK")

        from models.bjt_esd_model import BJTESDModel
        print("✓ BJTESDModel import OK")

        from models.spice_generator import SpiceGenerator
        print("✓ SpiceGenerator import OK")

    except Exception as e:
        print("✗ Module import error: {}".format(e))
        return False

    print("\n" + "=" * 50)
    print("✓ All tests passed!")
    print("✓ Basic functionality working correctly")
    print("\nGenerated files:")
    print("  - bjt_esd_simple.ckt (Simple SPICE model)")
    print("\nNext steps:")
    print("  1. Test HSPICE: hspice bjt_esd_simple.ckt -o test.lis")
    print("  2. Try GUI: python main.py")

    return True

if __name__ == "__main__":
    success = test_basic_functionality()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
