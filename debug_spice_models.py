#!/usr/bin/env python3
"""
Debug SPICE Models vs Fitted Model
Systematically check each SPICE model implementation
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def load_real_parameters():
    """Load real parameters from current data"""
    try:
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        # Load real data
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        # Extract real parameters
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        print("Real extracted parameters:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"  {key}: {value:.6e}")
            else:
                print(f"  {key}: {value:.6f}")
                
        return parameters, data
        
    except Exception as e:
        print(f"Failed to load real parameters: {e}")
        # Use example parameters
        return {
            'I_leak': 1e-9,
            'Vt1': 12.0,
            'k': 3.0,
            'Ron': 2.0,
            'Vh': 14.0,
            'I_offset': 0.05,
            'Isb': 0.04,
            'Vsb': 14.0
        }, None

def calculate_fitted_model(voltage, params):
    """Calculate fitted model current - EXACT implementation"""
    print("\nFITTED MODEL CALCULATION:")
    print("Using exact equations from parameter_extractor.py")
    
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < params['Vt1']:
            # Leakage region
            current[i] = params['I_leak'] * np.exp(v / 1.0)
        elif v < params['Vh']:
            # Trigger region  
            current[i] = params['I_leak'] * np.exp(params['k'] * (v - params['Vt1']) / params['Vt1'])
        else:
            # Snapback region
            linear_term = (v - params['Vsb']) / params['Ron'] if params['Ron'] > 0 else 0
            exp_term = params['Isb'] * np.exp(-(v - params['Vsb'])) if v > params['Vsb'] else params['Isb']
            current[i] = params['I_offset'] + linear_term + exp_term
            
    return current

def calculate_original_spice_behavioral(voltage, params):
    """Calculate original SPICE behavioral model"""
    print("\nORIGINAL SPICE BEHAVIORAL MODEL:")
    print("From spice_generator.py behavioral equations")
    
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < 0:
            current[i] = params['I_leak'] * 1e-3
        elif v < params['Vt1']:
            current[i] = params['I_leak'] * np.exp(v / 1.0)
        elif v < params['Vh']:
            current[i] = params['I_leak'] * np.exp(params['k'] * (v - params['Vt1']) / params['Vt1'])
        else:
            current[i] = params['I_offset'] + (v - params['Vsb']) / params['Ron'] + params['Isb'] * np.exp(-(v - params['Vsb']))
            
    return current

def calculate_original_spice_diode(voltage, params):
    """Calculate original SPICE diode model"""
    print("\nORIGINAL SPICE DIODE MODEL:")
    print("From spice_generator.py diode implementation")
    
    # Original (problematic) parameter conversion
    IS = params['I_leak'] * 1e6  # ❌ This is the problem!
    N = params['k']
    BV = params['Vh'] * 1.05
    IBV = params['I_offset'] * 0.1
    Ron = params['Ron']
    
    print(f"Converted parameters:")
    print(f"  IS = {IS:.6e} (I_leak * 1e6)")
    print(f"  N = {N:.3f}")
    print(f"  BV = {BV:.3f}")
    print(f"  IBV = {IBV:.6e}")
    
    current = np.zeros_like(voltage)
    Vt = 0.026  # Thermal voltage
    
    for i, v in enumerate(voltage):
        if v < BV:
            # Forward diode: I = IS * (exp(V/(N*Vt)) - 1)
            try:
                current[i] = IS * (np.exp(v / (N * Vt)) - 1)
            except OverflowError:
                current[i] = 1e10  # Large value for overflow
        else:
            # Breakdown region
            current[i] = IBV + (v - BV) / Ron
            
    return current

def calculate_fixed_behavioral(voltage, params):
    """Calculate fixed behavioral model"""
    print("\nFIXED BEHAVIORAL MODEL:")
    print("From fixed_spice_generator.py")
    
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < 0:
            current[i] = params['I_leak'] * 1e-3
        elif v < params['Vt1']:
            current[i] = params['I_leak'] * np.exp(v / 1.0)
        elif v < params['Vh']:
            current[i] = params['I_leak'] * np.exp(params['k'] * (v - params['Vt1']) / params['Vt1'])
        else:
            current[i] = params['I_offset'] + (v - params['Vsb']) / params['Ron'] + params['Isb'] * np.exp(-(v - params['Vsb']))
            
    return current

def calculate_fixed_diode(voltage, params):
    """Calculate fixed diode model"""
    print("\nFIXED DIODE MODEL:")
    print("From fixed_spice_generator.py with corrected parameters")
    
    # Corrected parameter conversion
    IS = params['I_leak'] / 1000  # Remove wrong scaling
    N = max(1.0, min(10.0, params['k']))
    BV = params['Vt1'] * 0.9  # Use trigger voltage
    IBV = params['I_offset']
    RS = params['Ron'] * 0.1
    
    print(f"Corrected parameters:")
    print(f"  IS = {IS:.6e} (I_leak / 1000)")
    print(f"  N = {N:.3f}")
    print(f"  BV = {BV:.3f}")
    print(f"  IBV = {IBV:.6e}")
    
    current = np.zeros_like(voltage)
    Vt = 0.026
    
    for i, v in enumerate(voltage):
        if v < BV:
            try:
                current[i] = IS * (np.exp(v / (N * Vt)) - 1)
            except OverflowError:
                current[i] = 1e10
        else:
            current[i] = IBV + (v - BV) / params['Ron']
            
    return current

def detailed_comparison(voltage, params):
    """Detailed comparison of all models"""
    print("\n" + "="*80)
    print("DETAILED MODEL COMPARISON")
    print("="*80)
    
    # Calculate all models
    fitted = calculate_fitted_model(voltage, params)
    orig_behavioral = calculate_original_spice_behavioral(voltage, params)
    orig_diode = calculate_original_spice_diode(voltage, params)
    fixed_behavioral = calculate_fixed_behavioral(voltage, params)
    fixed_diode = calculate_fixed_diode(voltage, params)
    
    # Test specific voltage points
    test_voltages = [1, 5, 10, 12, 15, 18, 20]
    
    print(f"\nPoint-by-point comparison:")
    print(f"{'V':>3} | {'Fitted':>12} | {'Orig_Behav':>12} | {'Orig_Diode':>12} | {'Fix_Behav':>12} | {'Fix_Diode':>12}")
    print("-" * 80)
    
    for test_v in test_voltages:
        idx = np.argmin(np.abs(voltage - test_v))
        v = voltage[idx]
        
        print(f"{v:3.0f} | {fitted[idx]:12.3e} | {orig_behavioral[idx]:12.3e} | {orig_diode[idx]:12.3e} | {fixed_behavioral[idx]:12.3e} | {fixed_diode[idx]:12.3e}")
    
    # Calculate errors
    def calc_log_error(model_current, fitted_current):
        try:
            log_fitted = np.log10(fitted_current + 1e-15)
            log_model = np.log10(model_current + 1e-15)
            return np.mean(np.abs(log_fitted - log_model))
        except:
            return float('inf')
    
    errors = {
        'Original Behavioral': calc_log_error(orig_behavioral, fitted),
        'Original Diode': calc_log_error(orig_diode, fitted),
        'Fixed Behavioral': calc_log_error(fixed_behavioral, fitted),
        'Fixed Diode': calc_log_error(fixed_diode, fitted)
    }
    
    print(f"\nLog Errors vs Fitted Model:")
    for model, error in errors.items():
        if error == float('inf'):
            print(f"  {model:20}: OVERFLOW/ERROR")
        elif error < 1e-10:
            print(f"  {model:20}: {error:.2e} ✅ PERFECT")
        elif error < 1e-6:
            print(f"  {model:20}: {error:.2e} ✅ EXCELLENT")
        elif error < 1e-3:
            print(f"  {model:20}: {error:.2e} ⚠️  GOOD")
        elif error < 0.1:
            print(f"  {model:20}: {error:.2e} ⚠️  ACCEPTABLE")
        else:
            print(f"  {model:20}: {error:.2e} ❌ POOR")
    
    return {
        'fitted': fitted,
        'orig_behavioral': orig_behavioral,
        'orig_diode': orig_diode,
        'fixed_behavioral': fixed_behavioral,
        'fixed_diode': fixed_diode,
        'errors': errors
    }

def create_debug_plots(voltage, results):
    """Create detailed debug plots"""
    print(f"\nCreating debug plots...")
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Plot 1: Linear scale comparison
    ax1.plot(voltage, results['fitted'], 'k-', linewidth=3, label='Fitted Model', alpha=0.8)
    ax1.plot(voltage, results['orig_behavioral'], 'r--', linewidth=2, label='Original Behavioral', alpha=0.7)
    ax1.plot(voltage, results['fixed_behavioral'], 'b--', linewidth=2, label='Fixed Behavioral', alpha=0.7)
    ax1.set_xlabel('Voltage (V)')
    ax1.set_ylabel('Current (A)')
    ax1.set_title('Linear Scale: Behavioral Models')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Log scale comparison
    ax2.semilogy(voltage, results['fitted'], 'k-', linewidth=3, label='Fitted Model', alpha=0.8)
    ax2.semilogy(voltage, results['orig_behavioral'], 'r--', linewidth=2, label='Original Behavioral', alpha=0.7)
    ax2.semilogy(voltage, results['fixed_behavioral'], 'b--', linewidth=2, label='Fixed Behavioral', alpha=0.7)
    ax2.set_xlabel('Voltage (V)')
    ax2.set_ylabel('Current (A)')
    ax2.set_title('Log Scale: Behavioral Models')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Diode models (limited range to avoid overflow)
    valid_mask = results['orig_diode'] < 1e10
    ax3.plot(voltage, results['fitted'], 'k-', linewidth=3, label='Fitted Model', alpha=0.8)
    ax3.plot(voltage[valid_mask], results['orig_diode'][valid_mask], 'r--', linewidth=2, label='Original Diode', alpha=0.7)
    ax3.plot(voltage, results['fixed_diode'], 'b--', linewidth=2, label='Fixed Diode', alpha=0.7)
    ax3.set_xlabel('Voltage (V)')
    ax3.set_ylabel('Current (A)')
    ax3.set_title('Linear Scale: Diode Models')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: Error comparison
    models = list(results['errors'].keys())
    errors = [results['errors'][model] if results['errors'][model] != float('inf') else 100 for model in models]
    colors = ['red', 'orange', 'green', 'blue']
    
    bars = ax4.bar(models, errors, color=colors, alpha=0.7)
    ax4.set_ylabel('Log Error')
    ax4.set_title('Model Accuracy Comparison')
    ax4.set_yscale('log')
    ax4.grid(True, alpha=0.3)
    
    # Add error values on bars
    for bar, error in zip(bars, errors):
        height = bar.get_height()
        if error != 100:
            ax4.text(bar.get_x() + bar.get_width()/2., height,
                    f'{error:.2e}', ha='center', va='bottom', rotation=45)
    
    plt.tight_layout()
    plt.savefig('spice_model_debug.png', dpi=150, bbox_inches='tight')
    print("✓ Debug plot saved: spice_model_debug.png")

def identify_specific_issues():
    """Identify specific issues in each model"""
    print(f"\n" + "="*80)
    print("SPECIFIC ISSUE IDENTIFICATION")
    print("="*80)
    
    issues = {
        "Original Behavioral": [
            "✅ Uses same equations as fitted model",
            "✅ Should be identical to fitted model",
            "❓ If different, check implementation details"
        ],
        "Original Diode": [
            "❌ IS parameter scaled by 1e6 (completely wrong)",
            "❌ Uses diode equation instead of ESD equation",
            "❌ N parameter has different meaning",
            "❌ BV uses Vh instead of Vt1",
            "❌ Will cause massive current errors"
        ],
        "Fixed Behavioral": [
            "✅ Should be identical to fitted model",
            "✅ Uses exact same equations",
            "❓ If different, implementation bug exists"
        ],
        "Fixed Diode": [
            "⚠️  Still uses diode approximation",
            "✅ Corrected IS scaling",
            "✅ Better parameter mapping",
            "⚠️  Will never be exact match"
        ]
    }
    
    for model, issue_list in issues.items():
        print(f"\n{model}:")
        for issue in issue_list:
            print(f"  {issue}")

def main():
    """Main debug function"""
    print("SPICE MODEL DEBUG ANALYSIS")
    print("=" * 60)
    print("Systematically checking each SPICE model vs fitted model")
    
    # Load real parameters
    params, data = load_real_parameters()
    
    # Create voltage range for testing
    voltage = np.linspace(0, 20, 200)
    
    # Run detailed comparison
    results = detailed_comparison(voltage, params)
    
    # Create debug plots
    create_debug_plots(voltage, results)
    
    # Identify specific issues
    identify_specific_issues()
    
    # Final recommendations
    print(f"\n" + "="*80)
    print("RECOMMENDATIONS")
    print("="*80)
    
    best_model = min(results['errors'], key=results['errors'].get)
    best_error = results['errors'][best_model]
    
    print(f"\nBest performing model: {best_model}")
    print(f"Error: {best_error:.2e}")
    
    if best_error < 1e-10:
        print("✅ EXCELLENT: This model should work perfectly")
    elif best_error < 1e-6:
        print("✅ GOOD: This model should work well")
    elif best_error < 1e-3:
        print("⚠️  ACCEPTABLE: This model has some errors")
    else:
        print("❌ POOR: This model has significant errors")
        print("\n🔧 DEBUGGING STEPS:")
        print("1. Check equation implementation line by line")
        print("2. Verify parameter values are correctly passed")
        print("3. Test with simple known values")
        print("4. Compare intermediate calculations")
    
    print(f"\n📋 NEXT STEPS:")
    print("1. Focus on the behavioral models (should be identical)")
    print("2. If behavioral models differ, debug implementation")
    print("3. Avoid diode models unless necessary")
    print("4. Use exact parameter conversion methods if needed")
    
    return True

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
