#!/usr/bin/env python3
"""
Quick test for improved HSPICE model
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """Quick test of improved model"""
    try:
        from utils.hspice_interface import HSPICEInterface
        from models.bjt_esd_model import BJTESDModel
        from utils.data_loader import DataLoader
        
        print("Quick HSPICE Model Test")
        print("=" * 30)
        
        # Load and fit data
        if os.path.exists('1.csv'):
            loader = DataLoader()
            voltage_exp, current_exp, _ = loader.load_data('1.csv')
            
            model = BJTESDModel()
            fitted_params = model.fit_parameters(voltage_exp, current_exp)
            
            print(f"Fitted R² = {fitted_params.get('r_squared', 0):.4f}")
            
            # Generate new model
            hspice = HSPICEInterface()
            model_content = hspice.generate_spice_model(fitted_params)
            
            print("Generated improved HSPICE model")
            
            # Show model content
            lines = model_content.split('\n')
            print("\nModel circuit:")
            for line in lines:
                if any(keyword in line for keyword in ['Rlow', 'Dlow', 'Rmid', 'Dmid', 'Rhigh']):
                    print(f"  {line}")
            
            # Save files
            with open('improved_model.ckt', 'w') as f:
                f.write(model_content)
            
            netlist_content = hspice.generate_netlist(fitted_params, (0, 20), 100, 'improved_model.ckt')
            with open('improved_simulation.sp', 'w') as f:
                f.write(netlist_content)
            
            print("✓ Files saved: improved_model.ckt, improved_simulation.sp")
            
            # Test HSPICE if available
            if hspice.verify_hspice_installation():
                print("Testing HSPICE simulation...")
                result = hspice.run_simulation_from_netlist('improved_simulation.sp')
                
                if result:
                    voltage_sim, current_sim = result
                    print(f"✓ Simulation successful: {len(voltage_sim)} points")
                    
                    # Quick comparison
                    import numpy as np
                    voltage_fitted = np.linspace(0, 20, 100)
                    current_fitted = model.current_equation(voltage_fitted, **fitted_params)
                    
                    # Compare at 20V
                    i_20v_fitted = current_fitted[-1]
                    i_20v_sim = current_sim[-1] if len(current_sim) > 0 else 0
                    error = abs(i_20v_sim - i_20v_fitted) / i_20v_fitted * 100 if i_20v_fitted > 0 else 0
                    
                    print(f"At 20V: Fitted={i_20v_fitted:.3f}A, HSPICE={i_20v_sim:.3f}A, Error={error:.1f}%")
                    
                    if error < 20:
                        print("✓ Good matching!")
                    else:
                        print("⚠ Needs improvement")
                    
                    return True
                else:
                    print("✗ Simulation failed")
                    return False
            else:
                print("! HSPICE not available")
                return True
        else:
            print("! No experimental data file")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    print(f"\nResult: {'Success' if success else 'Failed'}")
