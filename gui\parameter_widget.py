"""
Parameter Widget for BJT ESD Parameter Extractor
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QSlider, QLineEdit, QGroupBox, QPushButton,
                             QDoubleSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal
import numpy as np

class ParameterWidget(QWidget):
    """Widget for parameter adjustment with sliders and direct input"""

    parameters_changed = pyqtSignal()
    exact_conversion_requested = pyqtSignal()
    auto_fit_requested = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.parameter_controls = {}
        self.init_ui()

    def init_ui(self):
        """Initialize user interface"""
        layout = QVBoxLayout(self)

        # Create parameter groups
        self.create_leakage_group(layout)
        self.create_trigger_group(layout)
        self.create_snapback_group(layout)

        # Add control buttons
        self.create_control_buttons(layout)

    def create_leakage_group(self, parent_layout):
        """Create leakage current parameters group"""
        group = QGroupBox("Leakage Region Parameters")
        layout = QGridLayout(group)

        # I_leak parameter
        self.add_parameter_control(layout, 0, "I_leak", "Leakage Current (A)",
                                 1e-12, 1e-6, 1e-9, "scientific")

        parent_layout.addWidget(group)

    def create_trigger_group(self, parent_layout):
        """Create trigger region parameters group"""
        group = QGroupBox("Trigger Region Parameters")
        layout = QGridLayout(group)

        # Vt1 parameter
        self.add_parameter_control(layout, 0, "Vt1", "Trigger Voltage (V)",
                                 1.0, 20.0, 12.0, "linear")

        # k parameter
        self.add_parameter_control(layout, 1, "k", "Exponential Factor",
                                 1.0, 10.0, 3.0, "linear")

        parent_layout.addWidget(group)

    def create_snapback_group(self, parent_layout):
        """Create snapback region parameters group"""
        group = QGroupBox("Snapback Region Parameters")
        layout = QGridLayout(group)

        # Ron parameter
        self.add_parameter_control(layout, 0, "Ron", "On Resistance (Ohm)",
                                 0.1, 10.0, 2.0, "linear")

        # Vh parameter
        self.add_parameter_control(layout, 1, "Vh", "Holding Voltage (V)",
                                 10.0, 20.0, 14.0, "linear")

        # I_offset parameter
        self.add_parameter_control(layout, 2, "I_offset", "Current Offset (A)",
                                 0.001, 1.0, 0.05, "linear")

        # Isb parameter
        self.add_parameter_control(layout, 3, "Isb", "Snapback Current (A)",
                                 0.001, 1.0, 0.04, "linear")

        # Vsb parameter
        self.add_parameter_control(layout, 4, "Vsb", "Snapback Voltage (V)",
                                 10.0, 20.0, 14.0, "linear")

        parent_layout.addWidget(group)

    def add_parameter_control(self, layout, row, param_name, label_text,
                            min_val, max_val, default_val, scale_type):
        """Add parameter control with slider and input field"""
        # Label
        label = QLabel(label_text)
        layout.addWidget(label, row, 0)

        # Slider
        slider = QSlider(Qt.Horizontal)
        if scale_type == "scientific":
            # Use log scale for scientific notation
            log_min = np.log10(min_val)
            log_max = np.log10(max_val)
            log_default = np.log10(default_val)
            slider.setMinimum(int(log_min * 100))
            slider.setMaximum(int(log_max * 100))
            slider.setValue(int(log_default * 100))
        else:
            # Linear scale
            slider.setMinimum(int(min_val * 100))
            slider.setMaximum(int(max_val * 100))
            slider.setValue(int(default_val * 100))

        layout.addWidget(slider, row, 1)

        # Input field
        if scale_type == "scientific":
            input_field = QLineEdit(f"{default_val:.3e}")
        else:
            input_field = QLineEdit(f"{default_val:.3f}")
        input_field.setMaximumWidth(120)
        layout.addWidget(input_field, row, 2)

        # Store controls
        self.parameter_controls[param_name] = {
            'slider': slider,
            'input': input_field,
            'min_val': min_val,
            'max_val': max_val,
            'scale_type': scale_type
        }

        # Connect signals
        slider.valueChanged.connect(lambda: self.slider_changed(param_name))
        input_field.editingFinished.connect(lambda: self.input_changed(param_name))

    def slider_changed(self, param_name):
        """Handle slider value change"""
        control = self.parameter_controls[param_name]
        slider_value = control['slider'].value()

        if control['scale_type'] == "scientific":
            # Convert from log scale
            log_value = slider_value / 100.0
            actual_value = 10 ** log_value
            control['input'].setText(f"{actual_value:.3e}")
        else:
            # Linear scale
            actual_value = slider_value / 100.0
            control['input'].setText(f"{actual_value:.3f}")

        self.parameters_changed.emit()

    def input_changed(self, param_name):
        """Handle input field value change"""
        control = self.parameter_controls[param_name]

        try:
            value = float(control['input'].text())

            # Clamp value to valid range
            value = max(control['min_val'], min(control['max_val'], value))

            # Update slider
            if control['scale_type'] == "scientific":
                log_value = np.log10(value)
                slider_value = int(log_value * 100)
                control['input'].setText(f"{value:.3e}")
            else:
                slider_value = int(value * 100)
                control['input'].setText(f"{value:.3f}")

            control['slider'].setValue(slider_value)
            self.parameters_changed.emit()

        except ValueError:
            # Reset to current slider value if invalid input
            self.slider_changed(param_name)

    def create_control_buttons(self, parent_layout):
        """Create control buttons"""
        button_layout = QHBoxLayout()

        # Reset button
        reset_button = QPushButton("Reset to Default")
        reset_button.clicked.connect(self.reset_parameters)
        button_layout.addWidget(reset_button)

        # Auto-fit button (connects to main window)
        autofit_button = QPushButton("Auto-Fit Parameters")
        autofit_button.clicked.connect(self.auto_fit_requested.emit)
        button_layout.addWidget(autofit_button)

        # Exact conversion button
        exact_conversion_button = QPushButton("Exact Conversion")
        exact_conversion_button.clicked.connect(self.exact_conversion_requested.emit)
        button_layout.addWidget(exact_conversion_button)

        button_layout.addStretch()
        parent_layout.addLayout(button_layout)

    def reset_parameters(self):
        """Reset all parameters to default values"""
        defaults = {
            'I_leak': 1e-9,
            'Vt1': 12.0,
            'k': 3.0,
            'Ron': 2.0,
            'Vh': 14.0,
            'I_offset': 0.05,
            'Isb': 0.04,
            'Vsb': 14.0
        }

        self.set_parameters(defaults)



    def set_parameters(self, parameters):
        """Set parameter values from dictionary"""
        for param_name, value in parameters.items():
            if param_name in self.parameter_controls:
                control = self.parameter_controls[param_name]

                # Update input field
                if control['scale_type'] == "scientific":
                    control['input'].setText(f"{value:.3e}")
                else:
                    control['input'].setText(f"{value:.3f}")

                # Update slider
                if control['scale_type'] == "scientific":
                    log_value = np.log10(max(control['min_val'], value))
                    slider_value = int(log_value * 100)
                else:
                    slider_value = int(value * 100)

                control['slider'].setValue(slider_value)

        self.parameters_changed.emit()

    def get_parameters(self):
        """Get current parameter values as dictionary"""
        parameters = {}

        for param_name, control in self.parameter_controls.items():
            try:
                value = float(control['input'].text())
                parameters[param_name] = value
            except ValueError:
                # Use default value if parsing fails
                if param_name == 'I_leak':
                    parameters[param_name] = 1e-9
                elif param_name == 'Vt1':
                    parameters[param_name] = 12.0
                elif param_name == 'k':
                    parameters[param_name] = 3.0
                elif param_name == 'Ron':
                    parameters[param_name] = 2.0
                elif param_name == 'Vh':
                    parameters[param_name] = 14.0
                elif param_name == 'I_offset':
                    parameters[param_name] = 0.05
                elif param_name == 'Isb':
                    parameters[param_name] = 0.04
                elif param_name == 'Vsb':
                    parameters[param_name] = 14.0

        return parameters
