"""
Data Loader for BJT ESD Parameter Extractor
"""

import pandas as pd
import numpy as np
import os

class DataLoader:
    """Class for loading and preprocessing measurement data"""

    def __init__(self):
        pass

    def load_csv(self, file_path):
        """
        Load measurement data from CSV file

        Args:
            file_path (str): Path to CSV file

        Returns:
            dict: Dictionary containing voltage and current arrays
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError("File not found: {}".format(file_path))

        try:
            # Read CSV file manually to handle comments and parameters
            voltage_list = []
            current_list = []

            with open(file_path, "r") as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                # Skip empty lines, comments, and parameter lines
                if not line or line.startswith('#') or ',' not in line:
                    continue

                # Skip header line
                if 'Voltage' in line or 'Current' in line:
                    continue

                # Try to parse voltage and current
                try:
                    parts = line.split(',')
                    if len(parts) >= 2:
                        v = float(parts[0])
                        i = float(parts[1])
                        voltage_list.append(v)
                        current_list.append(i)
                except ValueError:
                    # Skip lines that can't be parsed as numbers
                    continue

            if not voltage_list:
                raise ValueError("No valid data found in CSV file")

            voltage = np.array(voltage_list)
            current = np.array(current_list)

            # Remove any NaN values
            valid_mask = ~(np.isnan(voltage) | np.isnan(current))
            voltage = voltage[valid_mask]
            current = current[valid_mask]

            # Sort by voltage
            sort_indices = np.argsort(voltage)
            voltage = voltage[sort_indices]
            current = current[sort_indices]

            # Convert current to positive values (absolute value)
            current = np.abs(current)

            return {
                'voltage': voltage,
                'current': current,
                'file_path': file_path
            }

        except Exception as e:
            raise Exception("Error loading CSV file: {}".format(str(e)))

    def load_hspice_output(self, file_path):
        """
        Load HSPICE simulation output

        Args:
            file_path (str): Path to HSPICE output file (.lis)

        Returns:
            dict: Dictionary containing voltage and current arrays
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError("File not found: {}".format(file_path))

        try:
            voltage = []
            current = []

            with open(file_path, 'r') as f:
                lines = f.readlines()

            # Find data section in HSPICE output
            data_started = False
            for line in lines:
                line = line.strip()

                # Skip empty lines and comments
                if not line or line.startswith('*') or line.startswith('#'):
                    continue

                # Look for voltage and current data
                if 'v(' in line.lower() and 'i(' in line.lower():
                    data_started = True
                    continue

                if data_started:
                    try:
                        parts = line.split()
                        if len(parts) >= 3:
                            # Assuming format: index voltage current
                            v = float(parts[1])
                            i = abs(float(parts[2]))  # Convert to positive
                            voltage.append(v)
                            current.append(i)
                    except (ValueError, IndexError):
                        continue

            if not voltage:
                raise ValueError("No valid data found in HSPICE output file")

            return {
                'voltage': np.array(voltage),
                'current': np.array(current),
                'file_path': file_path
            }

        except Exception as e:
            raise Exception("Error loading HSPICE output: {}".format(str(e)))

    def preprocess_data(self, data, voltage_range=None, current_threshold=None):
        """
        Preprocess measurement data

        Args:
            data (dict): Data dictionary from load_csv
            voltage_range (tuple): Optional voltage range (min, max) to filter
            current_threshold (float): Optional minimum current threshold

        Returns:
            dict: Preprocessed data dictionary
        """
        voltage = data['voltage'].copy()
        current = data['current'].copy()

        # Apply voltage range filter
        if voltage_range:
            min_v, max_v = voltage_range
            mask = (voltage >= min_v) & (voltage <= max_v)
            voltage = voltage[mask]
            current = current[mask]

        # Apply current threshold filter
        if current_threshold:
            mask = current >= current_threshold
            voltage = voltage[mask]
            current = current[mask]

        # Remove duplicate voltage points (keep first occurrence)
        _, unique_indices = np.unique(voltage, return_index=True)
        voltage = voltage[unique_indices]
        current = current[unique_indices]

        # Sort by voltage again
        sort_indices = np.argsort(voltage)
        voltage = voltage[sort_indices]
        current = current[sort_indices]

        return {
            'voltage': voltage,
            'current': current,
            'file_path': data.get('file_path', '')
        }

    def export_data(self, data, file_path, format='csv'):
        """
        Export data to file

        Args:
            data (dict): Data dictionary
            file_path (str): Output file path
            format (str): Export format ('csv' or 'txt')
        """
        try:
            if format.lower() == 'csv':
                df = pd.DataFrame({
                    'Voltage': data['voltage'],
                    'Current': data['current']
                })
                df.to_csv(file_path, index=False)

            elif format.lower() == 'txt':
                with open(file_path, 'w') as f:
                    f.write("# Voltage (V)\tCurrent (A)\n")
                    for v, i in zip(data['voltage'], data['current']):
                        f.write("{:.6e}\t{:.6e}\n".format(v, i))

            else:
                raise ValueError("Unsupported export format: {}".format(format))

        except Exception as e:
            raise Exception("Error exporting data: {}".format(str(e)))
