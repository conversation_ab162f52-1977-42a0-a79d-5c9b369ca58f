 ****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******                    
  Copyright (c) 1986 - 2025 by Synopsys, Inc. All Rights Reserved.              
  This software and the associated documentation are proprietary
  to Synopsys, Inc. This software may only be used in accordance
  with the terms and conditions of a written license agreement with
  Synopsys, Inc. All other use, reproduction, or distribution of
  this software is strictly prohibited.
  Input File: D:\code\esd\bjt_esd_simulation.sp                                 
  Command line options: C:\synopsys\Hspice_P-2019.06-SP1-1\WIN64\hspice.com D:\code\esd\bjt_esd_simulation.sp -o D:\code\esd\bjt_esd_simulation.lis
  Start time: Mon Jun  9 09:23:40 2025
  lic:  
  lic: FLEXlm: SDK_12.3 
  lic: USER:   e02727               HOSTNAME: ascend27 
  lic: HOSTID: "982cbcdcead7"       PID:      9624 
  lic: Using FLEXlm license file: 
  lic: 27000@ascend27 
  lic: Checkout 1 hspice 
  lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12 
  lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@ascend27 
  lic:   
  **error** (D:\code\esd\bjt_esd_simulation.sp:50) syntax error at or before "{Ron}" 

Syntax reference: 
Rxxx n1 n2 <mname> Rval [TC1 TC2 TC3] [SCALE=val] [M=val]
+ [AC=val] [DTEMP=val] [L=val] [W=val] [C=val]
+ [NOISE=val]
Rxxx n1 n2 mname [R=>resistance TC1=val
+ [<TC2=>val] [<TC=>val] [SCALE=val] [M=val]
+ [AC=val> [DTEMP=val> [L=val] [W=val]
+ [C=val] [NOISE=val]
Rxxx n1 n2 R='equation'


               ***** job aborted
  lic: Release hspice token(s) 
 lic: total license checkout elapse time:        0.02(s)
