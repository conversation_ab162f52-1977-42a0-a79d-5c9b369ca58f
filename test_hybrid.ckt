* BJT ESD Device SPICE Model
* Generated on: 2025-06-06 18:50:59
* Model type: hybrid
* 
* FITTED MODEL PARAMETERS:
* I_leak = 9.317230e-07 A
* Vt1    = 9.083848 V
* k      = 2.000000
* Ron    = 1.140351 Ohm
* Vh     = 14.752984 V
* I_offset = 0.000001 A
* Isb    = 0.569384 A
* Vsb    = 14.831887 V
*
* EXACT PARAMETER TRANSFER: HYBRID model ensures
* mathematical equivalence with fitted model equations.
*

* BJT ESD Hybrid Model - Behavioral + Physical Components
* Combines exact behavioral equations with physical circuit elements

.subckt bjt_esd_hybrid anode cathode

* Parameters
.param I_leak=9.317230e-07
.param Vt1=9.083848
.param k=2.000000
.param Ron=1.140351
.param Vh=14.752984
.param I_offset=0.000001
.param Isb=0.569384
.param Vsb=14.831887

* Main behavioral current source (exact fitted model)
G_main anode n_main cur='
+ if(V(anode,n_main) < 0, 
+   I_leak*1e-3,
+ if(V(anode,n_main) < Vt1,
+   I_leak*exp(V(anode,n_main)/1.0),
+ if(V(anode,n_main) < Vh,
+   I_leak*exp(k*(V(anode,n_main)-Vt1)/Vt1),
+   I_offset + (V(anode,n_main)-Vsb)/Ron + Isb*exp(-(V(anode,n_main)-Vsb))
+ )))'

* Series resistance for realism
R_series n_main cathode {Ron*0.1}

* Parallel capacitance for dynamic behavior
C_parallel anode cathode 1p

.ends bjt_esd_hybrid

* Test circuit
Vin n1 0 DC 0
X_esd n1 0 bjt_esd_hybrid

* Analysis
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.option post=2
.option accurate

.end
