"""
Parameter panel for BJT ESD device parameter adjustment
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QSlider, QLineEdit, QPushButton, QGroupBox,
                            QDoubleSpinBox, QCheckBox, QTabWidget, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QDoubleValidator
import numpy as np
import logging

from config.settings import PARAMETER_RANGES

logger = logging.getLogger(__name__)

class ParameterWidget(QWidget):
    """
    Widget for individual parameter control with slider and input
    """
    
    value_changed = pyqtSignal(str, float)  # parameter_name, value
    
    def __init__(self, name, param_info, initial_value, parent=None):
        super().__init__(parent)
        self.name = name
        self.param_info = param_info
        self.updating = False
        
        self.setup_ui(initial_value)
        self.connect_signals()
    
    def setup_ui(self, initial_value):
        """
        Setup parameter widget UI
        """
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # Parameter name label
        name_label = QLabel(f"{self.name}:")
        name_label.setMinimumWidth(80)
        layout.addWidget(name_label)
        
        # Get parameter range
        param_range = PARAMETER_RANGES.get(self.name, {
            'min': 0.0, 'max': 100.0, 'default': initial_value
        })
        
        self.min_val = param_range['min']
        self.max_val = param_range['max']
        
        # Slider
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setMinimum(0)
        self.slider.setMaximum(1000)
        self.slider.setValue(self.value_to_slider(initial_value))
        layout.addWidget(self.slider)
        
        # Value input
        self.value_input = QLineEdit()
        self.value_input.setMaximumWidth(100)
        self.value_input.setText(f"{initial_value:.6g}")
        
        # Set validator for numeric input
        if self.param_info.get('type') == 'exponential':
            # Allow scientific notation
            self.value_input.setValidator(None)
        else:
            validator = QDoubleValidator(self.min_val, self.max_val, 6)
            self.value_input.setValidator(validator)
        
        layout.addWidget(self.value_input)
        
        # Unit label
        unit = param_range.get('unit', '')
        if unit:
            unit_label = QLabel(unit)
            unit_label.setMinimumWidth(20)
            layout.addWidget(unit_label)
    
    def connect_signals(self):
        """
        Connect widget signals
        """
        self.slider.valueChanged.connect(self.on_slider_changed)
        self.value_input.editingFinished.connect(self.on_input_changed)
    
    def value_to_slider(self, value):
        """
        Convert parameter value to slider position
        """
        if self.param_info.get('type') == 'exponential':
            # Use log scale for exponential parameters
            if value <= 0:
                return 0
            log_min = np.log10(max(self.min_val, 1e-15))
            log_max = np.log10(self.max_val)
            log_val = np.log10(value)
            normalized = (log_val - log_min) / (log_max - log_min)
        else:
            # Linear scale
            normalized = (value - self.min_val) / (self.max_val - self.min_val)
        
        return int(normalized * 1000)
    
    def slider_to_value(self, slider_pos):
        """
        Convert slider position to parameter value
        """
        normalized = slider_pos / 1000.0
        
        if self.param_info.get('type') == 'exponential':
            # Use log scale for exponential parameters
            log_min = np.log10(max(self.min_val, 1e-15))
            log_max = np.log10(self.max_val)
            log_val = log_min + normalized * (log_max - log_min)
            return 10 ** log_val
        else:
            # Linear scale
            return self.min_val + normalized * (self.max_val - self.min_val)
    
    def on_slider_changed(self, value):
        """
        Handle slider value change
        """
        if self.updating:
            return
        
        self.updating = True
        param_value = self.slider_to_value(value)
        self.value_input.setText(f"{param_value:.6g}")
        self.value_changed.emit(self.name, param_value)
        self.updating = False
    
    def on_input_changed(self):
        """
        Handle input value change
        """
        if self.updating:
            return
        
        try:
            self.updating = True
            value_text = self.value_input.text()
            param_value = float(value_text)
            
            # Clamp to valid range
            param_value = max(self.min_val, min(self.max_val, param_value))
            
            # Update slider
            self.slider.setValue(self.value_to_slider(param_value))
            
            # Update input if clamped
            if param_value != float(value_text):
                self.value_input.setText(f"{param_value:.6g}")
            
            self.value_changed.emit(self.name, param_value)
            self.updating = False
            
        except ValueError:
            # Invalid input, revert to slider value
            self.updating = True
            param_value = self.slider_to_value(self.slider.value())
            self.value_input.setText(f"{param_value:.6g}")
            self.updating = False
    
    def set_value(self, value):
        """
        Set parameter value programmatically
        """
        self.updating = True
        self.value_input.setText(f"{value:.6g}")
        self.slider.setValue(self.value_to_slider(value))
        self.updating = False
    
    def get_value(self):
        """
        Get current parameter value
        """
        try:
            return float(self.value_input.text())
        except ValueError:
            return self.slider_to_value(self.slider.value())

class ParameterPanel(QWidget):
    """
    Panel for BJT ESD device parameter adjustment
    """
    
    parameters_changed = pyqtSignal(dict)
    fit_requested = pyqtSignal()
    reset_requested = pyqtSignal()
    
    def __init__(self, model, parent=None):
        super().__init__(parent)
        self.model = model
        self.parameter_widgets = {}
        self.update_timer = QTimer()
        self.update_timer.setSingleShot(True)
        self.update_timer.timeout.connect(self.emit_parameters_changed)
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """
        Setup parameter panel UI
        """
        layout = QVBoxLayout(self)
        
        # Create tab widget
        tab_widget = QTabWidget()
        
        # Parameters tab
        params_tab = QWidget()
        self.setup_parameters_tab(params_tab)
        tab_widget.addTab(params_tab, "Parameters")
        
        # Results tab
        results_tab = QWidget()
        self.setup_results_tab(results_tab)
        tab_widget.addTab(results_tab, "Results")
        
        layout.addWidget(tab_widget)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.fit_button = QPushButton("Fit Parameters")
        self.fit_button.clicked.connect(self.fit_requested.emit)
        button_layout.addWidget(self.fit_button)
        
        self.reset_button = QPushButton("Reset to Default")
        self.reset_button.clicked.connect(self.reset_parameters)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # Real-time update checkbox
        self.realtime_cb = QCheckBox("Real-time Update")
        self.realtime_cb.setChecked(True)
        button_layout.addWidget(self.realtime_cb)
        
        layout.addLayout(button_layout)
    
    def setup_parameters_tab(self, tab_widget):
        """
        Setup parameters tab
        """
        layout = QVBoxLayout(tab_widget)
        
        # Parameter group
        param_group = QGroupBox("Model Parameters")
        param_layout = QVBoxLayout(param_group)
        
        # Get parameter info
        param_info = self.model.get_parameter_info()
        current_params = self.model.get_parameters()
        
        # Create parameter widgets
        for param_name, value in current_params.items():
            info = param_info.get(param_name, {})
            widget = ParameterWidget(param_name, info, value)
            widget.value_changed.connect(self.on_parameter_changed)
            
            self.parameter_widgets[param_name] = widget
            param_layout.addWidget(widget)
        
        layout.addWidget(param_group)
        layout.addStretch()
    
    def setup_results_tab(self, tab_widget):
        """
        Setup results tab
        """
        layout = QVBoxLayout(tab_widget)
        
        # Fitting results group
        results_group = QGroupBox("Fitting Results")
        results_layout = QVBoxLayout(results_group)
        
        # R-squared display
        self.r_squared_label = QLabel("R² = N/A")
        font = QFont()
        font.setBold(True)
        self.r_squared_label.setFont(font)
        results_layout.addWidget(self.r_squared_label)
        
        # Parameter values display
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(150)
        self.results_text.setReadOnly(True)
        results_layout.addWidget(self.results_text)
        
        layout.addWidget(results_group)
        
        # Statistics group
        stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(100)
        self.stats_text.setReadOnly(True)
        stats_layout.addWidget(self.stats_text)
        
        layout.addWidget(stats_group)
        layout.addStretch()
    
    def connect_signals(self):
        """
        Connect signals
        """
        pass
    
    def on_parameter_changed(self, param_name, value):
        """
        Handle parameter value change
        """
        if self.realtime_cb.isChecked():
            # Debounce updates
            self.update_timer.start(100)  # 100ms delay
    
    def emit_parameters_changed(self):
        """
        Emit parameters changed signal
        """
        parameters = self.get_current_parameters()
        self.parameters_changed.emit(parameters)
    
    def get_current_parameters(self):
        """
        Get current parameter values from widgets
        """
        parameters = {}
        for name, widget in self.parameter_widgets.items():
            parameters[name] = widget.get_value()
        return parameters
    
    def update_parameter_values(self, parameters):
        """
        Update parameter widget values
        """
        for name, value in parameters.items():
            if name in self.parameter_widgets:
                self.parameter_widgets[name].set_value(value)
        
        # Update results display
        self.update_results_display(parameters)
    
    def update_results_display(self, parameters):
        """
        Update results display
        """
        # Update R-squared
        r_squared = parameters.get('r_squared', None)
        if r_squared is not None:
            self.r_squared_label.setText(f"R² = {r_squared:.6f}")
        else:
            self.r_squared_label.setText("R² = N/A")
        
        # Update parameter values text
        results_text = "Fitted Parameters:\n"
        param_info = self.model.get_parameter_info()
        
        for name, value in parameters.items():
            if name != 'r_squared':
                info = param_info.get(name, {})
                unit = PARAMETER_RANGES.get(name, {}).get('unit', '')
                description = info.get('description', name)
                results_text += f"{description}: {value:.6g} {unit}\n"
        
        self.results_text.setPlainText(results_text)
    
    def reset_parameters(self):
        """
        Reset parameters to default values
        """
        default_params = {}
        for name in self.parameter_widgets.keys():
            default_value = PARAMETER_RANGES.get(name, {}).get('default', 1.0)
            default_params[name] = default_value
        
        self.update_parameter_values(default_params)
        self.reset_requested.emit()
    
    def update_parameters_from_metadata(self, metadata):
        """
        Update parameters from loaded metadata
        """
        for name, value in metadata.items():
            if name in self.parameter_widgets and isinstance(value, (int, float)):
                self.parameter_widgets[name].set_value(value)
        
        # Emit change signal
        if self.realtime_cb.isChecked():
            self.emit_parameters_changed()
