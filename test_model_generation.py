#!/usr/bin/env python3
"""
Test model generation to see what's actually being generated
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_model_generation():
    """Test what model is actually being generated"""
    try:
        from utils.hspice_interface import HSPICEInterface
        
        # Use sample parameters
        test_params = {
            'I_leak': 9.161209e-11,
            'Vt1': 3.841,
            'k': 7.812,
            'Ron': 1.213,
            'Vh': 14.011,
            'I_offset': 0.152575,
            'Isb': 0.292588,
            'Vsb': 14.661
        }
        
        print("Testing HSPICE model generation...")
        print("Parameters:")
        for key, value in test_params.items():
            print(f"  {key}: {value}")
        
        hspice = HSPICEInterface()
        model_content = hspice.generate_spice_model(test_params)
        
        # Save the model
        with open('test_generated_model.ckt', 'w') as f:
            f.write(model_content)
        
        print(f"\nGenerated model saved to: test_generated_model.ckt")
        print(f"Model size: {len(model_content)} characters")
        
        # Show the circuit part
        lines = model_content.split('\n')
        print("\nCircuit definition lines:")
        in_circuit = False
        for i, line in enumerate(lines):
            if '.subckt bjt_esd_device' in line:
                in_circuit = True
            if in_circuit:
                print(f"  {i+1}: {line}")
                if '.ends' in line:
                    break
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_generation()
    if success:
        print("\n✓ Model generation test completed")
    else:
        print("\n✗ Model generation test failed")
        sys.exit(1)
