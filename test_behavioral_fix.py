#!/usr/bin/env python3
"""
Test Behavioral Fix for HSPICE Model
Generate and test the behavioral model with exact equations
"""

import sys
import os
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_behavioral_model():
    """Test the behavioral model generation and simulation"""
    print("TESTING BEHAVIORAL MODEL")
    print("=" * 40)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.hspice_compatible_generator import HspiceCompatibleGenerator
        
        # Load data and extract parameters
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        print("✓ Parameters loaded:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"    {key}: {value:.6e}")
            else:
                print(f"    {key}: {value:.6f}")
        
        # Generate behavioral model
        generator = HspiceCompatibleGenerator()
        generator.set_model_type("pwl")  # Still using pwl name but now behavioral
        
        filename = "bjt_esd_behavioral.ckt"
        content = generator.generate_hspice_model(parameters, filename)
        
        print(f"\n✓ Behavioral model generated: {filename}")
        print(f"✓ Content length: {len(content)} characters")
        
        # Check the generated content
        lines = content.split('\n')
        
        has_behavioral = False
        has_subckt = False
        
        for i, line in enumerate(lines):
            line_clean = line.strip()
            
            if 'G_esd' in line_clean and 'CUR=' in line_clean:
                has_behavioral = True
                print(f"  ✓ Found behavioral syntax at line {i+1}")
                print(f"    {line_clean}")
                
            if '.subckt bjt_esd_pwl' in line_clean:
                has_subckt = True
                print(f"  ✓ Found subckt definition at line {i+1}")
        
        print(f"\nSyntax verification:")
        print(f"  Behavioral syntax: {'✓' if has_behavioral else '❌'}")
        print(f"  Subckt definition: {'✓' if has_subckt else '❌'}")
        
        return True, filename
        
    except Exception as e:
        print(f"❌ Behavioral model generation failed: {e}")
        return False, None

def test_hspice_simulation(filename):
    """Test HSPICE simulation with behavioral model"""
    print(f"\nTESTING HSPICE SIMULATION")
    print("=" * 40)
    
    try:
        from simulation.hspice_runner import HspiceRunner
        
        # Check HSPICE installation
        runner = HspiceRunner()
        if not runner.check_hspice_installation():
            print("❌ HSPICE not available - skipping simulation test")
            return False, None
        
        print("✓ HSPICE installation detected")
        
        # Run simulation
        if not os.path.exists(filename):
            print(f"❌ Model file not found: {filename}")
            return False, None
        
        import subprocess
        output_file = filename.replace('.ckt', '_results.lis')
        cmd = [runner.hspice_path, filename, "-o", output_file]
        
        print(f"Running command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              cwd=os.getcwd(), timeout=30)
        
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ HSPICE simulation completed successfully!")
            
            # Check if output file exists and parse results
            if os.path.exists(output_file):
                print(f"✓ Output file created: {output_file}")
                
                # Try to parse results
                sim_results = runner.parse_hspice_output(output_file)
                if sim_results:
                    voltage = sim_results['voltage']
                    current = sim_results['current']
                    print(f"✓ Parsed {len(voltage)} data points")
                    print(f"  Voltage range: {voltage.min():.2f}V to {voltage.max():.2f}V")
                    print(f"  Current range: {current.min():.2e}A to {current.max():.2e}A")
                    
                    return True, sim_results
                else:
                    print("❌ Could not parse simulation results")
                    return False, None
            else:
                print(f"❌ Output file not created: {output_file}")
                return False, None
        else:
            print("❌ HSPICE simulation failed")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            
            # Save error log
            error_file = "hspice_behavioral_error.log"
            with open(error_file, 'w') as f:
                f.write("HSPICE Behavioral Model Error Log\n")
                f.write("=" * 40 + "\n")
                f.write(f"Command: {' '.join(cmd)}\n")
                f.write(f"Return code: {result.returncode}\n\n")
                f.write("STDOUT:\n")
                f.write(result.stdout)
                f.write("\n\nSTDERR:\n")
                f.write(result.stderr)
            print(f"Error log saved: {error_file}")
            
            return False, None
            
    except Exception as e:
        print(f"❌ HSPICE simulation error: {e}")
        return False, None

def compare_with_fitted(sim_results):
    """Compare HSPICE results with fitted model"""
    print(f"\nCOMPARING WITH FITTED MODEL")
    print("=" * 40)
    
    try:
        # Load fitted model
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        # Get HSPICE results
        hspice_v = sim_results['voltage']
        hspice_i = sim_results['current']
        
        # Calculate fitted model at HSPICE voltage points
        fitted_i = extractor.calculate_model_current(hspice_v, parameters)
        
        print(f"✓ Comparison data:")
        print(f"  HSPICE points: {len(hspice_v)}")
        print(f"  Voltage range: {hspice_v.min():.2f}V to {hspice_v.max():.2f}V")
        
        # Calculate errors
        abs_error = np.abs(hspice_i - fitted_i)
        rel_error = abs_error / (fitted_i + 1e-15) * 100
        
        print(f"  Max absolute error: {np.max(abs_error):.2e} A")
        print(f"  Max relative error: {np.max(rel_error):.1f} %")
        print(f"  Average relative error: {np.mean(rel_error):.1f} %")
        
        # Check specific points
        test_voltages = [1, 5, 10, 15, 20]
        print(f"\nPoint-by-point comparison:")
        print(f"{'V':>3} | {'HSPICE':>12} | {'Fitted':>12} | {'Error %':>8} | {'Status':>8}")
        print("-" * 65)
        
        good_matches = 0
        total_tests = 0
        
        for test_v in test_voltages:
            if test_v <= np.max(hspice_v):
                idx = np.argmin(np.abs(hspice_v - test_v))
                v_actual = hspice_v[idx]
                i_hspice = hspice_i[idx]
                i_fitted = fitted_i[idx]
                
                error_pct = abs(i_hspice - i_fitted) / (i_fitted + 1e-15) * 100
                
                if error_pct < 5:  # Less than 5% error
                    status = "✅ GOOD"
                    good_matches += 1
                elif error_pct < 20:  # Less than 20% error
                    status = "⚠️  OK"
                else:
                    status = "❌ POOR"
                
                total_tests += 1
                
                print(f"{v_actual:3.0f} | {i_hspice:12.3e} | {i_fitted:12.3e} | {error_pct:8.1f} | {status:>8}")
        
        # Overall assessment
        match_percentage = (good_matches / total_tests * 100) if total_tests > 0 else 0
        
        if np.mean(rel_error) < 5 and match_percentage >= 80:
            print("\n🎉 EXCELLENT: Behavioral model matches fitted model very well!")
            return True
        elif np.mean(rel_error) < 20 and match_percentage >= 60:
            print("\n✅ GOOD: Behavioral model matches fitted model reasonably well")
            return True
        else:
            print("\n⚠️  ACCEPTABLE: Some differences remain")
            return False
            
    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        return False

def main():
    """Main test function"""
    print("BEHAVIORAL MODEL TEST")
    print("=" * 50)
    print("Testing behavioral model with exact equations")
    
    # Test 1: Generate behavioral model
    gen_ok, filename = test_behavioral_model()
    
    if not gen_ok:
        print("\n❌ Behavioral model generation failed")
        return False
    
    # Test 2: Run HSPICE simulation
    sim_ok, sim_results = test_hspice_simulation(filename)
    
    # Test 3: Compare with fitted model
    comparison_ok = False
    if sim_ok and sim_results:
        comparison_ok = compare_with_fitted(sim_results)
    
    # Final summary
    print("\n" + "=" * 50)
    print("BEHAVIORAL MODEL TEST SUMMARY")
    print("=" * 50)
    
    print(f"Model generation: {'✅ PASS' if gen_ok else '❌ FAIL'}")
    print(f"HSPICE simulation: {'✅ PASS' if sim_ok else '❌ FAIL'}")
    print(f"Model comparison: {'✅ PASS' if comparison_ok else '❌ FAIL'}")
    
    overall_success = gen_ok and sim_ok and comparison_ok
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Behavioral model provides exact matching")
        print("✅ HSPICE compatibility issue is RESOLVED")
        print("✅ Green line should now overlap with red line in GUI")
        
        print(f"\n📋 NEXT STEPS:")
        print("1. Use GUI: File → Save HSPICE Compatible Model...")
        print("2. Select 'pwl' model type (now uses behavioral equations)")
        print("3. Run HSPICE simulation")
        print("4. Verify results match fitted model perfectly")
        
    else:
        print("\n❌ SOME TESTS FAILED")
        print("❌ Further debugging may be needed")
        
        if not gen_ok:
            print("• Fix behavioral model generation")
        if not sim_ok:
            print("• Fix HSPICE simulation issues")
        if not comparison_ok:
            print("• Fix model accuracy issues")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
