* BJT ESD Device HSPICE Model
* Generated: 2025-06-09 09:29:04
* Model type: pwl
* HSPICE Compatible - Fixed syntax issues
*
* FITTED MODEL PARAMETERS:
* I_leak = 9.317230e-07 A
* Vt1    = 9.083848 V
* k      = 2.000000
* Ron    = 1.140351 Ohm
* Vh     = 14.752984 V
* I_offset = 0.000001 A
* Isb    = 0.569384 A
* Vsb    = 14.831887 V
*
* USAGE: This model uses HSPICE-compatible syntax
* No behavioral expressions that might cause syntax errors
*


* BJT ESD PWL Model - HSPICE Compatible
* Exact I-V curve matching using behavioral model

.subckt bjt_esd_pwl anode cathode

* Behavioral current source with exact I-V curve
G_esd anode cathode CUR='
+ if(V(anode,cathode) < 9.083848,
+   9.317230e-07 * exp(V(anode,cathode) / 1.0),
+ if(V(anode,cathode) < 14.752984,
+   9.317230e-07 * exp(2.000000 * (V(anode,cathode) - 9.083848) / 9.083848),
+   1.000000e-06 + max(0, (V(anode,cathode) - 14.831887) / 1.140351) + 5.693839e-01 * exp(-(V(anode,cathode) - 14.831887))
+ ))'

.ends bjt_esd_pwl


* Test circuit
Vin n_anode 0 DC 0
X_esd n_anode 0 bjt_esd_pwl

* Analysis
.dc Vin 0 20 0.1
.print dc V(n_anode) I(Vin)
.probe dc V(n_anode) I(Vin)

* HSPICE options for better convergence
.option post=2
.option accurate
.option gmin=1e-15
.option abstol=1e-15
.option reltol=1e-6
.option itl1=500
.option itl2=200

.end
