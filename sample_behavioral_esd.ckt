* Behavioral ESD Model
* Sample file for demonstration

.subckt behavioral_esd anode cathode
.param I_leak=1e-9
.param Vt1=12.0
.param k=3.0

G_esd anode cathode cur='if(V(anode,cathode) < Vt1, I_leak*exp(V(anode,cathode)/1.0), I_leak*exp(k*(V(anode,cathode)-Vt1)/Vt1))'
R_parallel anode cathode 2.0

.ends behavioral_esd

* Test circuit
Vin n1 0 DC 0
X_esd n1 0 behavioral_esd
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.option post=2
.end
