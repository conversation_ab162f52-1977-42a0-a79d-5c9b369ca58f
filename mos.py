import subprocess
import re
import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import tkinter as tk
from tkinter import filedialog, messagebox

import os

def run_hspice_simulation(netlist_path):
    """运行HSPICE仿真并返回输出文件路径"""
    # 获取文件名（不带路径）和输出文件名
    netlist_filename = os.path.basename(netlist_path)  # 提取文件名，例如 mos.sp
    output_file = netlist_filename.replace('.sp', '.lis')  # 输出文件名，例如 mos.lis
    
    # 确保当前工作目录是 netlist_path 所在的目录
    original_dir = os.getcwd()  # 保存当前工作目录
    netlist_dir = os.path.dirname(netlist_path)  # 获取 netlist_path 的目录
    os.chdir(netlist_dir)  # 切换到 netlist_path 的目录
    
    # 使用相对路径运行 HSPICE
    cmd = f'hspice {netlist_filename} -o {output_file}'
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            check=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        print("HSPICE 标准输出：", result.stdout)
        print("HSPICE 错误输出：", result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"HSPICE 仿真失败: {e}")
        print(f"HSPICE 标准输出: {e.stdout}")
        print(f"HSPICE 错误输出: {e.stderr}")
        error_log = netlist_filename.replace('.sp', '.st0')
        if os.path.exists(error_log):
            with open(error_log, 'r', encoding='utf-8', errors='ignore') as f:
                print("HSPICE 错误日志 (.st0 文件):", f.read())
        raise
    finally:
        os.chdir(original_dir)  # 恢复原始工作目录
    
    # 返回输出文件的完整路径
    return os.path.join(netlist_dir, output_file)

def parse_lis_file(lis_file):
    """解析.lis文件，提取所有扫描数据"""
    with open(lis_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # 提取Vg值
    vg_pattern = r"\*\*\* parameter 0:vg\s*=\s*([\d\.eE+-]+)\s*\*\*\*"
    vg_matches = re.findall(vg_pattern, content)
    vg_values = [float(vg) for vg in vg_matches]
    print(f"找到 {len(vg_values)} 个 Vg 值: {vg_values}")
    
    # 提取数据块
    block_pattern = r'x\n\n\s*volt\s+current\s*\n\s*(\w+)\s*\n([\s\S]*?)\n\s*y'
    blocks = re.findall(block_pattern, content)
    print(f"找到 {len(blocks)} 个数据块，标签为: {[block[0] for block in blocks]}")
    
    # 检查数据块数量是否是4的倍数
    if not blocks or len(blocks) % 4 != 0:
        raise ValueError(f"数据块数量不是4的倍数: 实际 {len(blocks)} 块，预期为4的倍数")
    
    # 计算实际可以处理的Vg数量（基于数据块数量）
    blocks_per_vg = 4
    num_vg_with_data = len(blocks) // blocks_per_vg
    if num_vg_with_data > len(vg_values):
        raise ValueError(f"数据块数量过多: 实际 {len(blocks)} 块，Vg 数量 {len(vg_values)}")
    elif num_vg_with_data < len(vg_values):
        print(f"警告: 只有 {num_vg_with_data} 个 Vg 值有完整数据块，将忽略多余的 Vg 值")
        vg_values = vg_values[:num_vg_with_data]  # 只处理有数据的 Vg
    
    results = {}
    for i, vg in enumerate(vg_values):
        # 每个Vg有四个数据块: vb, vds, vg, vs
        vb_block = blocks[i*blocks_per_vg]
        vds_block = blocks[i*blocks_per_vg+1]
        vg_block = blocks[i*blocks_per_vg+2]
        vs_block = blocks[i*blocks_per_vg+3]
        
        # 解析vb数据
        if vb_block[0] != 'vb':
            raise ValueError(f"预期vb数据块，但找到: {vb_block[0]}")
        vb_data = parse_data_block(vb_block[1])
        
        # 解析vds数据
        if vds_block[0] != 'vds':
            raise ValueError(f"预期vds数据块，但找到: {vds_block[0]}")
        vds_data = parse_data_block(vds_block[1])
        
        # 解析vg数据
        if vg_block[0] != 'vg':
            raise ValueError(f"预期vg数据块，但找到: {vg_block[0]}")
        vg_data = parse_data_block(vg_block[1])
        
        # 解析vs数据
        if vs_block[0] != 'vs':
            raise ValueError(f"预期vs数据块，但找到: {vs_block[0]}")
        vs_data = parse_data_block(vs_block[1])
        
        # 提取电压和电流
        vds = np.array([point[0] for point in vb_data])
        ib = np.array([point[1] for point in vb_data])
        id_vals = -np.array([point[1] for point in vds_data])  # 取负值得到漏电流
        ig = np.array([point[1] for point in vg_data])
        is_vals = np.array([point[1] for point in vs_data])  # 使用vs数据作为源电流
        
        # 验证源电流 (可选，检查是否满足 Is = -Ib - Id - Ig)
        calculated_is = -ib - id_vals - ig
        if not np.allclose(is_vals, calculated_is, rtol=1e-5, atol=1e-8):
            print(f"警告: Vg={vg} 的源电流与计算值不一致，可能存在数据问题")
        
        # 计算ib/id百分比（避免除以零）
        ratio = np.zeros_like(id_vals)
        nonzero_idx = np.abs(id_vals) > 1e-20
        ratio[nonzero_idx] = 100 * np.abs(ib[nonzero_idx]) / np.abs(id_vals[nonzero_idx])
        
        results[vg] = {
            'vds': vds,
            'ib': ib,
            'id': id_vals,
            'is': is_vals,
            'ig': ig,
            'ratio': ratio
        }
    
    return results

def parse_data_block(data_str):
    """解析单个数据块"""
    lines = data_str.strip().split('\n')
    data = []
    for line in lines:
        line = line.strip()
        if not line:
            continue
        # 处理科学计数法中的负指数
        line = re.sub(r'(\d)(-)(\d)', r'\1e-\3', line)
        parts = line.split()
        try:
            if len(parts) >= 2:
                v = float(parts[0])
                i_val = float(parts[1])
                data.append([v, i_val])
        except ValueError:
            continue
    return data

def plot_results(results):
    """创建带有4个子图的图表"""
    # 创建Tkinter窗口
    root = tk.Tk()
    root.title("HSPICE simualtion results")
    root.geometry("1200x900")
    
    # 创建图形和子图
    fig, axs = plt.subplots(4, 1, figsize=(12, 10))
    fig.subplots_adjust(hspace=0.4)
    
    # 设置颜色和标记样式
    colors = ['b', 'r', 'g', 'm', 'c', 'y']
    markers = ['o', 's', '^', 'd', 'v', '<']
    linestyles = ['-', '--', '-.', ':']
    
    # 绘制每个Vg的数据
    for i, (vg, data) in enumerate(results.items()):
        color = colors[i % len(colors)]
        marker = markers[i % len(markers)]
        linestyle = linestyles[i % len(linestyles)]
        label = f'Vg={vg}V'
        
        # 子图1: Ib (体电流) - 对数坐标
        axs[0].semilogy(data['vds'], np.abs(data['ib']), 
                       marker=marker, markersize=4, markevery=5,
                       linestyle=linestyle, linewidth=1.5,
                       color=color, label=label)
        
        # 子图2: Id (漏电流)
        axs[1].plot(data['vds'], np.abs(data['id']), 
                   marker=marker, markersize=4, markevery=5,
                   linestyle=linestyle, linewidth=1.5,
                   color=color, label=label)
        
        # 子图3: Is (源电流)
        axs[2].plot(data['vds'], np.abs(data['is']), 
                   marker=marker, markersize=4, markevery=5,
                   linestyle=linestyle, linewidth=1.5,
                   color=color, label=label)
        
        # 子图4: Ib/Id 百分比
        axs[3].plot(data['vds'], data['ratio'], 
                   marker=marker, markersize=4, markevery=5,
                   linestyle=linestyle, linewidth=1.5,
                   color=color, label=label)
    
    # 设置子图1: Ib
    axs[0].set_title('Ib')
    axs[0].set_ylabel('|Ib| (A)')
    axs[0].set_yscale('log')
    axs[0].grid(True, which="both", ls="-", alpha=0.3)
    axs[0].legend(loc='best', fontsize=9)
    
    # 设置子图2: Id
    axs[1].set_title('Id')
    axs[1].set_ylabel('|Id| (A)')
    axs[1].grid(True, alpha=0.3)
    axs[1].legend(loc='best', fontsize=9)
    
    # 设置子图3: Is
    axs[2].set_title('Is')
    axs[2].set_ylabel('|Is| (A)')
    axs[2].grid(True, alpha=0.3)
    axs[2].legend(loc='best', fontsize=9)
    
    # 设置子图4: Ib/Id 百分比
    axs[3].set_title('Ib/Id')
    axs[3].set_xlabel('Vds (V)')
    axs[3].set_ylabel('%')
    axs[3].grid(True, alpha=0.3)
    axs[3].legend(loc='best', fontsize=9)
    
    # 添加导航工具栏
    canvas = FigureCanvasTkAgg(fig, master=root)
    canvas.draw()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
    
    toolbar = NavigationToolbar2Tk(canvas, root)
    toolbar.update()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
    
    # 添加保存按钮
    def save_figure():
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG", "*.png"), ("PDF", "*.pdf"), ("SVG", "*.svg"), ("All Files", "*.*")]
        )
        if file_path:
            fig.savefig(file_path, dpi=300, bbox_inches='tight')
            messagebox.showinfo("保存成功", f"图表已保存至:\n{file_path}")
    
    save_btn = tk.Button(root, text="保存图表", command=save_figure, bg='#4CAF50', fg='white')
    save_btn.pack(side=tk.BOTTOM, pady=10)
    
    # 添加关闭按钮
    close_btn = tk.Button(root, text="关闭", command=root.destroy, bg='#f44336', fg='white')
    close_btn.pack(side=tk.BOTTOM, pady=5)
    
    root.mainloop()

def main():
    """主函数：选择网表文件并执行仿真分析"""
    # 创建文件选择对话框
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 询问是否运行新仿真或使用现有结果
    choice = messagebox.askquestion("选择操作", "是否运行新的HSPICE仿真？\n选择'No'将使用现有.lis文件")
    
    if choice == 'yes':
        netlist_path = filedialog.askopenfilename(
            title="选择HSPICE网表文件",
            filetypes=[("SPICE Files", "*.sp"), ("All Files", "*.*")]
        )
        if not netlist_path:
            print("未选择文件，程序退出")
            return
        
        try:
            print("正在运行HSPICE仿真...")
            lis_file = run_hspice_simulation(netlist_path)
            print(f"仿真完成，结果文件: {lis_file}")
        except subprocess.CalledProcessError as e:
            messagebox.showerror("仿真错误", f"HSPICE仿真失败: {e}")
            return
    else:
        lis_file = filedialog.askopenfilename(
            title="选择HSPICE结果文件",
            filetypes=[("HSPICE Output", "*.lis"), ("All Files", "*.*")]
        )
        if not lis_file:
            print("未选择文件，程序退出")
            return
    
    # 解析结果文件
    try:
        print("解析结果文件...")
        results = parse_lis_file(lis_file)
        
        if not results:
            messagebox.showerror("解析错误", "未找到有效数据，请检查.lis文件格式")
            return
        
        print(f"成功解析 {len(results)} 组数据")
        print("绘制图表...")
        plot_results(results)
        
    except Exception as e:
        messagebox.showerror("错误", f"处理数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()