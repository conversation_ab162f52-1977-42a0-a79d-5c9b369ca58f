import subprocess
import re
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import tkinter as tk
from tkinter import filedialog, messagebox

def run_hspice_simulation(netlist_path):
    """运行HSPICE仿真并返回输出文件路径"""
    output_file = netlist_path.replace('.sp', '.lis')
    cmd = f'hspice {netlist_path} -o {output_file}'
    result = subprocess.run(cmd, shell=True, check=True)
    return output_file

def parse_lis_file(lis_file):
    """解析.lis文件，提取所有扫描数据"""
    with open(lis_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # 使用正则表达式提取不同Vg值的数据块
    vg_pattern = r"\*\*\* parameter 0:vg\s*=\s*([\d\.eE+-]+)\s*\*\*\*"
    vg_matches = re.findall(vg_pattern, content)
    vg_values = [float(vg) for vg in vg_matches]
    
    # 提取所有数据块 (每个Vg对应三个数据块: vb, vds, vg)
    block_pattern = r'x\n\n\s*volt\s+current\s*\n\s*(\w+)\s*\n([\s\S]*?)\n\s*y'
    blocks = re.findall(block_pattern, content)
    
    if not blocks or len(blocks) != len(vg_values) * 3:
        raise ValueError("数据块数量与Vg参数数量不匹配")
    
    results = {}
    for i, vg in enumerate(vg_values):
        # 每个Vg有三个数据块: vb, vds, vg
        vb_block = blocks[i*3]
        vds_block = blocks[i*3+1]
        vg_block = blocks[i*3+2]
        
        # 解析vb数据
        if vb_block[0] != 'vb':
            raise ValueError(f"预期vb数据块，但找到: {vb_block[0]}")
        vb_data = parse_data_block(vb_block[1])
        
        # 解析vds数据
        if vds_block[0] != 'vds':
            raise ValueError(f"预期vds数据块，但找到: {vds_block[0]}")
        vds_data = parse_data_block(vds_block[1])
        
        # 解析vg数据
        if vg_block[0] != 'vg':
            raise ValueError(f"预期vg数据块，但找到: {vg_block[0]}")
        vg_data = parse_data_block(vg_block[1])
        
        # 提取电压和电流
        vds = np.array([point[0] for point in vb_data])
        ib = np.array([point[1] for point in vb_data])
        id_vals = -np.array([point[1] for point in vds_data])  # 取负值得到漏电流
        ig = np.array([point[1] for point in vg_data])
        
        # 计算源电流 (Is = -Ib - Id - Ig)
        is_vals = -ib - id_vals - ig
        
        # 计算ib/id百分比（避免除以零）
        ratio = np.zeros_like(id_vals)
        nonzero_idx = np.abs(id_vals) > 1e-20
        ratio[nonzero_idx] = 100 * np.abs(ib[nonzero_idx]) / np.abs(id_vals[nonzero_idx])
        
        results[vg] = {
            'vds': vds,
            'ib': ib,
            'id': id_vals,
            'is': is_vals,
            'ig': ig,
            'ratio': ratio
        }
    
    return results

def parse_data_block(data_str):
    """解析单个数据块"""
    lines = data_str.strip().split('\n')
    data = []
    for line in lines:
        line = line.strip()
        if not line:
            continue
        # 处理科学计数法中的负指数
        line = re.sub(r'(\d)(-)(\d)', r'\1e-\3', line)
        parts = line.split()
        try:
            if len(parts) >= 2:
                v = float(parts[0])
                i_val = float(parts[1])
                data.append([v, i_val])
        except ValueError:
            continue
    return data

def plot_results(results):
    """创建带有4个子图的图表"""
    # 创建Tkinter窗口
    root = tk.Tk()
    root.title("HSPICE仿真结果分析")
    root.geometry("1200x900")
    
    # 创建图形和子图
    fig, axs = plt.subplots(4, 1, figsize=(12, 10))
    fig.subplots_adjust(hspace=0.4)
    
    # 设置颜色和标记样式
    colors = ['b', 'r', 'g', 'm', 'c', 'y']
    markers = ['o', 's', '^', 'd', 'v', '<']
    linestyles = ['-', '--', '-.', ':']
    
    # 绘制每个Vg的数据
    for i, (vg, data) in enumerate(results.items()):
        color = colors[i % len(colors)]
        marker = markers[i % len(markers)]
        linestyle = linestyles[i % len(linestyles)]
        label = f'Vg={vg}V'
        
        # 子图1: Ib (体电流) - 对数坐标
        axs[0].semilogy(data['vds'], np.abs(data['ib']), 
                       marker=marker, markersize=4, markevery=5,
                       linestyle=linestyle, linewidth=1.5,
                       color=color, label=label)
        
        # 子图2: Id (漏电流)
        axs[1].plot(data['vds'], np.abs(data['id']), 
                   marker=marker, markersize=4, markevery=5,
                   linestyle=linestyle, linewidth=1.5,
                   color=color, label=label)
        
        # 子图3: Is (源电流)
        axs[2].plot(data['vds'], np.abs(data['is']), 
                   marker=marker, markersize=4, markevery=5,
                   linestyle=linestyle, linewidth=1.5,
                   color=color, label=label)
        
        # 子图4: Ib/Id 百分比
        axs[3].plot(data['vds'], data['ratio'], 
                   marker=marker, markersize=4, markevery=5,
                   linestyle=linestyle, linewidth=1.5,
                   color=color, label=label)
    
    # 设置子图1: Ib
    axs[0].set_title('体电流 (Ib)')
    axs[0].set_ylabel('|Ib| (A)')
    axs[0].set_yscale('log')
    axs[0].grid(True, which="both", ls="-", alpha=0.3)
    axs[0].legend(loc='best', fontsize=9)
    
    # 设置子图2: Id
    axs[1].set_title('漏电流 (Id)')
    axs[1].set_ylabel('|Id| (A)')
    axs[1].grid(True, alpha=0.3)
    axs[1].legend(loc='best', fontsize=9)
    
    # 设置子图3: Is
    axs[2].set_title('源电流 (Is)')
    axs[2].set_ylabel('|Is| (A)')
    axs[2].grid(True, alpha=0.3)
    axs[2].legend(loc='best', fontsize=9)
    
    # 设置子图4: Ib/Id 百分比
    axs[3].set_title('Ib/Id 百分比')
    axs[3].set_xlabel('Vds (V)')
    axs[3].set_ylabel('百分比 (%)')
    axs[3].grid(True, alpha=0.3)
    axs[3].legend(loc='best', fontsize=9)
    
    # 添加导航工具栏
    canvas = FigureCanvasTkAgg(fig, master=root)
    canvas.draw()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
    
    toolbar = NavigationToolbar2Tk(canvas, root)
    toolbar.update()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
    
    # 添加保存按钮
    def save_figure():
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG", "*.png"), ("PDF", "*.pdf"), ("SVG", "*.svg"), ("All Files", "*.*")]
        )
        if file_path:
            fig.savefig(file_path, dpi=300, bbox_inches='tight')
            messagebox.showinfo("保存成功", f"图表已保存至:\n{file_path}")
    
    save_btn = tk.Button(root, text="保存图表", command=save_figure, bg='#4CAF50', fg='white')
    save_btn.pack(side=tk.BOTTOM, pady=10)
    
    # 添加关闭按钮
    close_btn = tk.Button(root, text="关闭", command=root.destroy, bg='#f44336', fg='white')
    close_btn.pack(side=tk.BOTTOM, pady=5)
    
    root.mainloop()

def main():
    """主函数：选择网表文件并执行仿真分析"""
    # 创建文件选择对话框
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 询问是否运行新仿真或使用现有结果
    choice = messagebox.askquestion("选择操作", "是否运行新的HSPICE仿真？\n选择'No'将使用现有.lis文件")
    
    if choice == 'yes':
        netlist_path = filedialog.askopenfilename(
            title="选择HSPICE网表文件",
            filetypes=[("SPICE Files", "*.sp"), ("All Files", "*.*")]
        )
        if not netlist_path:
            print("未选择文件，程序退出")
            return
        
        try:
            print("正在运行HSPICE仿真...")
            lis_file = run_hspice_simulation(netlist_path)
            print(f"仿真完成，结果文件: {lis_file}")
        except subprocess.CalledProcessError as e:
            messagebox.showerror("仿真错误", f"HSPICE仿真失败: {e}")
            return
    else:
        lis_file = filedialog.askopenfilename(
            title="选择HSPICE结果文件",
            filetypes=[("HSPICE Output", "*.lis"), ("All Files", "*.*")]
        )
        if not lis_file:
            print("未选择文件，程序退出")
            return
    
    # 解析结果文件
    try:
        print("解析结果文件...")
        results = parse_lis_file(lis_file)
        
        if not results:
            messagebox.showerror("解析错误", "未找到有效数据，请检查.lis文件格式")
            return
        
        print(f"成功解析 {len(results)} 组数据")
        print("绘制图表...")
        plot_results(results)
        
    except Exception as e:
        messagebox.showerror("错误", f"处理数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()