import subprocess
import re
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import tkinter as tk
from tkinter import filedialog

def run_hspice_simulation(netlist_path):
    """运行HSPICE仿真并返回输出文件路径"""
    output_file = netlist_path.replace('.sp', '.lis')
    cmd = f'hspice mos.sp -o mos.lis'
    subprocess.run(cmd, shell=True, check=True)
    return output_file

def parse_lis_file(lis_file):
    """解析.lis文件，提取所有扫描数据"""
    with open(lis_file, 'r') as f:
        content = f.read()
    
    # 使用正则表达式提取不同Vg值的数据块
    pattern = r'sweep\s+vg\s*=\s*([\d\.eE+-]+)\s*\n(.*?)\n\n'
    matches = re.findall(pattern, content, re.DOTALL)
    
    results = {}
    for vg_str, data_block in matches:
        vg = float(vg_str)
        
        # 提取电压和电流数据
        data_lines = [line.strip() for line in data_block.split('\n') 
                     if line.strip() and not line.startswith('index') and not line.startswith('---')]
        
        vds, ib, id, is_vals = [], [], [], []
        for line in data_lines:
            parts = line.split()
            if len(parts) >= 4:
                try:
                    vds.append(float(parts[0]))
                    ib.append(float(parts[1]))
                    id.append(float(parts[2]))
                    is_vals.append(float(parts[3]))
                except ValueError:
                    continue
        
        # 计算ib/id百分比（避免除以零）
        ratio = [100 * b / d if abs(d) > 1e-20 else 0 for b, d in zip(ib, id)]
        
        results[vg] = {
            'vds': np.array(vds),
            'ib': np.array(ib),
            'id': np.array(id),
            'is': np.array(is_vals),
            'ratio': np.array(ratio)
        }
    
    return results

def plot_results(results):
    """创建带有4个子图的图表"""
    # 创建Tkinter窗口
    root = tk.Tk()
    root.title("HSPICE仿真结果分析")
    root.geometry("1200x900")
    
    # 创建图形和子图
    fig, axs = plt.subplots(4, 1, figsize=(12, 10))
    fig.subplots_adjust(hspace=0.4)
    
    # 设置颜色和标记样式
    colors = ['b', 'r', 'g', 'm', 'c', 'y']
    markers = ['o', 's', '^', 'd', 'v', '<']
    
    # 绘制每个Vg的数据
    for i, (vg, data) in enumerate(results.items()):
        color = colors[i % len(colors)]
        marker = markers[i % len(markers)]
        label = f'Vg={vg}V'
        
        # 子图1: Ib (体电流)
        axs[0].semilogy(data['vds'], np.abs(data['ib']), 
                       marker=marker, linestyle='-', 
                       color=color, label=label)
        
        # 子图2: Id (漏电流)
        axs[1].plot(data['vds'], np.abs(data['id']), 
                   marker=marker, linestyle='-', 
                   color=color, label=label)
        
        # 子图3: Is (源电流)
        axs[2].plot(data['vds'], np.abs(data['is']), 
                   marker=marker, linestyle='-', 
                   color=color, label=label)
        
        # 子图4: Ib/Id 百分比
        axs[3].plot(data['vds'], data['ratio'], 
                   marker=marker, linestyle='-', 
                   color=color, label=label)
    
    # 设置子图1: Ib
    axs[0].set_title('体电流 (Ib)')
    axs[0].set_ylabel('Ib (A)')
    axs[0].set_yscale('log')
    axs[0].grid(True, which="both", ls="-", alpha=0.3)
    axs[0].legend(loc='best')
    
    # 设置子图2: Id
    axs[1].set_title('漏电流 (Id)')
    axs[1].set_ylabel('Id (A)')
    axs[1].grid(True)
    axs[1].legend(loc='best')
    
    # 设置子图3: Is
    axs[2].set_title('源电流 (Is)')
    axs[2].set_ylabel('Is (A)')
    axs[2].grid(True)
    axs[2].legend(loc='best')
    
    # 设置子图4: Ib/Id 百分比
    axs[3].set_title('Ib/Id 百分比')
    axs[3].set_xlabel('Vds (V)')
    axs[3].set_ylabel('百分比 (%)')
    axs[3].grid(True)
    axs[3].legend(loc='best')
    
    # 添加导航工具栏
    canvas = FigureCanvasTkAgg(fig, master=root)
    canvas.draw()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
    
    toolbar = NavigationToolbar2Tk(canvas, root)
    toolbar.update()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
    
    # 添加保存按钮
    def save_figure():
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG", "*.png"), ("PDF", "*.pdf"), ("All Files", "*.*")]
        )
        if file_path:
            fig.savefig(file_path, dpi=300)
            tk.messagebox.showinfo("保存成功", f"图表已保存至:\n{file_path}")
    
    save_btn = tk.Button(root, text="保存图表", command=save_figure)
    save_btn.pack(side=tk.BOTTOM, pady=10)
    
    root.mainloop()

def main():
    """主函数：选择网表文件并执行仿真分析"""
    # 创建文件选择对话框
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    netlist_path = filedialog.askopenfilename(
        title="选择HSPICE网表文件",
        filetypes=[("SPICE Files", "*.sp"), ("All Files", "*.*")]
    )
    
    if not netlist_path:
        print("未选择文件，程序退出")
        return
    
    # 运行仿真并分析结果
    try:
        print("正在运行HSPICE仿真...")
        lis_file = run_hspice_simulation(netlist_path)
        print(f"仿真完成，结果文件: {lis_file}")
        
        print("解析结果文件...")
        results = parse_lis_file(lis_file)
        
        if not results:
            print("未找到有效数据，请检查.lis文件格式")
            return
        
        print("绘制图表...")
        plot_results(results)
        
    except subprocess.CalledProcessError as e:
        print(f"HSPICE仿真失败: {e}")
    except Exception as e:
        print(f"发生错误: {str(e)}")

if __name__ == "__main__":
    main()