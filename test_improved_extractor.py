#!/usr/bin/env python3
"""
Test Improved Parameter Extractor
Tests the new improved parameter extraction algorithms
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_realistic_esd_data():
    """Create realistic ESD device I-V data for testing"""
    print("Creating realistic ESD test data...")
    
    # Voltage range
    voltage = np.linspace(0, 20, 200)
    
    # True parameters for comparison
    true_params = {
        'I_leak': 2e-10,
        'Vt1': 12.5,
        'k': 0.8,
        'Ron': 1.5,
        'Vh': 14.0,
        'I_offset': 0.02,
        'Isb': 0.1,
        'Vsb': 13.5
    }
    
    # Generate true ESD I-V curve
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < true_params['Vt1']:
            # Leakage region
            current[i] = true_params['I_leak'] * np.exp(v / 2.0)
        elif v < true_params['Vh']:
            # Trigger region
            current[i] = true_params['I_leak'] * np.exp(2.0) * np.exp(true_params['k'] * (v - true_params['Vt1']))
        else:
            # Snapback region
            linear_part = (v - true_params['Vsb']) / true_params['Ron']
            exp_part = true_params['Isb'] * np.exp(-(v - true_params['Vsb']) / 2.0)
            current[i] = true_params['I_offset'] + max(0, linear_part) + exp_part
    
    # Add realistic noise
    noise_level = 0.05  # 5% noise
    noise = np.random.normal(0, current * noise_level)
    current_noisy = current + noise
    current_noisy = np.maximum(current_noisy, 1e-15)  # Ensure positive
    
    print(f"✓ Generated ESD data with {len(voltage)} points")
    print(f"  Voltage range: {voltage.min():.1f} to {voltage.max():.1f} V")
    print(f"  Current range: {current_noisy.min():.2e} to {current_noisy.max():.2e} A")
    
    return {
        'voltage': voltage,
        'current': current_noisy,
        'true_current': current,
        'true_params': true_params
    }

def test_original_extractor(data):
    """Test original parameter extractor"""
    print("\nTesting original parameter extractor...")
    
    try:
        from data.parameter_extractor import ParameterExtractor
        
        extractor = ParameterExtractor()
        
        # Extract parameters
        extracted_params = extractor.extract_parameters(data)
        
        # Calculate model current
        model_current = extractor.calculate_model_current(data['voltage'], extracted_params)
        
        # Calculate error
        log_error = np.mean(np.abs(np.log10(data['current'] + 1e-15) - np.log10(model_current + 1e-15)))
        
        print("Original extractor results:")
        for key, value in extracted_params.items():
            if 'I_' in key or 'Isb' in key:
                print(f"  {key}: {value:.3e}")
            else:
                print(f"  {key}: {value:.3f}")
        print(f"  Log Error: {log_error:.4f}")
        
        return {
            'params': extracted_params,
            'current': model_current,
            'error': log_error,
            'success': True
        }
        
    except Exception as e:
        print(f"✗ Original extractor failed: {e}")
        return {'success': False, 'error': str(e)}

def test_improved_extractor(data):
    """Test improved parameter extractor"""
    print("\nTesting improved parameter extractor...")
    
    try:
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = True  # Enable debug output
        
        # Extract parameters
        extracted_params = extractor.extract_parameters(data)
        
        # Calculate model current
        model_current = extractor.calculate_model_current(data['voltage'], extracted_params)
        
        # Calculate error
        log_error = np.mean(np.abs(np.log10(data['current'] + 1e-15) - np.log10(model_current + 1e-15)))
        
        print("Improved extractor results:")
        for key, value in extracted_params.items():
            if 'I_' in key or 'Isb' in key:
                print(f"  {key}: {value:.3e}")
            else:
                print(f"  {key}: {value:.3f}")
        print(f"  Log Error: {log_error:.4f}")
        
        return {
            'params': extracted_params,
            'current': model_current,
            'error': log_error,
            'success': True
        }
        
    except Exception as e:
        print(f"✗ Improved extractor failed: {e}")
        return {'success': False, 'error': str(e)}

def compare_with_true_parameters(extracted_params, true_params):
    """Compare extracted parameters with true parameters"""
    print("\nParameter comparison with true values:")
    print("-" * 50)
    
    total_error = 0
    param_count = 0
    
    for key in true_params:
        if key in extracted_params:
            true_val = true_params[key]
            extracted_val = extracted_params[key]
            
            # Calculate relative error
            rel_error = abs(extracted_val - true_val) / abs(true_val) * 100
            total_error += rel_error
            param_count += 1
            
            if 'I_' in key or 'Isb' in key:
                print(f"{key:10}: True={true_val:.3e}, Extracted={extracted_val:.3e}, Error={rel_error:.1f}%")
            else:
                print(f"{key:10}: True={true_val:.3f}, Extracted={extracted_val:.3f}, Error={rel_error:.1f}%")
    
    avg_error = total_error / param_count if param_count > 0 else 0
    print(f"\nAverage parameter error: {avg_error:.1f}%")
    
    return avg_error

def plot_comparison(data, original_result, improved_result):
    """Plot comparison of extraction results"""
    print("\nGenerating comparison plots...")
    
    try:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        voltage = data['voltage']
        measured = data['current']
        true_current = data['true_current']
        
        # Linear plot
        ax1.plot(voltage, measured, 'ko-', markersize=3, label='Measured Data', alpha=0.7)
        ax1.plot(voltage, true_current, 'g-', linewidth=2, label='True Model', alpha=0.9)
        
        if original_result['success']:
            ax1.plot(voltage, original_result['current'], 'r--', linewidth=2, 
                    label=f'Original (Error: {original_result["error"]:.3f})', alpha=0.8)
        
        if improved_result['success']:
            ax1.plot(voltage, improved_result['current'], 'b-', linewidth=2, 
                    label=f'Improved (Error: {improved_result["error"]:.3f})', alpha=0.8)
        
        ax1.set_xlabel('Voltage (V)')
        ax1.set_ylabel('Current (A)')
        ax1.set_title('Linear Scale Comparison')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Log plot
        ax2.semilogy(voltage, measured, 'ko-', markersize=3, label='Measured Data', alpha=0.7)
        ax2.semilogy(voltage, true_current, 'g-', linewidth=2, label='True Model', alpha=0.9)
        
        if original_result['success']:
            ax2.semilogy(voltage, original_result['current'], 'r--', linewidth=2, 
                        label=f'Original (Error: {original_result["error"]:.3f})', alpha=0.8)
        
        if improved_result['success']:
            ax2.semilogy(voltage, improved_result['current'], 'b-', linewidth=2, 
                        label=f'Improved (Error: {improved_result["error"]:.3f})', alpha=0.8)
        
        ax2.set_xlabel('Voltage (V)')
        ax2.set_ylabel('Current (A)')
        ax2.set_title('Log Scale Comparison')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('parameter_extraction_comparison.png', dpi=150, bbox_inches='tight')
        print("✓ Comparison plot saved as 'parameter_extraction_comparison.png'")
        
        return True
        
    except Exception as e:
        print(f"✗ Plot generation failed: {e}")
        return False

def main():
    """Main test function"""
    print("Improved Parameter Extractor Test")
    print("=" * 50)
    
    # Create test data
    test_data = create_realistic_esd_data()
    
    # Test both extractors
    original_result = test_original_extractor(test_data)
    improved_result = test_improved_extractor(test_data)
    
    # Compare results
    print("\n" + "="*60)
    print("COMPARISON RESULTS")
    print("="*60)
    
    if original_result['success'] and improved_result['success']:
        print(f"\nFitting Quality (Log Error):")
        print(f"  Original Extractor:  {original_result['error']:.4f}")
        print(f"  Improved Extractor:  {improved_result['error']:.4f}")
        
        improvement = (original_result['error'] - improved_result['error']) / original_result['error'] * 100
        print(f"  Improvement:         {improvement:.1f}%")
        
        # Compare with true parameters
        if improved_result['success']:
            print("\nImproved Extractor vs True Parameters:")
            avg_error = compare_with_true_parameters(improved_result['params'], test_data['true_params'])
            
        # Generate plots
        plot_comparison(test_data, original_result, improved_result)
        
        # Overall assessment
        print("\n" + "="*60)
        print("ASSESSMENT")
        print("="*60)
        
        if improved_result['error'] < original_result['error']:
            print("✅ IMPROVED EXTRACTOR IS BETTER!")
            print(f"   • {improvement:.1f}% reduction in fitting error")
            print(f"   • Average parameter error: {avg_error:.1f}%")
        else:
            print("⚠️  Original extractor performed better on this test")
            
    else:
        print("❌ One or both extractors failed")
        if not original_result['success']:
            print(f"   Original: {original_result['error']}")
        if not improved_result['success']:
            print(f"   Improved: {improved_result['error']}")
    
    print("\n📋 NEXT STEPS:")
    print("  1. Run: python main.py")
    print("  2. Load your ESD data")
    print("  3. Use Auto-Fit Parameters")
    print("  4. Compare the fitting quality")
    
    return True

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
