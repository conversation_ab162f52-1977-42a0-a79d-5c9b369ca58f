* BJT ESD Behavioral Model - Exact Fitted Model Implementation
* This model exactly replicates the fitted mathematical model

.subckt bjt_esd_exact anode cathode
* Fitted model parameters
.param I_leak=1.744541e-07
.param Vt1=3.624550
.param k=0.644014
.param Ron=1.441466
.param Vh=12.720120
.param I_offset=1.256381e-03
.param Isb=4.981225e-01
.param Vsb=12.720120

* Voltage sensing
V_sense anode n_sense 0

* Behavioral current source implementing exact fitted model
G_esd n_sense cathode cur='bjt_esd_exact_current(V(anode,cathode))'

* Exact fitted model implementation
.param bjt_esd_exact_current(v) = '
+ if(v <= 0, I_leak*1e-6,
+ if(v < Vt1, I_leak*exp(v/1.0),
+ if(v < Vh, I_leak*exp(k*(v-Vt1)/max(Vt1,0.1)),
+ I_offset + max((v-Vsb)/Ron, 0) + Isb*exp(-abs(v-Vsb))
+ )))'

.ends bjt_esd_exact

* Test circuit
.subckt test_exact
Vin n1 0 DC 0
X_esd n1 0 bjt_esd_exact
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.ends test_exact

.end
