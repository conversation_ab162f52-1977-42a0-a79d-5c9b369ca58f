#!/usr/bin/env python3
"""
Test script to verify installation and basic functionality
"""

import sys
import os
import traceback

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import PyQt5
        print("✓ PyQt5 imported successfully")
    except ImportError as e:
        print(f"✗ PyQt5 import failed: {e}")
        return False
    
    try:
        import matplotlib
        print("✓ matplotlib imported successfully")
    except ImportError as e:
        print(f"✗ matplotlib import failed: {e}")
        return False
    
    try:
        import numpy
        print("✓ numpy imported successfully")
    except ImportError as e:
        print(f"✗ numpy import failed: {e}")
        return False
    
    try:
        import scipy
        print("✓ scipy imported successfully")
    except ImportError as e:
        print(f"✗ scipy import failed: {e}")
        return False
    
    try:
        import pandas
        print("✓ pandas imported successfully")
    except ImportError as e:
        print(f"✗ pandas import failed: {e}")
        return False
    
    return True

def test_project_modules():
    """Test if project modules can be imported"""
    print("\nTesting project modules...")
    
    # Add current directory to path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from models.bjt_esd_model import BJTESDModel
        print("✓ BJTESDModel imported successfully")
    except ImportError as e:
        print(f"✗ BJTESDModel import failed: {e}")
        return False
    
    try:
        from utils.data_loader import DataLoader
        print("✓ DataLoader imported successfully")
    except ImportError as e:
        print(f"✗ DataLoader import failed: {e}")
        return False
    
    try:
        from utils.hspice_interface import HSPICEInterface
        print("✓ HSPICEInterface imported successfully")
    except ImportError as e:
        print(f"✗ HSPICEInterface import failed: {e}")
        return False
    
    try:
        from gui.main_window import MainWindow
        print("✓ MainWindow imported successfully")
    except ImportError as e:
        print(f"✗ MainWindow import failed: {e}")
        return False
    
    return True

def test_model_functionality():
    """Test basic model functionality"""
    print("\nTesting model functionality...")
    
    try:
        from models.bjt_esd_model import BJTESDModel
        import numpy as np
        
        # Create model instance
        model = BJTESDModel()
        print("✓ Model instance created")
        
        # Test current equation
        voltage = np.linspace(0, 20, 100)
        current = model.current_equation(voltage)
        print(f"✓ Current equation computed for {len(voltage)} points")
        
        # Test parameter info
        param_info = model.get_parameter_info()
        print(f"✓ Parameter info retrieved ({len(param_info)} parameters)")
        
        return True
        
    except Exception as e:
        print(f"✗ Model functionality test failed: {e}")
        traceback.print_exc()
        return False

def test_data_loader():
    """Test data loader with sample data"""
    print("\nTesting data loader...")
    
    try:
        from utils.data_loader import DataLoader
        
        # Check if sample data file exists
        if os.path.exists("1.csv"):
            loader = DataLoader()
            voltage, current, metadata = loader.load_data("1.csv")
            print(f"✓ Sample data loaded: {len(voltage)} points")
            
            if metadata:
                print(f"✓ Metadata extracted: {len(metadata)} parameters")
            
            # Test validation
            is_valid = loader.validate_data(voltage, current)
            print(f"✓ Data validation: {'passed' if is_valid else 'failed'}")
            
        else:
            print("! Sample data file (1.csv) not found, skipping data loader test")
        
        return True
        
    except Exception as e:
        print(f"✗ Data loader test failed: {e}")
        traceback.print_exc()
        return False

def test_gui_creation():
    """Test GUI creation without showing"""
    print("\nTesting GUI creation...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import MainWindow
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create main window (but don't show it)
        window = MainWindow()
        print("✓ Main window created successfully")
        
        # Test basic functionality
        if hasattr(window, 'model'):
            print("✓ Model integration working")
        
        if hasattr(window, 'plot_widget'):
            print("✓ Plot widget created")
        
        if hasattr(window, 'parameter_panel'):
            print("✓ Parameter panel created")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI creation test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("BJT ESD Parameter Extractor - Installation Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_project_modules,
        test_model_functionality,
        test_data_loader,
        test_gui_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Installation is working correctly.")
        print("\nYou can now run the application with: python main.py")
    else:
        print("✗ Some tests failed. Please check the error messages above.")
        print("\nTry installing missing dependencies with: pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
