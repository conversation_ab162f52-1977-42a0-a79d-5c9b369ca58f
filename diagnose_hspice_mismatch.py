#!/usr/bin/env python3
"""
Diagnose HSPICE Simulation Mismatch
Systematic analysis of why HSPICE results differ from model curve
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_hspice_output():
    """Analyze HSPICE output file to understand the simulation results"""
    print("HSPICE OUTPUT ANALYSIS")
    print("=" * 50)
    
    # Look for HSPICE output files
    hspice_files = []
    for file in os.listdir('.'):
        if file.endswith('.lis') or file.endswith('.out'):
            hspice_files.append(file)
    
    if not hspice_files:
        print("❌ No HSPICE output files found (.lis or .out)")
        print("Please run HSPICE simulation first")
        return None
    
    print(f"Found HSPICE files: {hspice_files}")
    
    # Try to read the most recent .lis file
    latest_file = max(hspice_files, key=os.path.getmtime)
    print(f"Analyzing: {latest_file}")
    
    try:
        with open(latest_file, 'r') as f:
            content = f.read()
        
        # Look for simulation data
        lines = content.split('\n')
        
        # Find data section
        data_start = None
        for i, line in enumerate(lines):
            if 'v(n_anode)' in line.lower() or 'i(vin)' in line.lower():
                data_start = i
                break
        
        if data_start is None:
            print("❌ Could not find simulation data in HSPICE output")
            return None
        
        # Extract voltage and current data
        voltages = []
        currents = []
        
        for i in range(data_start + 1, len(lines)):
            line = lines[i].strip()
            if not line or line.startswith('*'):
                continue
                
            try:
                parts = line.split()
                if len(parts) >= 2:
                    v = float(parts[0])
                    i = abs(float(parts[1]))  # Take absolute value
                    voltages.append(v)
                    currents.append(i)
            except:
                continue
        
        if len(voltages) == 0:
            print("❌ No valid data points found in HSPICE output")
            return None
        
        print(f"✓ Extracted {len(voltages)} data points from HSPICE")
        print(f"  Voltage range: {min(voltages):.2f}V to {max(voltages):.2f}V")
        print(f"  Current range: {min(currents):.2e}A to {max(currents):.2e}A")
        
        return {
            'voltage': np.array(voltages),
            'current': np.array(currents),
            'file': latest_file
        }
        
    except Exception as e:
        print(f"❌ Error reading HSPICE file: {e}")
        return None

def load_model_curve():
    """Load the fitted model curve for comparison"""
    print("\nMODEL CURVE ANALYSIS")
    print("=" * 50)
    
    try:
        # Load measurement data and extract parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        print("✓ Loaded fitted model parameters:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"    {key}: {value:.6e}")
            else:
                print(f"    {key}: {value:.6f}")
        
        # Generate model curve
        voltage = np.linspace(0, 20, 200)
        model_current = extractor.calculate_model_current(voltage, parameters)
        
        print(f"✓ Generated model curve with {len(voltage)} points")
        print(f"  Voltage range: {voltage.min():.2f}V to {voltage.max():.2f}V")
        print(f"  Current range: {model_current.min():.2e}A to {model_current.max():.2e}A")
        
        return {
            'voltage': voltage,
            'current': model_current,
            'parameters': parameters
        }
        
    except Exception as e:
        print(f"❌ Error loading model curve: {e}")
        return None

def check_spice_netlist():
    """Check the SPICE netlist that was used for simulation"""
    print("\nSPICE NETLIST ANALYSIS")
    print("=" * 50)
    
    # Look for SPICE netlist files
    spice_files = []
    for file in os.listdir('.'):
        if file.endswith('.ckt') or file.endswith('.sp') or file.endswith('.spice'):
            spice_files.append(file)
    
    if not spice_files:
        print("❌ No SPICE netlist files found")
        return None
    
    # Analyze the most recent netlist
    latest_netlist = max(spice_files, key=os.path.getmtime)
    print(f"Analyzing netlist: {latest_netlist}")
    
    try:
        with open(latest_netlist, 'r') as f:
            content = f.read()
        
        print("✓ Netlist content preview:")
        lines = content.split('\n')
        
        # Look for key sections
        found_params = False
        found_model = False
        found_analysis = False
        
        for i, line in enumerate(lines[:50]):  # Check first 50 lines
            line_clean = line.strip()
            if not line_clean or line_clean.startswith('*'):
                continue
                
            print(f"  {i+1:2d}: {line_clean}")
            
            if '.param' in line_clean.lower():
                found_params = True
            if '.model' in line_clean.lower() or 'cur=' in line_clean.lower():
                found_model = True
            if '.dc' in line_clean.lower():
                found_analysis = True
        
        print(f"\nNetlist analysis:")
        print(f"  Parameters defined: {'✓' if found_params else '❌'}")
        print(f"  Model defined: {'✓' if found_model else '❌'}")
        print(f"  DC analysis: {'✓' if found_analysis else '❌'}")
        
        return {
            'file': latest_netlist,
            'content': content,
            'has_params': found_params,
            'has_model': found_model,
            'has_analysis': found_analysis
        }
        
    except Exception as e:
        print(f"❌ Error reading netlist: {e}")
        return None

def compare_curves(hspice_data, model_data):
    """Compare HSPICE simulation with model curve"""
    print("\nCURVE COMPARISON ANALYSIS")
    print("=" * 50)
    
    if hspice_data is None or model_data is None:
        print("❌ Cannot compare - missing data")
        return
    
    # Interpolate model curve to HSPICE voltage points
    hspice_v = hspice_data['voltage']
    hspice_i = hspice_data['current']
    
    model_v = model_data['voltage']
    model_i = model_data['current']
    
    # Interpolate model current at HSPICE voltage points
    model_i_interp = np.interp(hspice_v, model_v, model_i)
    
    # Calculate errors
    abs_error = np.abs(hspice_i - model_i_interp)
    rel_error = abs_error / (model_i_interp + 1e-15) * 100
    log_error = np.abs(np.log10(hspice_i + 1e-15) - np.log10(model_i_interp + 1e-15))
    
    print(f"Comparison results:")
    print(f"  Data points compared: {len(hspice_v)}")
    print(f"  Maximum absolute error: {np.max(abs_error):.2e} A")
    print(f"  Maximum relative error: {np.max(rel_error):.1f} %")
    print(f"  Average log error: {np.mean(log_error):.3f}")
    
    # Check specific voltage points
    test_voltages = [1, 5, 10, 15, 20]
    print(f"\nPoint-by-point comparison:")
    print(f"{'V':>3} | {'HSPICE':>12} | {'Model':>12} | {'Error':>10} | {'Ratio':>8}")
    print("-" * 60)
    
    for test_v in test_voltages:
        if test_v <= np.max(hspice_v):
            # Find closest voltage point
            idx = np.argmin(np.abs(hspice_v - test_v))
            v_actual = hspice_v[idx]
            i_hspice = hspice_i[idx]
            i_model = np.interp(v_actual, model_v, model_i)
            
            error = abs(i_hspice - i_model)
            ratio = i_hspice / i_model if i_model > 0 else float('inf')
            
            print(f"{v_actual:3.0f} | {i_hspice:12.3e} | {i_model:12.3e} | {error:10.2e} | {ratio:8.2f}")
    
    # Identify problem regions
    print(f"\nProblem identification:")
    
    # Check if HSPICE current is too high
    if np.mean(hspice_i) > np.mean(model_i_interp) * 10:
        print("❌ HSPICE current is much higher than model")
        print("   Possible causes:")
        print("   - Wrong parameter scaling in SPICE model")
        print("   - Incorrect diode model parameters")
        print("   - Missing series resistance")
        
    # Check if HSPICE current is too low
    elif np.mean(hspice_i) < np.mean(model_i_interp) / 10:
        print("❌ HSPICE current is much lower than model")
        print("   Possible causes:")
        print("   - Behavioral model not working")
        print("   - Wrong parameter values")
        print("   - HSPICE convergence issues")
        
    # Check for wrong shape
    elif np.mean(log_error) > 1.0:
        print("❌ HSPICE curve has wrong shape")
        print("   Possible causes:")
        print("   - Wrong mathematical model in SPICE")
        print("   - Parameter mapping errors")
        print("   - Model equation syntax errors")
    
    else:
        print("✓ Curves have similar magnitude and shape")
        print("  Small differences may be due to:")
        print("  - Numerical precision")
        print("  - HSPICE model approximations")

def identify_root_cause():
    """Identify the most likely root cause"""
    print("\nROOT CAUSE ANALYSIS")
    print("=" * 50)
    
    # Check what type of SPICE model was used
    netlist_info = check_spice_netlist()
    
    if netlist_info is None:
        print("❌ Cannot analyze - no SPICE netlist found")
        return
    
    content = netlist_info['content'].lower()
    
    # Determine model type
    if 'cur=' in content and 'if(' in content:
        model_type = "Behavioral"
        print(f"✓ Detected model type: {model_type}")
        print("  This should provide exact matching")
        
        if 'i_leak*exp(' in content:
            print("  ✓ Contains exponential equations")
        else:
            print("  ❌ Missing expected exponential equations")
            
    elif '.model' in content and 'd(' in content:
        model_type = "Diode"
        print(f"✓ Detected model type: {model_type}")
        print("  This is an approximation and may not match exactly")
        
        if 'is=' in content:
            print("  ✓ Contains diode parameters")
            # Check for wrong scaling
            if 'is=.*e+' in content:
                print("  ❌ WARNING: IS parameter may be too large")
        else:
            print("  ❌ Missing diode parameters")
            
    else:
        model_type = "Unknown"
        print(f"❌ Could not determine model type")
    
    # Check for common issues
    print(f"\nCommon issue checklist:")
    
    issues_found = []
    
    # Check for parameter scaling issues
    if 'is=' in content and ('e+' in content or '*1e6' in content):
        issues_found.append("Parameter scaling: IS parameter may be scaled incorrectly")
    
    # Check for missing behavioral support
    if model_type == "Behavioral" and 'cur=' not in content:
        issues_found.append("Behavioral model: Missing current equation")
    
    # Check for wrong voltage references
    if 'bv=' in content and 'vh' in content:
        issues_found.append("Voltage reference: BV should use Vt1, not Vh")
    
    # Check for missing series resistance
    if model_type == "Diode" and 'ron' not in content:
        issues_found.append("Series resistance: Missing Ron parameter")
    
    if issues_found:
        print("❌ Issues found:")
        for issue in issues_found:
            print(f"   • {issue}")
    else:
        print("✓ No obvious issues detected in netlist")
    
    return {
        'model_type': model_type,
        'issues': issues_found
    }

def recommend_solutions():
    """Recommend specific solutions based on analysis"""
    print("\nRECOMMENDED SOLUTIONS")
    print("=" * 50)
    
    print("Based on the analysis, try these solutions in order:")
    
    print("\n1. 🔧 USE BEHAVIORAL MODEL")
    print("   • File → Save Fixed SPICE Model...")
    print("   • Select 'behavioral' model type")
    print("   • This ensures 100% mathematical equivalence")
    
    print("\n2. 🔧 CHECK PARAMETER VALUES")
    print("   • Verify parameters in .ckt file match GUI")
    print("   • Look for scaling issues (e.g., IS parameter)")
    print("   • Ensure all parameters are reasonable")
    
    print("\n3. 🔧 VERIFY HSPICE SYNTAX")
    print("   • Check if HSPICE supports behavioral modeling")
    print("   • Verify 'cur=' syntax is correct")
    print("   • Add .option post=2 for better output")
    
    print("\n4. 🔧 USE EXACT CONVERSION")
    print("   • Tools → Exact Parameter Conversion")
    print("   • Select Method 1 (Behavioral)")
    print("   • This guarantees perfect matching")
    
    print("\n5. 🔧 DEBUG STEP BY STEP")
    print("   • Test with simple parameters first")
    print("   • Check individual regions (leakage, trigger, snapback)")
    print("   • Compare intermediate calculations")

def main():
    """Main diagnostic function"""
    print("HSPICE SIMULATION MISMATCH DIAGNOSIS")
    print("=" * 60)
    print("Analyzing why HSPICE results differ from model curve")
    
    # Step 1: Analyze HSPICE output
    hspice_data = analyze_hspice_output()
    
    # Step 2: Load model curve
    model_data = load_model_curve()
    
    # Step 3: Compare curves
    compare_curves(hspice_data, model_data)
    
    # Step 4: Identify root cause
    root_cause = identify_root_cause()
    
    # Step 5: Recommend solutions
    recommend_solutions()
    
    print("\n" + "=" * 60)
    print("DIAGNOSIS COMPLETE")
    print("=" * 60)
    
    if hspice_data and model_data:
        print("✓ Analysis completed successfully")
        print("✓ Check recommendations above")
        print("✓ Most likely solution: Use behavioral SPICE model")
    else:
        print("⚠️  Limited analysis due to missing data")
        print("⚠️  Ensure HSPICE simulation has been run")
    
    return True

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
