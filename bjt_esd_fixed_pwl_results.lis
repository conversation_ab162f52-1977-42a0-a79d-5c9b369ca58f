 ****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******                    
  Copyright (c) 1986 - 2025 by Synopsys, Inc. All Rights Reserved.              
  This software and the associated documentation are proprietary
  to Synopsys, Inc. This software may only be used in accordance
  with the terms and conditions of a written license agreement with
  Synopsys, Inc. All other use, reproduction, or distribution of
  this software is strictly prohibited.
  Input File: bjt_esd_fixed_pwl.ckt                                             
  Command line options: C:\synopsys\Hspice_P-2019.06-SP1-1\WIN64\hspice.com bjt_esd_fixed_pwl.ckt -o bjt_esd_fixed_pwl_results.lis
  Start time: Mon Jun  9 09:11:22 2025
  lic:  
  lic: FLEXlm: SDK_12.3 
  lic: USER:   e02727               HOSTNAME: ascend27 
  lic: HOSTID: "982cbcdcead7"       PID:      13004 
  lic: Using FLEXlm license file: 
  lic: 27000@ascend27 
  lic: Checkout 1 hspice 
  lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12 
  lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@ascend27 
  lic:   
  **error** (bjt_esd_fixed_pwl.ckt:27) syntax error at or before "V" "(" "anode" "cathode" ")" ")" "(" 

Syntax reference: 
Gxxx n+ n- LAPLACE in+ in- k0, k1, ..., kn / d0, d1, ..., dm
+ [SCALE=val] [TC1=val] [TC2=val] [M=val]
Gxxx n+ n- POLE in+ in- a az1, fz1, ..., azn, fzn / b,
+ ap1, fp1, ..., apm, fpm <SCALE=val> <TC1=val>
+ <TC2=val> <M=val>
Gxxx n+ n- FREQ in+ in- f1, a1, f1, ..., fi, ai, f1
+ <DELF=val> <MAXF=val> <SCALE=val> <TC1=val>
+ <TC2=val> <M=val> <LEVEL=val> <ACCURACY=val>
Gxxx n+ n- FOSTER in+ in- k0 k1
+ (Re{A1}, Im{A1})/ (Re{p1}, Im{p1})
+ (Re{A2}, Im{A2})/ (Re{p2}, Im{p2})
+ (Re{A3}, Im{A3})/ (Re{p3}, Im{p3})
+ ...



               ***** job aborted
  lic: Release hspice token(s) 
 lic: total license checkout elapse time:        0.03(s)
