#!/usr/bin/env python3
"""
Verify the Fix for Parameter Transfer
Test that all models now match exactly
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fixed_models():
    """Test that all models now match exactly"""
    print("VERIFICATION TEST: Fixed Parameter Transfer")
    print("=" * 60)
    
    try:
        # Load real data and extract parameters
        from data.data_loader import DataLoader
        from data.parameter_extractor import ParameterExtractor
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.fixed_spice_generator import FixedSpiceGenerator
        
        # Load data
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        print(f"✓ Loaded data: {len(data['voltage'])} points")
        
        # Extract parameters with improved extractor
        improved_extractor = ImprovedParameterExtractor()
        improved_extractor.debug = False
        parameters = improved_extractor.extract_parameters(data)
        
        print("✓ Extracted parameters:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"    {key}: {value:.6e}")
            else:
                print(f"    {key}: {value:.6f}")
        
        # Test voltage range
        voltage = np.linspace(0, 20, 100)
        
        # Calculate currents with all methods
        print(f"\nCalculating currents with all methods...")
        
        # 1. Improved extractor
        improved_current = improved_extractor.calculate_model_current(voltage, parameters)
        
        # 2. Original extractor
        original_extractor = ParameterExtractor()
        original_current = original_extractor.calculate_model_current(voltage, parameters)
        
        # 3. Fixed SPICE generator
        generator = FixedSpiceGenerator()
        spice_current = generator.calculate_fitted_current(voltage, parameters)
        
        # Calculate errors
        def calc_error(current1, current2):
            try:
                log1 = np.log10(current1 + 1e-15)
                log2 = np.log10(current2 + 1e-15)
                return np.mean(np.abs(log1 - log2))
            except:
                return float('inf')
        
        error_improved_vs_original = calc_error(improved_current, original_current)
        error_improved_vs_spice = calc_error(improved_current, spice_current)
        error_original_vs_spice = calc_error(original_current, spice_current)
        
        print(f"\nError Analysis:")
        print(f"  Improved vs Original:  {error_improved_vs_original:.2e}")
        print(f"  Improved vs SPICE:     {error_improved_vs_spice:.2e}")
        print(f"  Original vs SPICE:     {error_original_vs_spice:.2e}")
        
        # Test specific points
        test_voltages = [5, 10, 15, 18]
        print(f"\nPoint-by-point verification:")
        print(f"{'V':>3} | {'Improved':>12} | {'Original':>12} | {'SPICE':>12} | {'Match?':>8}")
        print("-" * 65)
        
        all_match = True
        for test_v in test_voltages:
            idx = np.argmin(np.abs(voltage - test_v))
            v = voltage[idx]
            
            improved_i = improved_current[idx]
            original_i = original_current[idx]
            spice_i = spice_current[idx]
            
            # Check if they match (within floating point precision)
            match1 = abs(improved_i - original_i) < 1e-15
            match2 = abs(improved_i - spice_i) < 1e-15
            match3 = abs(original_i - spice_i) < 1e-15
            
            match_all = match1 and match2 and match3
            if not match_all:
                all_match = False
            
            match_str = "✅" if match_all else "❌"
            
            print(f"{v:3.0f} | {improved_i:12.3e} | {original_i:12.3e} | {spice_i:12.3e} | {match_str:>8}")
        
        # Overall assessment
        print(f"\n" + "="*60)
        print("VERIFICATION RESULTS")
        print("="*60)
        
        if error_improved_vs_original < 1e-15 and error_improved_vs_spice < 1e-15:
            print("🎉 SUCCESS: All models match perfectly!")
            print("✅ Parameter transfer is now exact")
            print("✅ SPICE simulation will match fitted model")
            success = True
        elif error_improved_vs_original < 1e-10 and error_improved_vs_spice < 1e-10:
            print("✅ EXCELLENT: Models match within numerical precision")
            print("✅ Parameter transfer is essentially exact")
            success = True
        elif error_improved_vs_original < 1e-6 and error_improved_vs_spice < 1e-6:
            print("⚠️  GOOD: Models match well but not perfectly")
            print("⚠️  Small differences may exist in simulation")
            success = False
        else:
            print("❌ FAILED: Models still have significant differences")
            print("❌ Parameter transfer problem not fully resolved")
            success = False
        
        return success, {
            'improved_current': improved_current,
            'original_current': original_current,
            'spice_current': spice_current,
            'voltage': voltage,
            'parameters': parameters
        }
        
    except Exception as e:
        print(f"✗ Verification test failed: {e}")
        return False, None

def create_verification_plot(results):
    """Create verification plot"""
    if results is None:
        return False
        
    print(f"\nCreating verification plot...")
    
    try:
        voltage = results['voltage']
        improved = results['improved_current']
        original = results['original_current']
        spice = results['spice_current']
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Linear plot
        ax1.plot(voltage, improved, 'b-', linewidth=3, label='Improved Extractor', alpha=0.8)
        ax1.plot(voltage, original, 'r--', linewidth=2, label='Original Extractor', alpha=0.7)
        ax1.plot(voltage, spice, 'g:', linewidth=2, label='SPICE Model', alpha=0.7)
        ax1.set_xlabel('Voltage (V)')
        ax1.set_ylabel('Current (A)')
        ax1.set_title('Linear Scale: All Models Should Match')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Log plot
        ax2.semilogy(voltage, improved, 'b-', linewidth=3, label='Improved Extractor', alpha=0.8)
        ax2.semilogy(voltage, original, 'r--', linewidth=2, label='Original Extractor', alpha=0.7)
        ax2.semilogy(voltage, spice, 'g:', linewidth=2, label='SPICE Model', alpha=0.7)
        ax2.set_xlabel('Voltage (V)')
        ax2.set_ylabel('Current (A)')
        ax2.set_title('Log Scale: All Models Should Match')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('verification_plot.png', dpi=150, bbox_inches='tight')
        print("✓ Verification plot saved: verification_plot.png")
        
        return True
        
    except Exception as e:
        print(f"✗ Plot creation failed: {e}")
        return False

def test_spice_model_generation():
    """Test SPICE model generation with fixed parameters"""
    print(f"\nTesting SPICE model generation...")
    
    try:
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.fixed_spice_generator import FixedSpiceGenerator
        
        # Load and extract
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        # Generate SPICE models
        generator = FixedSpiceGenerator()
        
        # Test behavioral model
        generator.set_model_type("behavioral")
        generator.generate_spice_model(parameters, "verification_behavioral.ckt")
        
        # Verify exact matching
        error = generator.compare_models(parameters)
        
        print(f"✓ Generated verification_behavioral.ckt")
        print(f"✓ Parameter transfer error: {error:.2e}")
        
        if error < 1e-15:
            print("🎉 PERFECT: SPICE model exactly matches fitted model!")
            return True
        elif error < 1e-10:
            print("✅ EXCELLENT: SPICE model essentially matches fitted model")
            return True
        else:
            print("⚠️  WARNING: SPICE model has some differences")
            return False
            
    except Exception as e:
        print(f"✗ SPICE model generation test failed: {e}")
        return False

def main():
    """Main verification function"""
    print("PARAMETER TRANSFER FIX VERIFICATION")
    print("=" * 60)
    print("Testing that the mathematical model fix resolves all issues")
    
    # Test 1: Model matching
    success1, results = test_fixed_models()
    
    # Test 2: Visualization
    success2 = create_verification_plot(results)
    
    # Test 3: SPICE generation
    success3 = test_spice_model_generation()
    
    # Final assessment
    print(f"\n" + "="*60)
    print("FINAL VERIFICATION RESULTS")
    print("="*60)
    
    print(f"\nTest Results:")
    print(f"  Model Matching:       {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"  Visualization:        {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"  SPICE Generation:     {'✅ PASS' if success3 else '❌ FAIL'}")
    
    overall_success = success1 and success2 and success3
    
    if overall_success:
        print(f"\n🎉 VERIFICATION SUCCESSFUL!")
        print(f"✅ All mathematical models now match exactly")
        print(f"✅ Parameter transfer problem is RESOLVED")
        print(f"✅ SPICE simulation will match fitted model")
        
        print(f"\n📋 READY FOR USE:")
        print(f"1. Use Auto-Fit Parameters in GUI")
        print(f"2. Save Fixed SPICE Model (behavioral)")
        print(f"3. Run HSPICE simulation")
        print(f"4. Results will match fitted model perfectly!")
        
    else:
        print(f"\n❌ VERIFICATION FAILED")
        print(f"Some issues remain. Check error messages above.")
        
    return overall_success

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
