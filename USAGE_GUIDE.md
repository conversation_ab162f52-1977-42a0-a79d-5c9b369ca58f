# BJT ESD Parameter Extractor - 使用指南

## 快速开始

### 1. 安装验证
```bash
# 运行安装测试
python test_installation.py
```
应该看到所有5项测试通过的消息。

### 2. 启动应用程序
```bash
# 方法1: 直接运行
python main.py

# 方法2: 使用批处理文件 (Windows)
run_app.bat

# 方法3: 运行演示脚本 (无GUI)
python demo.py
```

## 主要功能使用

### 1. 加载实验数据
1. 点击工具栏的 **Open** 按钮或菜单 **File → Open Data File**
2. 选择您的I-V数据文件 (支持CSV, TXT, DAT格式)
3. 数据将自动加载并在上方图形区域显示

**支持的数据格式:**
```csv
Voltage,Current
0.0,0.0
0.068,8.83e-09
0.137,1.81e-08
...
# 可选参数 (作为注释)
# I_leak,1.9981e-07
# Vt1,12.645
```

### 2. 参数拟合
1. 确保已加载实验数据
2. 点击工具栏的 **Fit** 按钮或菜单 **Analysis → Fit Parameters**
3. 等待拟合完成 (通常几秒钟)
4. 查看拟合结果:
   - 图形中的红色拟合曲线
   - **Results** 标签页中的R²值和参数值

### 3. 手动调节参数
1. 在下方 **Parameters** 标签页中:
   - 使用滑块进行粗调
   - 在输入框中直接输入精确值
   - 启用 **Real-time Update** 可实时看到变化
2. 参数说明:
   - **I_leak**: 漏电流 (A) - 指数刻度
   - **Vt1**: 触发电压 (V) - 线性刻度
   - **k**: 指数因子 - 线性刻度
   - **Ron**: 导通电阻 (Ω) - 线性刻度
   - **Vh**: 保持电压 (V) - 线性刻度
   - **I_offset**: 电流偏移 (A) - 线性刻度
   - **Isb**: 回扣电流 (A) - 线性刻度
   - **Vsb**: 回扣电压 (V) - 线性刻度

### 4. HSPICE仿真 (可选)
1. 确保HSPICE已安装并在PATH中
2. 点击工具栏的 **HSPICE** 按钮或菜单 **Analysis → Run HSPICE Simulation**
3. 仿真结果将以绿色虚线显示在图形中

### 5. 图形控制
在图形区域下方的控制面板中:
- **Experimental Data**: 显示/隐藏实验数据点
- **Fitted Model**: 显示/隐藏拟合曲线
- **HSPICE Simulation**: 显示/隐藏HSPICE仿真结果
- **Log Scale**: 切换Y轴对数/线性刻度
- **Clear Plot**: 清除所有图形数据

### 6. 导出结果
1. 菜单 **File → Export Results**
2. 选择保存位置和文件名
3. 结果将保存为CSV格式

## 高级功能

### 1. 批量处理
使用演示脚本作为模板，可以编写批量处理脚本:
```python
from models.bjt_esd_model import BJTESDModel
from utils.data_loader import DataLoader

model = BJTESDModel()
loader = DataLoader()

# 处理多个文件
for file_path in file_list:
    voltage, current, metadata = loader.load_data(file_path)
    fitted_params = model.fit_parameters(voltage, current)
    # 保存结果...
```

### 2. 自定义参数范围
编辑 `config/settings.py` 中的 `PARAMETER_RANGES` 来调整参数范围:
```python
PARAMETER_RANGES = {
    'I_leak': {'min': 1e-15, 'max': 1e-5, 'default': 1.9981e-07, 'unit': 'A'},
    # ... 其他参数
}
```

### 3. 模型扩展
在 `models/bjt_esd_model.py` 中可以:
- 修改电流方程
- 添加新的参数
- 实现不同的拟合算法

## 故障排除

### 1. 安装问题
```bash
# 重新安装依赖
pip install -r requirements.txt

# 检查Python版本 (需要3.7+)
python --version
```

### 2. 数据加载失败
- 检查CSV文件格式是否正确
- 确保数据中没有非数值字符
- 尝试删除空行和特殊字符

### 3. 参数拟合失败
- 检查数据质量 (至少10个数据点)
- 尝试调整初始参数值
- 检查数据范围是否合理

### 4. HSPICE问题
- 确保HSPICE已正确安装
- 检查PATH环境变量
- 在命令行测试: `hspice -v`

### 5. GUI问题
- 确保PyQt5正确安装
- 在虚拟环境中运行
- 检查显示设置和DPI缩放

## 性能优化

### 1. 大数据集处理
- 对于超过1000个数据点，考虑数据抽样
- 使用 `preprocess_data()` 函数清理数据

### 2. 拟合速度
- 提供好的初始参数估计
- 减少数据点数量
- 调整拟合算法参数

## 技术支持

### 日志文件
应用程序会生成日志信息，可以通过以下方式查看:
```python
import logging
logging.basicConfig(level=logging.INFO)
```

### 调试模式
在开发模式下运行:
```bash
python -u main.py  # 无缓冲输出
```

### 常见错误代码
- **R² < 0.9**: 拟合质量差，检查数据或模型
- **参数超出范围**: 调整参数边界
- **内存错误**: 减少数据点数量

## 最佳实践

1. **数据准备**: 确保数据干净、有序、无缺失值
2. **参数初值**: 根据器件特性设置合理的初始参数
3. **拟合验证**: 总是检查R²值和残差分布
4. **结果保存**: 及时保存重要的拟合结果
5. **版本控制**: 记录参数变化和拟合历史

## 扩展开发

项目采用模块化设计，便于扩展:
- **新器件模型**: 在 `models/` 目录添加新模型
- **新数据格式**: 在 `utils/data_loader.py` 添加支持
- **新仿真器**: 在 `utils/` 目录添加接口
- **新GUI组件**: 在 `gui/` 目录添加组件

欢迎贡献代码和改进建议！
