"""
HSPICE Simulation Runner for BJT ESD Parameter Extractor
"""

import os
import subprocess
import tempfile
import re
import numpy as np
from models.spice_generator import SpiceGenerator
from data.data_loader import DataLoader

class HspiceRunner:
    """Class for running HSPICE simulations"""

    def __init__(self, hspice_path="hspice"):
        """
        Initialize HSPICE runner

        Args:
            hspice_path (str): Path to HSPICE executable
        """
        self.hspice_path = hspice_path
        self.spice_generator = SpiceGenerator()
        self.data_loader = DataLoader()

    def check_hspice_installation(self):
        """
        Check if HSPICE is properly installed and accessible

        Returns:
            bool: True if HSPICE is available
        """
        try:
            result = subprocess.run([self.hspice_path, "-v"],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            return False

    def run_simulation(self, parameters, voltage_range=(0, 20), step=0.1,
                      work_dir=None, keep_files=True):
        """
        Run HSPICE simulation with given parameters

        Args:
            parameters (dict): Model parameters
            voltage_range (tuple): Voltage sweep range
            step (float): Voltage step size
            work_dir (str): Working directory for simulation files (default: current dir)
            keep_files (bool): Keep simulation files after completion

        Returns:
            dict: Simulation results or None if failed
        """
        if not self.check_hspice_installation():
            raise RuntimeError("HSPICE not found or not properly installed")

        # Use current directory if not specified
        if work_dir is None:
            work_dir = os.getcwd()

        try:
            # Generate netlist
            netlist_content = self.spice_generator.generate_test_netlist(
                parameters, voltage_range, step
            )

            # Write netlist file in current directory
            netlist_file = os.path.join(work_dir, "bjt_esd_simulation.sp")
            with open(netlist_file, 'w') as f:
                f.write(netlist_content)

            # Run HSPICE simulation
            output_file = os.path.join(work_dir, "bjt_esd_simulation.lis")

            cmd = [self.hspice_path, netlist_file, "-o", output_file]

            print(f"Running HSPICE command: {' '.join(cmd)}")
            print(f"Working directory: {work_dir}")
            print(f"Netlist file: {netlist_file}")
            print(f"Output file: {output_file}")

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  cwd=work_dir, timeout=60)

            if result.returncode != 0:
                print(f"HSPICE simulation failed with return code {result.returncode}")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")

                # Save error output for debugging
                error_file = os.path.join(work_dir, "hspice_error.log")
                with open(error_file, 'w') as f:
                    f.write("HSPICE Error Log\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"Command: {' '.join(cmd)}\n")
                    f.write(f"Return code: {result.returncode}\n\n")
                    f.write("STDOUT:\n")
                    f.write(result.stdout)
                    f.write("\n\nSTDERR:\n")
                    f.write(result.stderr)
                print(f"Error log saved: {error_file}")

                return None

            # Parse results
            sim_results = self.parse_hspice_output(output_file)

            # Add file paths to results for debugging
            if sim_results:
                sim_results['netlist_file'] = netlist_file
                sim_results['output_file'] = output_file

            print(f"✓ HSPICE simulation completed successfully")
            print(f"✓ Results available in: {output_file}")

            return sim_results

        except subprocess.TimeoutExpired:
            print("HSPICE simulation timed out")
            return None
        except Exception as e:
            print(f"HSPICE simulation error: {e}")
            return None

    def parse_hspice_output(self, output_file):
        """
        Parse HSPICE output file to extract I-V data

        Args:
            output_file (str): Path to HSPICE .lis output file

        Returns:
            dict: Parsed simulation data
        """
        if not os.path.exists(output_file):
            raise FileNotFoundError(f"HSPICE output file not found: {output_file}")

        voltage = []
        current = []

        try:
            with open(output_file, 'r') as f:
                lines = f.readlines()

            # Find data section
            data_section = False
            header_found = False

            for line in lines:
                line = line.strip()

                # Skip empty lines
                if not line:
                    continue

                # Look for data header
                if 'v(n_anode)' in line.lower() and 'i(vin)' in line.lower():
                    header_found = True
                    data_section = True
                    continue

                # Look for alternative header formats
                if not header_found and ('voltage' in line.lower() or 'v(' in line.lower()):
                    if 'current' in line.lower() or 'i(' in line.lower():
                        header_found = True
                        data_section = True
                        continue

                # Parse data lines
                if data_section and header_found:
                    # Skip lines that don't look like data
                    if line.startswith('*') or line.startswith('#'):
                        continue

                    # Check if this is end of data section
                    if line.startswith('.') or 'job concluded' in line.lower():
                        break

                    # Try to parse voltage and current values
                    try:
                        # Split line and extract numeric values
                        parts = line.split()

                        # Look for two numeric values
                        numeric_parts = []
                        for part in parts:
                            try:
                                # Handle scientific notation
                                val = float(part.replace('D', 'E').replace('d', 'e'))
                                numeric_parts.append(val)
                            except ValueError:
                                continue

                        if len(numeric_parts) >= 2:
                            v = numeric_parts[0]
                            i = abs(numeric_parts[1])  # Take absolute value
                            voltage.append(v)
                            current.append(i)

                    except (ValueError, IndexError):
                        continue

            if not voltage:
                # Try alternative parsing method
                return self.parse_hspice_output_alternative(output_file)

            return {
                'voltage': np.array(voltage),
                'current': np.array(current),
                'file_path': output_file
            }

        except Exception as e:
            raise Exception(f"Error parsing HSPICE output: {e}")

    def parse_hspice_output_alternative(self, output_file):
        """
        Alternative parser for HSPICE output with different formats

        Args:
            output_file (str): Path to HSPICE output file

        Returns:
            dict: Parsed simulation data
        """
        voltage = []
        current = []

        try:
            with open(output_file, 'r') as f:
                content = f.read()

            # Use regex to find voltage and current patterns
            # Pattern for scientific notation: 1.234E-05 or 1.234D-05
            sci_pattern = r'[-+]?\d*\.?\d+[eEdD][-+]?\d+'
            num_pattern = r'[-+]?\d*\.?\d+'

            # Look for lines with two numbers (voltage and current)
            lines = content.split('\n')
            for line in lines:
                # Find all numbers in the line
                numbers = re.findall(f'({sci_pattern}|{num_pattern})', line)

                if len(numbers) >= 2:
                    try:
                        v = float(numbers[0].replace('D', 'E').replace('d', 'e'))
                        i = abs(float(numbers[1].replace('D', 'E').replace('d', 'e')))

                        # Basic sanity check
                        if 0 <= v <= 100 and 0 <= i <= 100:
                            voltage.append(v)
                            current.append(i)
                    except ValueError:
                        continue

            if voltage:
                return {
                    'voltage': np.array(voltage),
                    'current': np.array(current),
                    'file_path': output_file
                }
            else:
                raise ValueError("No valid data found in HSPICE output")

        except Exception as e:
            raise Exception(f"Alternative parsing failed: {e}")

    def run_parameter_sweep(self, base_parameters, sweep_param, sweep_values,
                          voltage_range=(0, 20), step=0.1):
        """
        Run parameter sweep simulation

        Args:
            base_parameters (dict): Base model parameters
            sweep_param (str): Parameter to sweep
            sweep_values (list): Values to sweep
            voltage_range (tuple): Voltage range for each simulation
            step (float): Voltage step size

        Returns:
            list: List of simulation results for each sweep value
        """
        results = []

        for value in sweep_values:
            # Create modified parameters
            params = base_parameters.copy()
            params[sweep_param] = value

            # Run simulation
            sim_result = self.run_simulation(params, voltage_range, step)

            if sim_result:
                sim_result['sweep_param'] = sweep_param
                sim_result['sweep_value'] = value
                results.append(sim_result)
            else:
                print(f"Simulation failed for {sweep_param} = {value}")

        return results

    def cleanup_temp_files(self, temp_dir):
        """Clean up temporary simulation files"""
        try:
            import shutil
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"Warning: Could not clean up temporary files: {e}")

    def save_simulation_script(self, parameters, script_path,
                             voltage_range=(0, 20), step=0.1):
        """
        Save HSPICE simulation script for manual execution

        Args:
            parameters (dict): Model parameters
            script_path (str): Path to save script
            voltage_range (tuple): Voltage range
            step (float): Voltage step
        """
        # Generate netlist
        netlist_content = self.spice_generator.generate_test_netlist(
            parameters, voltage_range, step
        )

        # Save netlist
        netlist_path = script_path.replace('.bat', '.sp').replace('.sh', '.sp')
        with open(netlist_path, 'w') as f:
            f.write(netlist_content)

        # Generate script
        script_content = self.spice_generator.generate_hspice_script(
            netlist_path, os.path.dirname(script_path)
        )

        with open(script_path, 'w') as f:
            f.write(script_content)

        print(f"Simulation script saved to: {script_path}")
        print(f"Netlist saved to: {netlist_path}")
