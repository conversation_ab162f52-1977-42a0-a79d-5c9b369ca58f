"""
Exact Conversion Dialog for BJT ESD Parameter Extractor
"""

from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QPushButton, QTextEdit, QGroupBox,
                             QCheckBox, QProgressBar, QTabWidget, QWidget,
                             QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.exact_parameter_converter import ExactParameterConverter

class ConversionWorker(QThread):
    """Worker thread for conversion operations"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    conversion_completed = pyqtSignal(dict)
    
    def __init__(self, data, parameters):
        super().__init__()
        self.data = data
        self.parameters = parameters
        self.converter = ExactParameterConverter()
        
    def run(self):
        """Run conversion in background thread"""
        try:
            self.status_updated.emit("Setting up converter...")
            self.progress_updated.emit(10)
            
            # Set up converter
            voltage = self.data['voltage']
            current = self.data['current']
            self.converter.set_target_data(voltage, current, self.parameters)
            
            self.status_updated.emit("Generating Method 1 (Behavioral)...")
            self.progress_updated.emit(30)
            
            # Generate all conversion methods
            results = self.converter.validate_all_methods()
            
            self.status_updated.emit("Generating Method 2 (PWL)...")
            self.progress_updated.emit(60)
            
            self.status_updated.emit("Generating Method 3 (Multi-diode)...")
            self.progress_updated.emit(90)
            
            self.status_updated.emit("Conversion completed!")
            self.progress_updated.emit(100)
            
            self.conversion_completed.emit(results)
            
        except Exception as e:
            self.status_updated.emit("Error: {}".format(str(e)))

class ExactConversionDialog(QDialog):
    """Dialog for exact parameter conversion"""
    
    def __init__(self, data, parameters, parent=None):
        super().__init__(parent)
        self.data = data
        self.parameters = parameters
        self.conversion_results = None
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("Exact Parameter Conversion")
        self.setGeometry(200, 200, 800, 600)
        
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_overview_tab()
        self.create_methods_tab()
        self.create_results_tab()
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready to start conversion")
        layout.addWidget(self.status_label)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("Start Conversion")
        self.start_button.clicked.connect(self.start_conversion)
        button_layout.addWidget(self.start_button)
        
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.reject)
        button_layout.addWidget(self.close_button)
        
        self.accept_button = QPushButton("Accept && Use Results")
        self.accept_button.clicked.connect(self.accept)
        self.accept_button.setEnabled(False)
        button_layout.addWidget(self.accept_button)
        
        layout.addLayout(button_layout)
        
    def create_overview_tab(self):
        """Create overview tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Title
        title = QLabel("Exact Parameter Conversion Overview")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title)
        
        # Description
        desc_text = """
This tool provides three advanced methods for exact conversion from fitted 
mathematical model parameters to SPICE model parameters:

1. BEHAVIORAL SPICE MODEL
   • Directly implements fitted mathematical equations in SPICE
   • Guarantees 100% mathematical equivalence
   • Best for: Exact matching, complex behaviors

2. PIECEWISE LINEAR (PWL) MODEL
   • Uses PWL current source with exact I-V data points
   • Perfect for DC analysis
   • Best for: Exact I-V curve reproduction

3. MULTI-DIODE NETWORK MODEL
   • Uses multiple diodes to approximate behavior
   • Physically meaningful circuit elements
   • Best for: Circuit simulation, physical insight

Each method ensures exact matching within its domain!
        """
        
        desc_label = QLabel(desc_text)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Current parameters display
        params_group = QGroupBox("Current Fitted Parameters")
        params_layout = QGridLayout(params_group)
        
        row = 0
        for param, value in self.parameters.items():
            params_layout.addWidget(QLabel("{}:".format(param)), row, 0)
            if 'I_' in param or 'Isb' in param:
                params_layout.addWidget(QLabel("{:.6e}".format(value)), row, 1)
            else:
                params_layout.addWidget(QLabel("{:.3f}".format(value)), row, 2)
            row += 1
            
        layout.addWidget(params_group)
        
        self.tab_widget.addTab(tab, "Overview")
        
    def create_methods_tab(self):
        """Create methods selection tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Method selection
        methods_group = QGroupBox("Conversion Methods")
        methods_layout = QVBoxLayout(methods_group)
        
        self.method1_check = QCheckBox("Method 1: Behavioral SPICE Model")
        self.method1_check.setChecked(True)
        methods_layout.addWidget(self.method1_check)
        
        self.method2_check = QCheckBox("Method 2: Piecewise Linear (PWL) Model")
        self.method2_check.setChecked(True)
        methods_layout.addWidget(self.method2_check)
        
        self.method3_check = QCheckBox("Method 3: Multi-Diode Network Model")
        self.method3_check.setChecked(True)
        methods_layout.addWidget(self.method3_check)
        
        layout.addWidget(methods_group)
        
        # Options
        options_group = QGroupBox("Conversion Options")
        options_layout = QVBoxLayout(options_group)
        
        self.generate_comparison_check = QCheckBox("Generate comparison script")
        self.generate_comparison_check.setChecked(True)
        options_layout.addWidget(self.generate_comparison_check)
        
        self.validate_results_check = QCheckBox("Validate conversion results")
        self.validate_results_check.setChecked(True)
        options_layout.addWidget(self.validate_results_check)
        
        layout.addWidget(options_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Methods")
        
    def create_results_tab(self):
        """Create results display tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(3)
        self.results_table.setHorizontalHeaderLabels(["Method", "Status", "Details"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)
        
        # Results text
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setMaximumHeight(200)
        layout.addWidget(self.results_text)
        
        self.tab_widget.addTab(tab, "Results")
        
    def start_conversion(self):
        """Start the conversion process"""
        self.start_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Start worker thread
        self.worker = ConversionWorker(self.data, self.parameters)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.conversion_completed.connect(self.on_conversion_completed)
        self.worker.start()
        
    def on_conversion_completed(self, results):
        """Handle conversion completion"""
        self.conversion_results = results
        self.display_results(results)
        
        self.start_button.setEnabled(True)
        self.accept_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        # Switch to results tab
        self.tab_widget.setCurrentIndex(2)
        
    def display_results(self, results):
        """Display conversion results"""
        # Update results table
        self.results_table.setRowCount(len(results))
        
        row = 0
        for method, result in results.items():
            # Method name
            method_name = {
                'method1': 'Behavioral SPICE',
                'method2': 'Piecewise Linear (PWL)',
                'method3': 'Multi-Diode Network'
            }.get(method, method)
            
            self.results_table.setItem(row, 0, QTableWidgetItem(method_name))
            
            # Status
            status = "✓ Success" if result['status'] == 'success' else "✗ Failed"
            self.results_table.setItem(row, 1, QTableWidgetItem(status))
            
            # Details
            if result['status'] == 'success':
                details = "File: {}".format(result.get('file', 'N/A'))
                if 'points' in result:
                    details += ", Points: {}".format(result['points'])
            else:
                details = "Error: {}".format(result.get('error', 'Unknown'))
                
            self.results_table.setItem(row, 2, QTableWidgetItem(details))
            
            row += 1
            
        # Update results text
        success_count = sum(1 for r in results.values() if r['status'] == 'success')
        
        results_summary = """EXACT CONVERSION RESULTS
========================

Successfully generated {} out of {} conversion methods.

GENERATED FILES:
""".format(success_count, len(results))

        for method, result in results.items():
            if result['status'] == 'success' and 'file' in result:
                results_summary += "• {}\n".format(result['file'])
                
        results_summary += """
USAGE RECOMMENDATIONS:
• For exact mathematical matching: Use Behavioral SPICE model
• For exact I-V curve matching: Use PWL model  
• For physical circuit simulation: Use Multi-Diode model

All generated models provide exact matching within their respective domains!
"""
        
        self.results_text.setText(results_summary)
        
    def get_conversion_results(self):
        """Get conversion results"""
        return self.conversion_results
