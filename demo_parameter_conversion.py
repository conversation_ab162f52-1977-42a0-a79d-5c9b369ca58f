#!/usr/bin/env python3
"""
BJT ESD Parameter Conversion Demo
Demonstrates how fitted model parameters are converted to SPICE parameters
with validation and optimization for exact matching
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.parameter_converter import ParameterConverter
from data.parameter_extractor import ParameterExtractor

def load_test_data():
    """Load test data from CSV file"""
    print("Loading test data from 1.csv...")
    
    if not os.path.exists("1.csv"):
        print("Error: 1.csv not found!")
        return None, None
    
    try:
        voltage_list = []
        current_list = []
        
        with open("1.csv", "r") as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#') or ',' not in line:
                continue
            if 'Voltage' in line or 'Current' in line:
                continue
            
            try:
                parts = line.split(',')
                if len(parts) >= 2:
                    v = float(parts[0])
                    i = float(parts[1])
                    voltage_list.append(v)
                    current_list.append(abs(i))
            except ValueError:
                continue
        
        voltage = np.array(voltage_list)
        current = np.array(current_list)
        
        print("✓ Loaded {} data points".format(len(voltage)))
        print("  Voltage range: {:.3f} to {:.3f} V".format(voltage.min(), voltage.max()))
        print("  Current range: {:.3e} to {:.3e} A".format(current.min(), current.max()))
        
        return voltage, current
        
    except Exception as e:
        print("Error loading data: {}".format(e))
        return None, None

def extract_fitted_parameters(voltage, current):
    """Extract fitted model parameters"""
    print("\nExtracting fitted model parameters...")
    
    try:
        extractor = ParameterExtractor()
        data = {'voltage': voltage, 'current': current}
        parameters = extractor.extract_parameters(data)
        
        print("✓ Fitted parameters extracted:")
        for param, value in parameters.items():
            if 'I_' in param:
                print("  {}: {:.6e}".format(param, value))
            else:
                print("  {}: {:.3f}".format(param, value))
        
        return parameters
        
    except Exception as e:
        print("Error extracting parameters: {}".format(e))
        return None

def demonstrate_conversion_methods(voltage, current, fitted_params):
    """Demonstrate different parameter conversion methods"""
    print("\n" + "="*60)
    print("PARAMETER CONVERSION METHODS COMPARISON")
    print("="*60)
    
    # Initialize converter
    converter = ParameterConverter()
    converter.set_fitted_model(fitted_params, voltage, current)
    
    # Method 1: Direct conversion
    print("\n1. DIRECT CONVERSION (Empirical Mapping)")
    print("-" * 40)
    
    direct_params = converter.convert_to_spice_parameters(method='direct')
    direct_validation = converter.validate_conversion(direct_params)
    
    print("SPICE Parameters (Direct):")
    for param, value in direct_params.items():
        if param in ['IS', 'IBV']:
            print("  {}: {:.6e}".format(param, value))
        else:
            print("  {}: {:.6f}".format(param, value))
    
    print("\nValidation Results (Direct):")
    print("  Max Log Error: {:.4f}".format(direct_validation['max_log_error']))
    print("  Mean Log Error: {:.4f}".format(direct_validation['mean_log_error']))
    print("  Within Tolerance: {}".format(direct_validation['within_tolerance']))
    
    # Method 2: Optimization-based conversion
    print("\n2. OPTIMIZATION-BASED CONVERSION (Exact Matching)")
    print("-" * 50)
    
    opt_params = converter.convert_to_spice_parameters(method='optimization')
    opt_validation = converter.validate_conversion(opt_params)
    
    print("SPICE Parameters (Optimized):")
    for param, value in opt_params.items():
        if param in ['IS', 'IBV']:
            print("  {}: {:.6e}".format(param, value))
        else:
            print("  {}: {:.6f}".format(param, value))
    
    print("\nValidation Results (Optimized):")
    print("  Max Log Error: {:.4f}".format(opt_validation['max_log_error']))
    print("  Mean Log Error: {:.4f}".format(opt_validation['mean_log_error']))
    print("  Within Tolerance: {}".format(opt_validation['within_tolerance']))
    
    # Improvement analysis
    improvement = (direct_validation['max_log_error'] - opt_validation['max_log_error']) / direct_validation['max_log_error'] * 100
    print("\nImprovement: {:.1f}% reduction in max log error".format(improvement))
    
    return direct_params, opt_params, direct_validation, opt_validation

def plot_comparison(voltage, current, fitted_params, direct_params, opt_params, 
                   direct_validation, opt_validation):
    """Plot comparison of fitted model vs SPICE models"""
    print("\nGenerating comparison plots...")
    
    try:
        converter = ParameterConverter()
        
        # Calculate currents from all models
        fitted_current = converter.calculate_fitted_current(voltage, fitted_params)
        direct_spice_current = direct_validation['spice_current']
        opt_spice_current = opt_validation['spice_current']
        
        # Create comparison plot
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Plot 1: I-V Characteristics
        ax1.semilogy(voltage, current, 'ko-', markersize=3, linewidth=1, 
                    label='Measurement Data', alpha=0.7)
        ax1.semilogy(voltage, fitted_current, 'r-', linewidth=2, 
                    label='Fitted Model', alpha=0.9)
        ax1.semilogy(voltage, direct_spice_current, 'b--', linewidth=2, 
                    label='SPICE (Direct)', alpha=0.8)
        ax1.semilogy(voltage, opt_spice_current, 'g-', linewidth=2, 
                    label='SPICE (Optimized)', alpha=0.9)
        
        ax1.set_xlabel('Voltage (V)')
        ax1.set_ylabel('Current (A)')
        ax1.set_title('BJT ESD Model Comparison: Fitted vs SPICE')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_xlim(0, voltage.max())
        ax1.set_ylim(1e-12, current.max() * 10)
        
        # Plot 2: Error Analysis
        direct_error = direct_validation['log_error']
        opt_error = opt_validation['log_error']
        
        ax2.semilogy(voltage, direct_error, 'b-', linewidth=2, 
                    label='Direct Conversion Error')
        ax2.semilogy(voltage, opt_error, 'g-', linewidth=2, 
                    label='Optimized Conversion Error')
        ax2.axhline(y=0.1, color='r', linestyle='--', alpha=0.7, 
                   label='Tolerance (10%)')
        
        ax2.set_xlabel('Voltage (V)')
        ax2.set_ylabel('Log Error')
        ax2.set_title('Parameter Conversion Error Analysis')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_xlim(0, voltage.max())
        
        plt.tight_layout()
        plt.savefig('parameter_conversion_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✓ Comparison plot saved as parameter_conversion_comparison.png")
        
    except Exception as e:
        print("Error creating plots: {}".format(e))

def generate_optimized_spice_model(voltage, current, fitted_params):
    """Generate optimized SPICE model with validation"""
    print("\nGenerating optimized SPICE model...")
    
    try:
        converter = ParameterConverter()
        converter.set_fitted_model(fitted_params, voltage, current)
        
        # Generate optimized SPICE model
        spice_content, validation = converter.generate_optimized_spice_model()
        
        # Save to file
        with open("bjt_esd_optimized.ckt", "w") as f:
            f.write(spice_content)
        
        print("✓ Optimized SPICE model saved to bjt_esd_optimized.ckt")
        print("✓ Model validation:")
        print("  Max Log Error: {:.4f}".format(validation['max_log_error']))
        print("  Mean Log Error: {:.4f}".format(validation['mean_log_error']))
        print("  Within Tolerance: {}".format(validation['within_tolerance']))
        
        if not validation['within_tolerance']:
            print("  Warning: Model may not meet accuracy requirements")
            if len(validation['problem_voltages']) > 0:
                print("  Problem voltage range: {:.2f} - {:.2f} V".format(
                    validation['problem_voltages'].min(),
                    validation['problem_voltages'].max()
                ))
        
        return True
        
    except Exception as e:
        print("Error generating optimized SPICE model: {}".format(e))
        return False

def main():
    """Main demonstration function"""
    print("BJT ESD Parameter Conversion Demo")
    print("=" * 50)
    print("This demo shows how fitted model parameters are converted")
    print("to SPICE parameters with validation and optimization")
    print()
    
    # Load test data
    voltage, current = load_test_data()
    if voltage is None:
        return False
    
    # Extract fitted parameters
    fitted_params = extract_fitted_parameters(voltage, current)
    if fitted_params is None:
        return False
    
    # Demonstrate conversion methods
    direct_params, opt_params, direct_validation, opt_validation = \
        demonstrate_conversion_methods(voltage, current, fitted_params)
    
    # Plot comparison
    plot_comparison(voltage, current, fitted_params, direct_params, opt_params,
                   direct_validation, opt_validation)
    
    # Generate optimized SPICE model
    success = generate_optimized_spice_model(voltage, current, fitted_params)
    
    print("\n" + "="*60)
    print("SUMMARY: FITTED MODEL TO SPICE CONVERSION")
    print("="*60)
    print("\n✓ Key Points:")
    print("  1. Direct conversion uses empirical parameter mapping")
    print("  2. Optimization ensures exact matching between models")
    print("  3. Validation quantifies conversion accuracy")
    print("  4. Optimized SPICE model maintains physical behavior")
    
    print("\n✓ Generated Files:")
    print("  - bjt_esd_optimized.ckt (Optimized SPICE model)")
    print("  - parameter_conversion_comparison.png (Comparison plot)")
    
    print("\n✓ Next Steps:")
    print("  1. Run HSPICE simulation: hspice bjt_esd_optimized.ckt -o results.lis")
    print("  2. Compare simulation results with fitted model")
    print("  3. Use optimized model in circuit design")
    
    return success

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
