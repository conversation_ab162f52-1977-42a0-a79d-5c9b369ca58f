#!/usr/bin/env python3
"""
Test script for HSPICE interface
"""

import sys
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.hspice_interface import HSPICEInterface
from models.bjt_esd_model import BJTESDModel

def test_hspice_installation():
    """Test HSPICE installation"""
    print("Testing HSPICE installation...")
    
    hspice = HSPICEInterface()
    
    if hspice.verify_hspice_installation():
        print("✓ HSPICE is installed and accessible")
        return True
    else:
        print("✗ HSPICE is not installed or not in PATH")
        return False

def test_spice_model_generation():
    """Test SPICE model generation"""
    print("\nTesting SPICE model generation...")
    
    try:
        model = BJTESDModel()
        hspice = HSPICEInterface()
        
        parameters = model.get_parameters()
        
        # Generate SPICE model
        model_content = hspice.generate_spice_model(parameters)
        print("✓ SPICE model generated successfully")
        
        # Save to file
        output_file = "test_bjt_esd_model.sp"
        success = hspice.save_spice_model(parameters, output_file)
        
        if success and os.path.exists(output_file):
            print(f"✓ SPICE model saved to {output_file}")
            
            # Show first few lines
            with open(output_file, 'r') as f:
                lines = f.readlines()[:10]
            print("First 10 lines of generated model:")
            for i, line in enumerate(lines, 1):
                print(f"  {i}: {line.strip()}")
            
            return True
        else:
            print("✗ Failed to save SPICE model")
            return False
            
    except Exception as e:
        print(f"✗ SPICE model generation failed: {e}")
        return False

def test_netlist_generation():
    """Test HSPICE netlist generation"""
    print("\nTesting HSPICE netlist generation...")
    
    try:
        model = BJTESDModel()
        hspice = HSPICEInterface()
        
        parameters = model.get_parameters()
        
        # Generate netlist
        netlist = hspice.generate_netlist(parameters, (0, 20), 50)
        print("✓ HSPICE netlist generated successfully")
        
        # Save to file
        output_file = "test_bjt_esd_simulation.sp"
        success = hspice.save_netlist(parameters, output_file, (0, 20), 50)
        
        if success and os.path.exists(output_file):
            print(f"✓ HSPICE netlist saved to {output_file}")
            
            # Show first few lines
            with open(output_file, 'r') as f:
                lines = f.readlines()[:15]
            print("First 15 lines of generated netlist:")
            for i, line in enumerate(lines, 1):
                print(f"  {i}: {line.strip()}")
            
            return True
        else:
            print("✗ Failed to save HSPICE netlist")
            return False
            
    except Exception as e:
        print(f"✗ HSPICE netlist generation failed: {e}")
        return False

def test_hspice_simulation():
    """Test HSPICE simulation (if HSPICE is available)"""
    print("\nTesting HSPICE simulation...")
    
    try:
        model = BJTESDModel()
        hspice = HSPICEInterface()
        
        if not hspice.verify_hspice_installation():
            print("! HSPICE not available, skipping simulation test")
            return True
        
        parameters = model.get_parameters()
        
        print("Running HSPICE simulation...")
        result = hspice.run_simulation(parameters, (0, 15), 20)
        
        if result:
            voltage, current = result
            print(f"✓ HSPICE simulation completed successfully")
            print(f"  Generated {len(voltage)} data points")
            print(f"  Voltage range: {voltage.min():.3f} to {voltage.max():.3f} V")
            print(f"  Current range: {current.min():.3e} to {current.max():.3e} A")
            
            # Show first few points
            print("First 5 simulation points:")
            for i in range(min(5, len(voltage))):
                print(f"  V={voltage[i]:.3f}V, I={current[i]:.3e}A")
            
            return True
        else:
            print("✗ HSPICE simulation failed")
            
            # Show temp files for debugging
            temp_files = hspice.get_temp_files()
            if temp_files:
                print("Available temporary files:")
                for file_type, file_path in temp_files.items():
                    print(f"  {file_type}: {file_path}")
            
            return False
            
    except Exception as e:
        print(f"✗ HSPICE simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all HSPICE tests"""
    print("BJT ESD Parameter Extractor - HSPICE Interface Test")
    print("=" * 55)
    
    tests = [
        test_hspice_installation,
        test_spice_model_generation,
        test_netlist_generation,
        test_hspice_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 55)
    print(f"HSPICE Test Results: {passed}/{total} tests passed")
    
    if passed >= 3:  # Allow simulation test to fail if HSPICE not available
        print("✓ HSPICE interface is working correctly!")
        if passed < total:
            print("Note: Some tests failed, but core functionality is working.")
    else:
        print("✗ HSPICE interface has issues. Please check the error messages above.")
    
    print("\nGenerated files:")
    for filename in ["test_bjt_esd_model.sp", "test_bjt_esd_simulation.sp"]:
        if os.path.exists(filename):
            print(f"  {filename} - {os.path.getsize(filename)} bytes")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
