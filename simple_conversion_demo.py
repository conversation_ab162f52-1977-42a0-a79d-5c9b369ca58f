#!/usr/bin/env python3
"""
Simple Parameter Conversion Demo
Shows the basic concept of fitted model to SPICE parameter conversion
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

def load_test_data():
    """Load test data"""
    print("Loading test data from 1.csv...")
    
    voltage_list = []
    current_list = []
    
    with open("1.csv", "r") as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('#') or ',' not in line:
            continue
        if 'Voltage' in line or 'Current' in line:
            continue
        
        try:
            parts = line.split(',')
            if len(parts) >= 2:
                v = float(parts[0])
                i = float(parts[1])
                voltage_list.append(v)
                current_list.append(abs(i))
        except ValueError:
            continue
    
    voltage = np.array(voltage_list)
    current = np.array(current_list)
    
    print("✓ Loaded {} data points".format(len(voltage)))
    return voltage, current

def extract_simple_parameters(voltage, current):
    """Extract simple fitted parameters"""
    print("\nExtracting fitted parameters...")
    
    # Simple parameter extraction
    i_leak = np.mean(current[:20])
    
    # Find trigger point
    log_current = np.log10(current + 1e-15)
    d_log_i = np.gradient(log_current, voltage)
    trigger_idx = np.argmax(d_log_i)
    vt1 = voltage[trigger_idx]
    
    # Find holding voltage
    vh_idx = len(voltage) - 1
    for i, curr in enumerate(current):
        if curr > 1e-3:
            vh_idx = i
            break
    vh = voltage[vh_idx]
    
    # Simple resistance
    if vh_idx < len(voltage) - 5:
        v_end = voltage[-5:]
        i_end = current[-5:]
        slope = (i_end[-1] - i_end[0]) / (v_end[-1] - v_end[0])
        ron = 1.0 / slope if slope > 0 else 2.0
    else:
        ron = 2.0
    
    parameters = {
        'I_leak': i_leak,
        'Vt1': vt1,
        'k': 3.0,
        'Ron': ron,
        'Vh': vh,
        'I_offset': 0.05,
        'Isb': 0.04,
        'Vsb': vh
    }
    
    print("✓ Fitted parameters:")
    for param, value in parameters.items():
        if 'I_' in param:
            print("  {}: {:.6e}".format(param, value))
        else:
            print("  {}: {:.3f}".format(param, value))
    
    return parameters

def calculate_fitted_current(voltage, params):
    """Calculate current using fitted model"""
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < params['Vt1']:
            current[i] = params['I_leak'] * np.exp(v / 1.0)
        elif v < params['Vh']:
            current[i] = params['I_leak'] * np.exp(params['k'] * (v - params['Vt1']) / params['Vt1'])
        else:
            linear_term = (v - params['Vsb']) / params['Ron']
            exp_term = params['Isb'] * np.exp(-(v - params['Vsb']))
            current[i] = params['I_offset'] + linear_term + exp_term
    
    return current

def convert_to_spice_direct(fitted_params):
    """Direct conversion to SPICE parameters"""
    print("\nDirect parameter conversion:")
    
    spice_params = {
        'IS': fitted_params['I_leak'] * 1e6,
        'N': fitted_params['k'],
        'RS': 0.1,
        'BV': fitted_params['Vh'] * 1.05,
        'IBV': fitted_params['I_offset'] * 0.1,
        'R_parallel': fitted_params['Ron']
    }
    
    print("SPICE parameters:")
    for param, value in spice_params.items():
        if param in ['IS', 'IBV']:
            print("  {}: {:.6e}".format(param, value))
        else:
            print("  {}: {:.6f}".format(param, value))
    
    return spice_params

def calculate_spice_current_simple(voltage, spice_params):
    """Simple SPICE current calculation"""
    IS = spice_params['IS']
    N = spice_params['N']
    BV = spice_params['BV']
    R_parallel = spice_params['R_parallel']
    
    Vt = 0.026  # Thermal voltage
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < 0:
            current[i] = IS * 1e-6
        elif v < BV:
            # Forward diode
            exp_arg = min(v / (N * Vt), 50)  # Prevent overflow
            i_diode = IS * (np.exp(exp_arg) - 1)
            i_parallel = v / R_parallel if R_parallel > 0 else 0
            current[i] = i_diode + i_parallel
        else:
            # Breakdown region
            i_diode = IS * (np.exp(BV / (N * Vt)) - 1)
            i_parallel = v / R_parallel
            current[i] = i_diode + i_parallel
    
    return current

def compare_models(voltage, current, fitted_params, spice_params):
    """Compare fitted and SPICE models"""
    print("\nComparing models...")
    
    fitted_current = calculate_fitted_current(voltage, fitted_params)
    spice_current = calculate_spice_current_simple(voltage, spice_params)
    
    # Calculate simple error metrics
    relative_error = np.abs(fitted_current - spice_current) / (fitted_current + 1e-15)
    log_error = np.abs(np.log10(fitted_current + 1e-15) - np.log10(spice_current + 1e-15))
    
    # Remove invalid values
    valid_mask = np.isfinite(log_error) & np.isfinite(relative_error)
    if np.any(valid_mask):
        max_log_error = np.max(log_error[valid_mask])
        mean_log_error = np.mean(log_error[valid_mask])
    else:
        max_log_error = 1.0
        mean_log_error = 1.0
    
    print("✓ Comparison results:")
    print("  Max log error: {:.4f}".format(max_log_error))
    print("  Mean log error: {:.4f}".format(mean_log_error))
    
    if max_log_error < 0.1:
        print("  Quality: Excellent")
    elif max_log_error < 0.2:
        print("  Quality: Good")
    else:
        print("  Quality: Needs improvement")
    
    return fitted_current, spice_current, max_log_error

def plot_comparison(voltage, measured, fitted_current, spice_current):
    """Plot model comparison"""
    print("\nGenerating comparison plot...")
    
    try:
        plt.figure(figsize=(12, 8))
        
        plt.semilogy(voltage, measured, 'ko-', markersize=3, linewidth=1,
                    label='Measurement Data', alpha=0.7)
        plt.semilogy(voltage, fitted_current, 'r-', linewidth=2,
                    label='Fitted Model', alpha=0.9)
        plt.semilogy(voltage, spice_current, 'b--', linewidth=2,
                    label='SPICE Model', alpha=0.8)
        
        plt.xlabel('Voltage (V)')
        plt.ylabel('Current (A)')
        plt.title('BJT ESD Model Comparison: Fitted vs SPICE')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, voltage.max())
        plt.ylim(1e-12, measured.max() * 10)
        
        plt.savefig('simple_model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✓ Plot saved as simple_model_comparison.png")
        
    except Exception as e:
        print("Error creating plot: {}".format(e))

def generate_spice_model(fitted_params, spice_params):
    """Generate SPICE model file"""
    print("\nGenerating SPICE model...")
    
    spice_content = """* BJT ESD Device SPICE Model
* Generated by Simple Parameter Conversion Demo

* Original Fitted Parameters:
* I_leak = {:.6e} A
* Vt1 = {:.3f} V
* k = {:.3f}
* Ron = {:.3f} Ohm
* Vh = {:.3f} V

* Converted SPICE Parameters:
* IS = {:.6e} A
* N = {:.3f}
* BV = {:.3f} V
* R_parallel = {:.3f} Ohm

.subckt bjt_esd_simple anode cathode
* Main ESD diode
D_main anode n_main D_ESD_SIMPLE
R_main n_main cathode 0.1

* Parallel resistance
R_parallel anode cathode {:.3f}

* Diode model
.model D_ESD_SIMPLE D(
+ IS={:.6e}
+ N={:.3f}
+ RS=0.1
+ BV={:.3f}
+ IBV={:.6e}
+ CJO=1e-12
+ TT=1e-12
+ )

.ends bjt_esd_simple

* Test circuit
.subckt test_simple
Vin n1 0 DC 0
X_esd n1 0 bjt_esd_simple
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.ends test_simple

.end
""".format(
        fitted_params['I_leak'],
        fitted_params['Vt1'],
        fitted_params['k'],
        fitted_params['Ron'],
        fitted_params['Vh'],
        spice_params['IS'],
        spice_params['N'],
        spice_params['BV'],
        spice_params['R_parallel'],
        spice_params['R_parallel'],
        spice_params['IS'],
        spice_params['N'],
        spice_params['BV'],
        spice_params['IBV']
    )
    
    with open("bjt_esd_converted.ckt", "w") as f:
        f.write(spice_content)
    
    print("✓ SPICE model saved to bjt_esd_converted.ckt")

def main():
    """Main function"""
    print("Simple Parameter Conversion Demo")
    print("=" * 40)
    print("Shows how fitted model parameters are converted to SPICE parameters")
    print()
    
    # Load data
    voltage, current = load_test_data()
    
    # Extract fitted parameters
    fitted_params = extract_simple_parameters(voltage, current)
    
    # Convert to SPICE parameters
    spice_params = convert_to_spice_direct(fitted_params)
    
    # Compare models
    fitted_current, spice_current, error = compare_models(voltage, current, fitted_params, spice_params)
    
    # Plot comparison
    plot_comparison(voltage, current, fitted_current, spice_current)
    
    # Generate SPICE model
    generate_spice_model(fitted_params, spice_params)
    
    print("\n" + "=" * 40)
    print("CONVERSION SUMMARY")
    print("=" * 40)
    print("✓ Fitted model parameters extracted from measurement data")
    print("✓ Parameters converted to SPICE model format")
    print("✓ Conversion quality: Max log error = {:.4f}".format(error))
    print("✓ SPICE model generated and ready for simulation")
    
    print("\nKey conversion relationships:")
    print("  Fitted I_leak → SPICE IS (scaled by 1e6)")
    print("  Fitted k → SPICE N (ideality factor)")
    print("  Fitted Vh → SPICE BV (breakdown voltage)")
    print("  Fitted Ron → SPICE R_parallel (resistance)")
    
    print("\nNext steps:")
    print("  1. Test SPICE model: hspice bjt_esd_converted.ckt -o test.lis")
    print("  2. Compare simulation results with fitted model")
    print("  3. Refine conversion if needed")
    
    return True

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
