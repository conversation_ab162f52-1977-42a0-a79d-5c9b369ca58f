#!/usr/bin/env python3
"""
Create an exact matching HSPICE model using behavioral modeling
"""

import sys
import os
import numpy as np

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_exact_match_model():
    """Create HSPICE model that exactly matches the fitted curve"""
    try:
        from utils.data_loader import DataLoader
        from models.bjt_esd_model import BJTESDModel
        
        if not os.path.exists('1.csv'):
            print("No experimental data file found")
            return False
        
        # Load and fit data
        loader = DataLoader()
        voltage_exp, current_exp, _ = loader.load_data('1.csv')
        
        model = BJTESDModel()
        fitted_params = model.fit_parameters(voltage_exp, current_exp)
        
        print("Creating exact match HSPICE model...")
        print(f"Fitted R² = {fitted_params.get('r_squared', 0):.6f}")
        
        # Generate high-resolution fitted curve
        voltage_points = np.linspace(0, 20, 201)  # 201 points for high resolution
        current_points = model.current_equation(voltage_points, **fitted_params)
        
        # Method 1: Behavioral current source with mathematical equation
        model_content_behavioral = create_behavioral_model(fitted_params)
        
        # Method 2: PWL lookup table with dense points
        model_content_pwl = create_pwl_model(voltage_points, current_points, fitted_params)
        
        # Method 3: Verilog-A model (most accurate)
        model_content_verilog = create_verilog_a_model(fitted_params)
        
        # Save all three models
        models = [
            ('exact_match_behavioral.ckt', model_content_behavioral),
            ('exact_match_pwl.ckt', model_content_pwl),
            ('exact_match_verilog.va', model_content_verilog)
        ]
        
        for filename, content in models:
            with open(filename, 'w') as f:
                f.write(content)
            print(f"✓ Created {filename}")
        
        # Create test netlists
        create_test_netlists()
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_behavioral_model(fitted_params):
    """Create behavioral model using HSPICE B-element"""
    
    # Extract parameters
    I_leak = fitted_params.get('I_leak', 1e-7)
    Vt1 = fitted_params.get('Vt1', 12.6)
    k = fitted_params.get('k', 3.0)
    Vh = fitted_params.get('Vh', 13.9)
    Ron = fitted_params.get('Ron', 2.0)
    I_offset = fitted_params.get('I_offset', 0.05)
    
    model_content = f"""* BJT ESD Device SPICE Model - EXACT MATCH BEHAVIORAL VERSION
* Uses HSPICE B-element to implement the exact fitted equation
* Date: 2025-06-06

* Fitted Parameters:
* I_leak = {I_leak:.6e} A
* Vt1 = {Vt1:.3f} V  
* k = {k:.3f}
* Vh = {Vh:.3f} V
* Ron = {Ron:.3f} Ohm
* I_offset = {I_offset:.6f} A
* R² = {fitted_params.get('r_squared', 0):.6f}

.subckt bjt_esd_device anode cathode

* Behavioral current source implementing the exact fitted equation
* Region 1: V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2: Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)  
* Region 3: V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

.param I_leak={I_leak:.6e}
.param Vt1={Vt1:.3f}
.param k={k:.3f}
.param Vh={Vh:.3f}
.param Ron={Ron:.3f}
.param I_offset={I_offset:.6f}
.param Vsb={{Vh*0.9}}
.param Isb={{I_offset*0.5}}

Besd anode cathode I='
+ (V(anode,cathode) < Vt1) ? I_leak * exp(V(anode,cathode)/Vt1) :
+ (V(anode,cathode) < Vh) ? I_leak * exp(k*(V(anode,cathode)-Vt1)/Vt1) :
+ I_offset + (V(anode,cathode)-Vsb)/Ron + Isb*exp(-(V(anode,cathode)-Vsb))'

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device"""
    
    return model_content

def create_pwl_model(voltage_points, current_points, fitted_params):
    """Create PWL lookup table model"""
    
    # Create PWL pairs
    pwl_pairs = []
    for v, i in zip(voltage_points, current_points):
        pwl_pairs.append(f"({v:.6f},{i:.6e})")
    
    # Split into multiple lines for readability
    pwl_lines = []
    pairs_per_line = 5
    for i in range(0, len(pwl_pairs), pairs_per_line):
        line_pairs = pwl_pairs[i:i+pairs_per_line]
        if i == 0:
            pwl_lines.append("Gesd anode cathode PWL(1) V(anode,cathode) " + " ".join(line_pairs))
        else:
            pwl_lines.append("+ " + " ".join(line_pairs))
    
    model_content = f"""* BJT ESD Device SPICE Model - EXACT MATCH PWL VERSION
* Uses PWL lookup table with {len(voltage_points)} points for exact matching
* Date: 2025-06-06

* Fitted Parameters:
* R² = {fitted_params.get('r_squared', 0):.6f}

.subckt bjt_esd_device anode cathode

* PWL current source with exact fitted data points
* {len(voltage_points)} points from 0V to 20V
{chr(10).join(pwl_lines)}

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device"""
    
    return model_content

def create_verilog_a_model(fitted_params):
    """Create Verilog-A model for perfect matching"""
    
    # Extract parameters
    I_leak = fitted_params.get('I_leak', 1e-7)
    Vt1 = fitted_params.get('Vt1', 12.6)
    k = fitted_params.get('k', 3.0)
    Vh = fitted_params.get('Vh', 13.9)
    Ron = fitted_params.get('Ron', 2.0)
    I_offset = fitted_params.get('I_offset', 0.05)
    
    model_content = f"""// BJT ESD Device Verilog-A Model - EXACT MATCH VERSION
// Implements the exact fitted equation for perfect matching
// Date: 2025-06-06

// Fitted Parameters:
// I_leak = {I_leak:.6e} A
// Vt1 = {Vt1:.3f} V  
// k = {k:.3f}
// Vh = {Vh:.3f} V
// Ron = {Ron:.3f} Ohm
// I_offset = {I_offset:.6f} A
// R² = {fitted_params.get('r_squared', 0):.6f}

`include "constants.vams"
`include "disciplines.vams"

module bjt_esd_device(anode, cathode);
    inout anode, cathode;
    electrical anode, cathode;
    
    // Model parameters
    parameter real I_leak = {I_leak:.6e};
    parameter real Vt1 = {Vt1:.3f};
    parameter real k = {k:.3f};
    parameter real Vh = {Vh:.3f};
    parameter real Ron = {Ron:.3f};
    parameter real I_offset = {I_offset:.6f};
    parameter real Vsb = {Vh*0.9:.3f};
    parameter real Isb = {I_offset*0.5:.6f};
    
    real v, i;
    
    analog begin
        v = V(anode, cathode);
        
        // Implement the exact fitted equation
        if (v < Vt1) begin
            // Region 1: Leakage
            i = I_leak * exp(v/Vt1);
        end else if (v < Vh) begin
            // Region 2: Trigger
            i = I_leak * exp(k*(v-Vt1)/Vt1);
        end else begin
            // Region 3: Snapback
            i = I_offset + (v-Vsb)/Ron + Isb*exp(-(v-Vsb));
        end
        
        // Current contribution
        I(anode, cathode) <+ i;
    end
endmodule"""
    
    return model_content

def create_test_netlists():
    """Create test netlists for all models"""
    
    netlists = [
        ('test_behavioral.sp', 'exact_match_behavioral.ckt'),
        ('test_pwl.sp', 'exact_match_pwl.ckt')
    ]
    
    for netlist_name, model_file in netlists:
        netlist_content = f"""* Test netlist for exact match model
* Date: 2025-06-06

.title BJT ESD Device I-V Characteristics - Exact Match Test

.include '{model_file}'

* Voltage source for DC sweep
Vin n1 0 DC 0

* Instantiate ESD device
Xesd n1 0 bjt_esd_device

* DC Analysis
.dc Vin 0 20 0.1

* Output commands
.print dc V(n1) I(Vin)
.probe dc V(n1) I(Vin)

* Simulation options
.option post=2
.option gmin=1e-15
.option reltol=1e-6
.option abstol=1e-15

.end"""
        
        with open(netlist_name, 'w') as f:
            f.write(netlist_content)
        print(f"✓ Created {netlist_name}")

def test_exact_match_models():
    """Test the exact match models"""
    try:
        from utils.hspice_interface import HSPICEInterface
        
        hspice = HSPICEInterface()
        if not hspice.verify_hspice_installation():
            print("HSPICE not available for testing")
            return False
        
        print(f"\nTesting exact match models...")
        
        # Test behavioral model
        print(f"Testing behavioral model...")
        result_behavioral = hspice.run_simulation_from_netlist('test_behavioral.sp')
        
        if result_behavioral:
            voltage_sim, current_sim = result_behavioral
            print(f"✓ Behavioral model simulation successful: {len(voltage_sim)} points")
            
            # Load fitted curve for comparison
            from utils.data_loader import DataLoader
            from models.bjt_esd_model import BJTESDModel
            
            loader = DataLoader()
            voltage_exp, current_exp, _ = loader.load_data('1.csv')
            
            model = BJTESDModel()
            fitted_params = model.fit_parameters(voltage_exp, current_exp)
            
            voltage_fitted = np.linspace(0, 20, 100)
            current_fitted = model.current_equation(voltage_fitted, **fitted_params)
            
            # Compare at key points
            print(f"Comparison with fitted curve:")
            test_voltages = [5, 10, 15, 20]
            total_error = 0
            
            for v_test in test_voltages:
                i_fitted = np.interp(v_test, voltage_fitted, current_fitted)
                i_sim = np.interp(v_test, voltage_sim, current_sim)
                
                if i_fitted > 1e-9:
                    error = abs(i_sim - i_fitted) / i_fitted * 100
                    total_error += error
                    print(f"  {v_test:2.0f}V: Fitted={i_fitted:.6f}A, HSPICE={i_sim:.6f}A, Error={error:.3f}%")
                else:
                    print(f"  {v_test:2.0f}V: Fitted={i_fitted:.9f}A, HSPICE={i_sim:.9f}A")
            
            avg_error = total_error / len(test_voltages)
            print(f"Average error: {avg_error:.3f}%")
            
            if avg_error < 1.0:
                print("✓ Excellent matching achieved!")
                return True
            elif avg_error < 5.0:
                print("✓ Good matching achieved!")
                return True
            else:
                print("⚠ Matching needs improvement")
                return False
        else:
            print("✗ Behavioral model simulation failed")
            return False
            
    except Exception as e:
        print(f"Error testing models: {e}")
        return False

def main():
    """Main function"""
    print("BJT ESD Parameter Extractor - Exact Match Model Creator")
    print("=" * 60)
    
    success = create_exact_match_model()
    
    if success:
        print(f"\n✓ Exact match models created successfully")
        
        # Test the models
        test_success = test_exact_match_models()
        
        print(f"\nGenerated files:")
        files = [
            'exact_match_behavioral.ckt',
            'exact_match_pwl.ckt', 
            'exact_match_verilog.va',
            'test_behavioral.sp',
            'test_pwl.sp'
        ]
        
        for filename in files:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"  {filename} - {size} bytes")
        
        print(f"\nRecommendations:")
        print(f"1. Use behavioral model (.ckt) for HSPICE compatibility")
        print(f"2. Use Verilog-A model (.va) for maximum accuracy")
        print(f"3. Update main program to use exact match model")
        
        if test_success:
            print(f"\n🎯 EXACT MATCHING ACHIEVED!")
            print(f"The behavioral model should provide <1% error vs fitted curve")
        
    else:
        print(f"\n✗ Failed to create exact match models")
    
    return success

if __name__ == "__main__":
    main()
