@echo off
echo Comparing BJT ESD Model Conversion Methods
echo ==========================================

echo.
echo Method 1: Behavioral Model
if exist bjt_esd_method1_behavioral.ckt (
    echo Running behavioral model simulation...
    hspice bjt_esd_method1_behavioral.ckt -o method1_results.lis
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Method 1 simulation completed
    ) else (
        echo [FAIL] Method 1 simulation failed
    )
) else (
    echo [FAIL] Method 1 file not found
)

echo.
echo Method 2: PWL Model
if exist bjt_esd_method2_pwl.ckt (
    echo Running PWL model simulation...
    hspice bjt_esd_method2_pwl.ckt -o method2_results.lis
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Method 2 simulation completed
    ) else (
        echo [FAIL] Method 2 simulation failed
    )
) else (
    echo [FAIL] Method 2 file not found
)

echo.
echo Method 3: Multi-Diode Model
if exist bjt_esd_method3_multidiode.ckt (
    echo Running multi-diode model simulation...
    hspice bjt_esd_method3_multidiode.ckt -o method3_results.lis
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Method 3 simulation completed
    ) else (
        echo [FAIL] Method 3 simulation failed
    )
) else (
    echo [FAIL] Method 3 file not found
)

echo.
echo ==========================================
echo All simulations completed!
echo Check .lis files for results
echo ==========================================

pause
