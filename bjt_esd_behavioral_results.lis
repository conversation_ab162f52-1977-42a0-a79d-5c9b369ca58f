 ****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******                    
  Copyright (c) 1986 - 2025 by Synopsys, Inc. All Rights Reserved.              
  This software and the associated documentation are proprietary
  to Synopsys, Inc. This software may only be used in accordance
  with the terms and conditions of a written license agreement with
  Synopsys, Inc. All other use, reproduction, or distribution of
  this software is strictly prohibited.
  Input File: bjt_esd_behavioral.ckt                                            
  Command line options: C:\synopsys\Hspice_P-2019.06-SP1-1\WIN64\hspice.com bjt_esd_behavioral.ckt -o bjt_esd_behavioral_results.lis
  Start time: Mon Jun  9 09:29:05 2025
  lic:  
  lic: FLEXlm: SDK_12.3 
  lic: USER:   e02727               HOSTNAME: ascend27 
  lic: HOSTID: "982cbcdcead7"       PID:      35480 
  lic: Using FLEXlm license file: 
  lic: 27000@ascend27 
  lic: Checkout 1 hspice 
  lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12 
  lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@ascend27 
  lic:   
  **warning** (bjt_esd_behavioral.ckt:49) runlvl smaller than 5, reset to 5 when accurate turned on
  **error** (bjt_esd_behavioral.ckt:27) Undefined function definition "if". Please enter a defined name.

 **warning**  runlvl smaller than 5, reset to 5 when accurate turned on
  **warning** pivtol too large ** reset to half minimum value of (gmindc,gmin)

          ***** job aborted
1****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******                    
 ******  
 * bjt esd device hspice model

 ****** job statistics summary tnom=  25.000 temp=  25.000 ******

  ******  HSPICE Threads Information  ******

  Command Line Threads Count :     1
  Available CPU Count        :     8
  Actual Threads Count       :     1


  ******  Circuit Statistics  ******
  # nodes       =       0 # elements   =       2
  # resistors   =       0 # capacitors =       0 # inductors   =       0
  # mutual_inds =       0 # vccs       =       1 # vcvs        =       0
  # cccs        =       0 # ccvs       =       0 # volt_srcs   =       1
  # curr_srcs   =       0 # diodes     =       0 # bjts        =       0
  # jfets       =       0 # mosfets    =       0 # U elements  =       0
  # T elements  =       0 # W elements =       0 # B elements  =       0
  # S elements  =       0 # P elements =       0 # va device   =       0
  # vector_srcs =       0 # N elements =       0


  ******  Runtime Statistics (seconds)  ******

  analysis           time    # points   tot. iter  conv.iter
  op point           0.00           1           0
  readin             0.01
  errchk             0.00
  setup              0.00
  output             0.00


           peak memory used         62.16 megabytes
           total cpu time            0.01 seconds
           total elapsed time        0.04 seconds
           job started at     09:29:05 06/09/2025
           job ended   at     09:29:05 06/09/2025
           job total runtime         0.04 seconds


  lic: Release hspice token(s) 
 lic: total license checkout elapse time:        0.02(s)
