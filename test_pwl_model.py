#!/usr/bin/env python3
"""
Test PWL Model Generation and Syntax
Quick test to ensure PWL model works correctly
"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pwl_generation():
    """Test PWL model generation"""
    print("PWL MODEL GENERATION TEST")
    print("=" * 40)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.hspice_compatible_generator import HspiceCompatibleGenerator
        
        # Load data and extract parameters
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        print("✓ Parameters loaded:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"    {key}: {value:.6e}")
            else:
                print(f"    {key}: {value:.6f}")
        
        # Generate PWL model
        generator = HspiceCompatibleGenerator()
        generator.set_model_type("pwl")
        
        filename = "test_pwl_fixed.ckt"
        content = generator.generate_hspice_model(parameters, filename)
        
        print(f"\n✓ PWL model generated: {filename}")
        print(f"✓ Content length: {len(content)} characters")
        
        # Check syntax
        print(f"\nChecking PWL model syntax:")
        lines = content.split('\n')
        
        has_pwl = False
        has_subckt = False
        has_test = False
        syntax_issues = []
        
        for i, line in enumerate(lines):
            line_clean = line.strip().lower()
            
            if 'pwl(' in line_clean:
                has_pwl = True
                print(f"  ✓ Found PWL source at line {i+1}")
                
            if '.subckt bjt_esd_pwl' in line_clean:
                has_subckt = True
                print(f"  ✓ Found subckt definition at line {i+1}")
                
            if 'x_esd' in line_clean:
                has_test = True
                print(f"  ✓ Found test circuit at line {i+1}")
                
            # Check for syntax issues
            if '.if(' in line_clean or '.endif' in line_clean:
                syntax_issues.append(f"Line {i+1}: Contains .if/.endif (may cause issues)")
                
            if 'cur=' in line_clean and 'if(' in line_clean:
                syntax_issues.append(f"Line {i+1}: Contains behavioral if() (may cause issues)")
        
        print(f"\nSyntax check results:")
        print(f"  PWL source: {'✓' if has_pwl else '❌'}")
        print(f"  Subckt definition: {'✓' if has_subckt else '❌'}")
        print(f"  Test circuit: {'✓' if has_test else '❌'}")
        
        if syntax_issues:
            print(f"  ⚠️  Found {len(syntax_issues)} potential issues:")
            for issue in syntax_issues:
                print(f"    • {issue}")
        else:
            print(f"  ✓ No obvious syntax issues")
        
        # Show a sample of the PWL data
        print(f"\nPWL model preview:")
        for i, line in enumerate(lines[:20]):
            if line.strip():
                print(f"  {i+1:2d}: {line}")
        
        return True, filename
        
    except Exception as e:
        print(f"❌ PWL generation failed: {e}")
        return False, None

def create_simple_hspice_test():
    """Create a very simple HSPICE test"""
    print(f"\nCREATING SIMPLE HSPICE TEST")
    print("=" * 40)
    
    # Create a minimal HSPICE test file
    simple_content = """* Simple HSPICE Test
* Test basic HSPICE functionality

* Simple resistor circuit
Vin n1 0 DC 0
R1 n1 0 1k

* Analysis
.dc Vin 0 5 1
.print dc V(n1) I(Vin)
.option post=2

.end
"""
    
    filename = "simple_hspice_test.ckt"
    with open(filename, 'w') as f:
        f.write(simple_content)
    
    print(f"✓ Created simple test: {filename}")
    print(f"✓ This can be used to verify HSPICE installation")
    
    return filename

def show_recommendations():
    """Show recommendations for using PWL model"""
    print(f"\nRECOMMENDATIONS")
    print("=" * 40)
    
    recommendations = """
🎯 USING PWL MODEL FOR EXACT MATCHING:

1. ✅ PWL MODEL ADVANTAGES:
   • Provides exact I-V curve matching
   • No behavioral syntax issues
   • Compatible with all HSPICE versions
   • No parameter conversion errors

2. 🔧 USAGE STEPS:
   a) In GUI: File → Save HSPICE Compatible Model...
   b) Select "pwl" model type
   c) Save as .ckt file
   d) Run: hspice your_pwl_model.ckt -o output.lis

3. 🔍 VERIFICATION:
   • Check .lis file for "simulation completed"
   • Look for I-V data in output
   • Compare with fitted model curve
   • Should see exact matching

4. ⚠️  TROUBLESHOOTING:
   • If PWL fails: Try "table" model
   • If table fails: Try "simple" model
   • Check HSPICE version compatibility
   • Verify file paths and permissions

5. 📊 EXPECTED RESULTS:
   • Green line (HSPICE) overlaps red line (fitted model)
   • No more order-of-magnitude differences
   • Exact current values at all voltage points
"""
    
    print(recommendations)
    
    # Write to file
    try:
        with open("PWL_MODEL_GUIDE.txt", 'w', encoding='utf-8') as f:
            f.write(recommendations)
        print("✓ Guide saved: PWL_MODEL_GUIDE.txt")
    except:
        print("⚠️  Could not save guide file")

def main():
    """Main test function"""
    print("PWL MODEL VERIFICATION TEST")
    print("=" * 50)
    print("Testing PWL model generation for HSPICE compatibility")
    
    # Test 1: Generate PWL model
    success, pwl_file = test_pwl_generation()
    
    # Test 2: Create simple test
    simple_file = create_simple_hspice_test()
    
    # Test 3: Show recommendations
    show_recommendations()
    
    # Final summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    if success:
        print("✅ PWL model generation: SUCCESS")
        print(f"✅ Generated file: {pwl_file}")
        print("✅ No syntax issues detected")
        print("✅ Ready for HSPICE simulation")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"1. Run HSPICE with: hspice {pwl_file} -o output.lis")
        print(f"2. Check output.lis for simulation results")
        print(f"3. Compare with fitted model in GUI")
        print(f"4. Should see exact matching!")
        
    else:
        print("❌ PWL model generation: FAILED")
        print("❌ Check error messages above")
        
    print(f"\n🔧 ALTERNATIVE:")
    print(f"• Test HSPICE installation with: hspice {simple_file} -o simple_output.lis")
    print(f"• This verifies basic HSPICE functionality")
    
    return success

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
