# BJT ESD Parameter Extractor

A comprehensive tool for extracting parameters from BJT ESD (Electrostatic Discharge) protection device I-V characteristics.

## Features

- **Interactive Parameter Fitting**: Advanced curve fitting algorithms to extract BJT ESD model parameters
- **Real-time Visualization**: Interactive plots with experimental data, fitted curves, and HSPICE simulation results
- **Parameter Adjustment**: Intuitive sliders and input fields for manual parameter tuning
- **HSPICE Integration**: Direct integration with HSPICE for circuit simulation validation
- **Data Import/Export**: Support for CSV, TXT, and DAT file formats
- **Modular Architecture**: Clean, extensible codebase with separate modules for different functionalities

## BJT ESD Device Model

The tool implements a comprehensive model for pnp BJT ESD devices where:
- **Emitter** serves as the anode
- **Base and Collector** are shorted together as the cathode

### Model Parameters

| Parameter | Description | Unit |
|-----------|-------------|------|
| I_leak | Leakage Current | A |
| Vt1 | Trigger Voltage | V |
| k | Exponential Factor | - |
| Ron | On Resistance | Ω |
| Vh | Holding Voltage | V |
| I_offset | Current Offset | A |
| Isb | Snapback Current | A |
| Vsb | Snapback Voltage | V |

## Installation

### Prerequisites

- Python 3.7 or higher
- HSPICE (optional, for simulation features)

### Setup

1. Clone or download the project
2. Install required packages:
```bash
pip install -r requirements.txt
```

3. Run the application:
```bash
python main.py
```

## Usage

### Loading Data

1. Click **File → Open Data File** or use the toolbar button
2. Select your I-V measurement data file (CSV, TXT, or DAT format)
3. The data will be automatically loaded and plotted

### Data Format

The tool supports CSV files with the following format:
```csv
Voltage,Current
0.0,0.0
0.068,8.83e-09
0.137,1.81e-08
...
```

Parameters can be included as comments:
```csv
# I_leak,1.9981e-07
# Vt1,12.645
# k,3.0
```

### Parameter Extraction

1. Load experimental data
2. Click **Analysis → Fit Parameters** or use the toolbar button
3. The tool will automatically fit the model parameters to your data
4. View results in the **Results** tab

### Manual Parameter Adjustment

1. Use the **Parameters** tab to manually adjust model parameters
2. Enable **Real-time Update** for immediate plot updates
3. Use sliders for coarse adjustment or input fields for precise values

### HSPICE Simulation

1. Ensure HSPICE is installed and accessible
2. Click **Analysis → Run HSPICE Simulation**
3. Compare simulation results with experimental data

## Project Structure

```
bjt_esd_extractor/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── config/
│   ├── __init__.py
│   └── settings.py         # Application configuration
├── models/
│   ├── __init__.py
│   └── bjt_esd_model.py    # BJT ESD device model
├── utils/
│   ├── __init__.py
│   ├── data_loader.py      # Data loading utilities
│   └── hspice_interface.py # HSPICE integration
└── gui/
    ├── __init__.py
    ├── main_window.py      # Main application window
    ├── plot_widget.py      # Plotting functionality
    └── parameter_panel.py  # Parameter adjustment panel
```

## Model Equations

The BJT ESD device model uses a piecewise function to describe different operating regions:

### Region 1: Leakage (V < Vt1)
```
I = I_leak * exp(V/Vt1)
```

### Region 2: Trigger (Vt1 ≤ V < Vh)
```
I = I_leak * exp(k*(V-Vt1)/Vt1)
```

### Region 3: Snapback (V ≥ Vh)
```
I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For questions or issues, please create an issue in the project repository.
