# BJT ESD Parameter Extractor

A comprehensive tool for extracting parameters from BJT ESD (Electrostatic Discharge) protection devices and generating HSPICE-compatible SPICE models.

## Features

- **GUI Interface**: PyQt5-based graphical interface with plotting and parameter controls
- **Parameter Extraction**: Automatic extraction of BJT ESD device parameters from I-V measurement data
- **Interactive Parameter Tuning**: Real-time parameter adjustment with sliders and direct input
- **SPICE Model Generation**: Generate HSPICE-compatible .ckt model files
- **HSPICE Integration**: Direct HSPICE simulation and result comparison
- **Data Visualization**: Interactive plotting with navigation toolbar

## Installation

1. **Prerequisites**:
   - Python 3.7 or higher
   - HSPICE (for simulation features)

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify HSPICE installation** (optional):
   ```bash
   hspice -v
   ```

## Usage

### Starting the Application

```bash
python main.py
```

### Loading Data

1. Click "Load Data" or use File → Load Data...
2. Select your CSV file containing voltage and current measurements
3. The data will be automatically plotted and parameters extracted

### Parameter Extraction

The tool automatically extracts the following BJT ESD parameters:

- **I_leak**: Leakage current (A)
- **Vt1**: Trigger voltage (V)
- **k**: Exponential factor
- **Ron**: On resistance (Ω)
- **Vh**: Holding voltage (V)
- **I_offset**: Current offset (A)
- **Isb**: Snapback current (A)
- **Vsb**: Snapback voltage (V)

### Parameter Adjustment

Use the parameter control panel to:
- Adjust parameters with sliders for real-time feedback
- Enter precise values directly in input fields
- Reset to default or auto-fitted values

### SPICE Model Generation

1. Extract or adjust parameters as needed
2. Click "Save Model" or use File → Save SPICE Model...
3. Choose location to save the .ckt file
4. The generated model is ready for HSPICE simulation

### HSPICE Simulation

1. Ensure HSPICE is installed and accessible
2. Click "HSPICE Simulation" to run verification simulation
3. Results will be overlaid on the measurement data plot

## File Structure

```
bjt_esd_extractor/
├── main.py                 # Main application entry point
├── gui/                    # GUI modules
│   ├── main_window.py      # Main application window
│   ├── plot_widget.py      # Plotting functionality
│   └── parameter_widget.py # Parameter control panel
├── data/                   # Data processing modules
│   ├── data_loader.py      # CSV and HSPICE data loading
│   └── parameter_extractor.py # Parameter extraction algorithms
├── models/                 # Model modules
│   ├── bjt_esd_model.py    # BJT ESD device model class
│   └── spice_generator.py  # SPICE model generation
├── simulation/             # Simulation modules
│   └── hspice_runner.py    # HSPICE simulation interface
└── requirements.txt        # Python dependencies
```

## Data Format

### Input CSV Format

The tool expects CSV files with voltage and current columns:

```csv
Voltage,Current
0.0,0.0
0.1,1.23e-09
0.2,2.45e-09
...
```

### SPICE Model Output

Generated .ckt files contain:
- Complete subcircuit definition
- Parameter declarations
- HSPICE-compatible syntax
- Usage examples and test circuits

## BJT ESD Device Model

The tool implements a three-region model:

1. **Leakage Region** (V < Vt1): Exponential leakage current
2. **Trigger Region** (Vt1 ≤ V < Vh): Rapid current increase
3. **Snapback Region** (V ≥ Vh): Linear conduction with snapback behavior

### Model Equation

```
I(V) = {
  I_leak * exp(V/1.0)                           if V < Vt1
  I_leak * exp(k*(V-Vt1)/Vt1)                  if Vt1 ≤ V < Vh
  I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))   if V ≥ Vh
}
```

## HSPICE Integration

### Requirements

- HSPICE must be installed and accessible via command line
- Environment variables properly configured

### Simulation Features

- Automatic netlist generation
- DC sweep analysis
- Result parsing and visualization
- Parameter sweep capabilities

### Manual Simulation

Generated .ckt files can be used independently:

```bash
hspice bjt_esd_model.ckt -o simulation_results.lis
```

## Troubleshooting

### Common Issues

1. **HSPICE not found**: Ensure HSPICE is in system PATH
2. **CSV loading errors**: Check file format and column structure
3. **Parameter extraction fails**: Verify data quality and voltage range
4. **Simulation convergence**: Adjust HSPICE options in generated netlist

### Debug Mode

Enable debug output by setting environment variable:
```bash
set DEBUG=1
python main.py
```

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with appropriate tests
4. Submit pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
- Check the troubleshooting section
- Review HSPICE documentation
- Submit issues via project repository
