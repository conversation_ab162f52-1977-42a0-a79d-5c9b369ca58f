#!/usr/bin/env python3
"""
Test HSPICE Compatibility
Generate and test HSPICE-compatible models
"""

import sys
import os
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_hspice_models():
    """Test all HSPICE-compatible model types"""
    print("HSPICE COMPATIBILITY TEST")
    print("=" * 50)
    
    try:
        # Load real parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.hspice_compatible_generator import HspiceCompatibleGenerator
        
        # Load data and extract parameters
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        print("✓ Loaded parameters:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"    {key}: {value:.6e}")
            else:
                print(f"    {key}: {value:.6f}")
        
        # Test all model types
        generator = HspiceCompatibleGenerator()
        model_types = ["pwl", "diode_corrected", "table", "simple"]
        
        results = {}
        
        for model_type in model_types:
            print(f"\n" + "-"*40)
            print(f"Testing {model_type.upper()} model:")
            
            try:
                # Set model type
                generator.set_model_type(model_type)
                
                # Generate model
                filename = f"test_hspice_{model_type}.ckt"
                content = generator.generate_hspice_model(parameters, filename)
                
                # Check syntax
                syntax_ok = check_hspice_syntax(content)
                
                # Compare with fitted model
                fitted_current = generator.compare_with_fitted(parameters)
                
                results[model_type] = {
                    'generated': True,
                    'syntax_ok': syntax_ok,
                    'filename': filename,
                    'content_length': len(content)
                }
                
                print(f"✓ Generated: {filename}")
                print(f"✓ Syntax check: {'PASS' if syntax_ok else 'FAIL'}")
                print(f"✓ Content length: {len(content)} characters")
                
            except Exception as e:
                print(f"✗ Failed to generate {model_type}: {e}")
                results[model_type] = {'generated': False, 'error': str(e)}
        
        return results
        
    except Exception as e:
        print(f"✗ Test setup failed: {e}")
        return None

def check_hspice_syntax(content):
    """Check for common HSPICE syntax issues"""
    print("  Checking HSPICE syntax...")
    
    issues = []
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        line_clean = line.strip().lower()
        
        # Check for problematic behavioral expressions
        if 'cur=' in line_clean and 'if(' in line_clean:
            issues.append(f"Line {i+1}: Behavioral if() may not be supported")
            
        # Check for parameter scaling issues
        if 'is=' in line_clean and ('*1e6' in line_clean or 'e+' in line_clean):
            issues.append(f"Line {i+1}: Potential parameter scaling issue")
            
        # Check for missing model definitions
        if line_clean.startswith('x_') and 'bjt_esd' in line_clean:
            # Check if corresponding .subckt exists
            subckt_name = line_clean.split()[-1] if len(line_clean.split()) > 0 else ""
            if subckt_name and f".subckt {subckt_name}" not in content.lower():
                issues.append(f"Line {i+1}: Missing .subckt definition for {subckt_name}")
    
    if issues:
        print(f"  ⚠️  Found {len(issues)} potential issues:")
        for issue in issues[:3]:  # Show first 3 issues
            print(f"    • {issue}")
        if len(issues) > 3:
            print(f"    • ... and {len(issues)-3} more")
        return False
    else:
        print("  ✓ No obvious syntax issues found")
        return True

def compare_model_outputs():
    """Compare outputs of different model types"""
    print("\nMODEL OUTPUT COMPARISON")
    print("=" * 50)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        # Calculate fitted model curve
        voltage = np.linspace(0, 20, 100)
        fitted_current = extractor.calculate_model_current(voltage, parameters)
        
        print(f"Fitted model characteristics:")
        print(f"  Voltage range: {voltage.min():.1f}V to {voltage.max():.1f}V")
        print(f"  Current range: {fitted_current.min():.2e}A to {fitted_current.max():.2e}A")
        
        # Key voltage points
        test_voltages = [1, 5, 10, 15, 20]
        print(f"\nKey voltage points:")
        print(f"{'V':>3} | {'Current':>12} | {'Region':>10}")
        print("-" * 30)
        
        for test_v in test_voltages:
            idx = np.argmin(np.abs(voltage - test_v))
            v_actual = voltage[idx]
            i_actual = fitted_current[idx]
            
            # Determine region
            if v_actual < parameters['Vt1']:
                region = "Leakage"
            elif v_actual < parameters['Vh']:
                region = "Trigger"
            else:
                region = "Snapback"
                
            print(f"{v_actual:3.0f} | {i_actual:12.3e} | {region:>10}")
        
        return True
        
    except Exception as e:
        print(f"✗ Comparison failed: {e}")
        return False

def generate_usage_guide():
    """Generate usage guide for HSPICE models"""
    print("\nHSPICE USAGE GUIDE")
    print("=" * 50)
    
    guide = """
📋 HSPICE Model Usage Guide:

1. 🎯 RECOMMENDED MODEL: PWL (Piecewise Linear)
   • Provides exact I-V curve matching
   • No syntax compatibility issues
   • Works with all HSPICE versions
   
2. 🔧 USAGE STEPS:
   a) In GUI: File → Save HSPICE Compatible Model...
   b) Select "pwl" model type
   c) Save as .ckt file
   d) Run: hspice your_model.ckt -o output.lis
   
3. ⚠️  TROUBLESHOOTING:
   • If PWL doesn't work: Try "table" model
   • If table doesn't work: Try "diode_corrected"
   • If all fail: Use "simple" for basic functionality
   
4. 🔍 VERIFICATION:
   • Check .lis file for errors
   • Compare I-V curve with fitted model
   • Verify current levels are reasonable
   
5. 📊 EXPECTED RESULTS:
   • PWL/Table models: Exact matching
   • Diode models: Good approximation
   • Simple model: Basic ESD behavior
"""
    
    print(guide)
    
    # Write to file
    with open("HSPICE_USAGE_GUIDE.txt", "w") as f:
        f.write(guide)
    print("✓ Usage guide saved: HSPICE_USAGE_GUIDE.txt")

def main():
    """Main test function"""
    print("HSPICE COMPATIBILITY VERIFICATION")
    print("=" * 60)
    print("Testing HSPICE-compatible model generation")
    
    # Test 1: Generate all model types
    results = test_hspice_models()
    
    # Test 2: Compare model outputs
    comparison_ok = compare_model_outputs()
    
    # Test 3: Generate usage guide
    generate_usage_guide()
    
    # Final assessment
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if results:
        print("\nModel Generation Results:")
        for model_type, result in results.items():
            if result.get('generated', False):
                syntax_status = "✓ PASS" if result.get('syntax_ok', False) else "⚠️  ISSUES"
                print(f"  {model_type:15}: ✓ Generated, Syntax: {syntax_status}")
            else:
                print(f"  {model_type:15}: ✗ Failed - {result.get('error', 'Unknown error')}")
        
        # Count successful generations
        successful = sum(1 for r in results.values() if r.get('generated', False))
        total = len(results)
        
        print(f"\nOverall: {successful}/{total} models generated successfully")
        
        if successful >= 2:
            print("✅ HSPICE compatibility: GOOD")
            print("✅ Multiple model options available")
        elif successful >= 1:
            print("⚠️  HSPICE compatibility: LIMITED")
            print("⚠️  Some model options available")
        else:
            print("❌ HSPICE compatibility: POOR")
            print("❌ No models generated successfully")
    else:
        print("❌ Test failed to run")
    
    print(f"\nComparison test: {'✓ PASS' if comparison_ok else '✗ FAIL'}")
    
    print("\n📋 NEXT STEPS:")
    print("1. Use GUI: File → Save HSPICE Compatible Model...")
    print("2. Select PWL model type for best results")
    print("3. Test with your HSPICE installation")
    print("4. Compare results with fitted model")
    
    return results is not None and comparison_ok

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
