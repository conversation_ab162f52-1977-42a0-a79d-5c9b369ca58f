"""
Exact Parameter Converter for BJT ESD Model
Implements advanced conversion methods to ensure exact matching
between fitted mathematical model and SPICE model
"""

import numpy as np
from scipy.optimize import minimize_scalar, curve_fit
import warnings

class ExactParameterConverter:
    """
    Advanced parameter converter that ensures exact matching
    between fitted model and SPICE model through multiple approaches
    """

    def __init__(self):
        self.fitted_params = None
        self.voltage_data = None
        self.current_data = None

    def set_target_data(self, voltage, current, fitted_params):
        """Set target I-V data and fitted parameters"""
        self.voltage_data = voltage.copy()
        self.current_data = current.copy()
        self.fitted_params = fitted_params.copy()

    def method1_behavioral_spice(self):
        """
        Method 1: Use HSPICE behavioral modeling to exactly replicate fitted model
        This is the most accurate approach - directly implement the fitted equations
        """
        print("Method 1: Behavioral SPICE Model (Exact Implementation)")

        # Clean up parameters to avoid mathematical issues
        params = self.fitted_params.copy()

        # Fix problematic parameters
        if params['Vt1'] <= 0:
            params['Vt1'] = 0.1  # Minimum trigger voltage
        if params['k'] <= 0:
            params['k'] = 1.0
        if params['Ron'] <= 0:
            params['Ron'] = 1.0

        spice_content = """* BJT ESD Behavioral Model - Exact Fitted Model Implementation
* This model exactly replicates the fitted mathematical model

.subckt bjt_esd_exact anode cathode
* Fitted model parameters
.param I_leak={:.6e}
.param Vt1={:.6f}
.param k={:.6f}
.param Ron={:.6f}
.param Vh={:.6f}
.param I_offset={:.6e}
.param Isb={:.6e}
.param Vsb={:.6f}

* Voltage sensing
V_sense anode n_sense 0

* Behavioral current source implementing exact fitted model
G_esd n_sense cathode cur='bjt_esd_exact_current(V(anode,cathode))'

* Exact fitted model implementation
.param bjt_esd_exact_current(v) = '
+ if(v <= 0, I_leak*1e-6,
+ if(v < Vt1, I_leak*exp(v/1.0),
+ if(v < Vh, I_leak*exp(k*(v-Vt1)/max(Vt1,0.1)),
+ I_offset + max((v-Vsb)/Ron, 0) + Isb*exp(-abs(v-Vsb))
+ )))'

.ends bjt_esd_exact

* Test circuit
.subckt test_exact
Vin n1 0 DC 0
X_esd n1 0 bjt_esd_exact
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.ends test_exact

.end
""".format(
            params['I_leak'],
            params['Vt1'],
            params['k'],
            params['Ron'],
            params['Vh'],
            params['I_offset'],
            params['Isb'],
            params['Vsb']
        )

        return spice_content, params

    def method2_piecewise_linear(self):
        """
        Method 2: Piecewise Linear (PWL) approximation
        Create PWL source that exactly matches the I-V curve
        """
        print("Method 2: Piecewise Linear (PWL) Model")

        # Calculate fitted model current at all voltage points
        fitted_current = self._calculate_fitted_current_safe(self.voltage_data)

        # Create PWL data points
        pwl_points = []
        for v, i in zip(self.voltage_data, fitted_current):
            if np.isfinite(v) and np.isfinite(i) and i > 0:
                pwl_points.append((v, i))

        # Limit number of points for SPICE compatibility
        if len(pwl_points) > 100:
            # Subsample to 100 points
            indices = np.linspace(0, len(pwl_points)-1, 100, dtype=int)
            pwl_points = [pwl_points[i] for i in indices]

        # Generate PWL SPICE content
        pwl_data = ""
        for v, i in pwl_points:
            pwl_data += " {:.6f} {:.6e}".format(v, i)

        spice_content = """* BJT ESD PWL Model - Exact I-V Curve Matching
* This model uses piecewise linear approximation of the exact I-V curve

.subckt bjt_esd_pwl anode cathode
* PWL current source with exact I-V data
G_pwl anode cathode cur='pwl(V(anode,cathode){})'

* Small resistance for numerical stability
R_stab anode cathode 1e12

.ends bjt_esd_pwl

* Test circuit
.subckt test_pwl
Vin n1 0 DC 0
X_esd n1 0 bjt_esd_pwl
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.ends test_pwl

.end
""".format(pwl_data)

        return spice_content, len(pwl_points)

    def method3_optimized_diode_network(self):
        """
        Method 3: Optimized multi-diode network
        Use multiple diodes to approximate the I-V characteristic
        """
        print("Method 3: Optimized Multi-Diode Network")

        # Analyze the I-V curve to determine diode parameters
        voltage = self.voltage_data
        current = self.current_data

        # Find key points in the curve
        log_current = np.log10(current + 1e-15)

        # Leakage region (first 20% of points or until rapid increase)
        leakage_end = min(len(voltage) // 5, 50)
        for i in range(10, len(voltage)):
            if current[i] > current[0] * 100:  # 100x increase
                leakage_end = i
                break

        # Trigger region (rapid increase)
        trigger_start = leakage_end
        trigger_end = len(voltage) - 1
        for i in range(trigger_start, len(voltage)):
            if current[i] > 1e-3:  # 1mA threshold
                trigger_end = i
                break

        # Extract parameters for each region
        # Leakage diode
        v_leak = voltage[:leakage_end]
        i_leak = current[:leakage_end]
        if len(v_leak) > 5:
            # Fit exponential: I = Is*exp(V/Vt)
            try:
                def exp_func(v, is_val, vt_val):
                    return is_val * np.exp(v / vt_val)
                popt_leak, _ = curve_fit(exp_func, v_leak, i_leak,
                                       p0=[1e-9, 1.0], bounds=([1e-15, 0.1], [1e-3, 10]))
                is_leak, vt_leak = popt_leak
            except:
                is_leak, vt_leak = 1e-9, 1.0
        else:
            is_leak, vt_leak = 1e-9, 1.0

        # Trigger diode
        if trigger_end > trigger_start + 5:
            v_trig = voltage[trigger_start:trigger_end]
            i_trig = current[trigger_start:trigger_end]
            try:
                def exp_func(v, is_val, n_val):
                    return is_val * np.exp(v / (n_val * 0.026))
                popt_trig, _ = curve_fit(exp_func, v_trig, i_trig,
                                       p0=[1e-6, 3.0], bounds=([1e-12, 0.5], [1e-1, 20]))
                is_trig, n_trig = popt_trig
            except:
                is_trig, n_trig = 1e-6, 3.0
        else:
            is_trig, n_trig = 1e-6, 3.0

        # Snapback resistance
        if trigger_end < len(voltage) - 5:
            v_snap = voltage[trigger_end:]
            i_snap = current[trigger_end:]
            # Linear fit for resistance
            slope = np.polyfit(v_snap, i_snap, 1)[0]
            r_snap = 1.0 / slope if slope > 0 else 1.0
        else:
            r_snap = 1.0

        # Generate multi-diode SPICE model
        spice_content = """* BJT ESD Multi-Diode Network Model
* Optimized diode network to match I-V characteristic

.subckt bjt_esd_multidiode anode cathode
* Leakage path diode
D_leak anode n_leak D_LEAK
R_leak n_leak cathode 1e6

* Trigger diode
D_trig anode n_trig D_TRIG
R_trig n_trig cathode 100

* Snapback resistance
R_snap anode cathode {:.3f}

* Diode models
.model D_LEAK D(
+ IS={:.6e}
+ N=1.0
+ RS=0.1
+ CJO=1e-12
+ TT=1e-12
+ )

.model D_TRIG D(
+ IS={:.6e}
+ N={:.3f}
+ RS=0.01
+ CJO=1e-12
+ TT=1e-12
+ )

.ends bjt_esd_multidiode

* Test circuit
.subckt test_multidiode
Vin n1 0 DC 0
X_esd n1 0 bjt_esd_multidiode
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.ends test_multidiode

.end
""".format(r_snap, is_leak, is_trig, n_trig)

        return spice_content, {'is_leak': is_leak, 'vt_leak': vt_leak,
                              'is_trig': is_trig, 'n_trig': n_trig, 'r_snap': r_snap}

    def _calculate_fitted_current_safe(self, voltage):
        """Calculate fitted current with safety checks"""
        params = self.fitted_params
        current = np.zeros_like(voltage)

        # Safety parameters
        vt1_safe = max(params['Vt1'], 0.1)
        k_safe = max(params['k'], 0.1)
        ron_safe = max(params['Ron'], 0.1)

        for i, v in enumerate(voltage):
            try:
                if v <= 0:
                    current[i] = params['I_leak'] * 1e-6
                elif v < vt1_safe:
                    # Leakage region
                    exp_arg = min(v / 1.0, 50)  # Prevent overflow
                    current[i] = params['I_leak'] * np.exp(exp_arg)
                elif v < params['Vh']:
                    # Trigger region
                    exp_arg = min(k_safe * (v - vt1_safe) / vt1_safe, 50)
                    current[i] = params['I_leak'] * np.exp(exp_arg)
                else:
                    # Snapback region
                    linear_term = max((v - params['Vsb']) / ron_safe, 0)
                    exp_term = params['Isb'] * np.exp(-abs(v - params['Vsb']))
                    current[i] = params['I_offset'] + linear_term + exp_term

                # Ensure positive current
                current[i] = max(current[i], 1e-15)

            except (OverflowError, ZeroDivisionError):
                current[i] = 1e-15

        return current

    def validate_all_methods(self):
        """Validate all conversion methods"""
        print("\n" + "="*60)
        print("VALIDATION OF ALL CONVERSION METHODS")
        print("="*60)

        results = {}

        # Method 1: Behavioral
        try:
            spice_content1, params1 = self.method1_behavioral_spice()
            with open("bjt_esd_method1_behavioral.ckt", "w") as f:
                f.write(spice_content1)
            results['method1'] = {'status': 'success', 'file': 'bjt_esd_method1_behavioral.ckt'}
            print("✓ Method 1 (Behavioral): Generated successfully")
        except Exception as e:
            results['method1'] = {'status': 'failed', 'error': str(e)}
            print("✗ Method 1 (Behavioral): Failed - {}".format(e))

        # Method 2: PWL
        try:
            spice_content2, num_points = self.method2_piecewise_linear()
            with open("bjt_esd_method2_pwl.ckt", "w") as f:
                f.write(spice_content2)
            results['method2'] = {'status': 'success', 'file': 'bjt_esd_method2_pwl.ckt', 'points': num_points}
            print("✓ Method 2 (PWL): Generated with {} points".format(num_points))
        except Exception as e:
            results['method2'] = {'status': 'failed', 'error': str(e)}
            print("✗ Method 2 (PWL): Failed - {}".format(e))

        # Method 3: Multi-diode
        try:
            spice_content3, diode_params = self.method3_optimized_diode_network()
            with open("bjt_esd_method3_multidiode.ckt", "w") as f:
                f.write(spice_content3)
            results['method3'] = {'status': 'success', 'file': 'bjt_esd_method3_multidiode.ckt', 'params': diode_params}
            print("✓ Method 3 (Multi-diode): Generated successfully")
        except Exception as e:
            results['method3'] = {'status': 'failed', 'error': str(e)}
            print("✗ Method 3 (Multi-diode): Failed - {}".format(e))

        return results

    def generate_comparison_script(self):
        """Generate HSPICE script to compare all methods"""
        script_content = """@echo off
echo Comparing BJT ESD Model Conversion Methods
echo ==========================================

echo.
echo Method 1: Behavioral Model
if exist bjt_esd_method1_behavioral.ckt (
    echo Running behavioral model simulation...
    hspice bjt_esd_method1_behavioral.ckt -o method1_results.lis
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Method 1 simulation completed
    ) else (
        echo [FAIL] Method 1 simulation failed
    )
) else (
    echo [FAIL] Method 1 file not found
)

echo.
echo Method 2: PWL Model
if exist bjt_esd_method2_pwl.ckt (
    echo Running PWL model simulation...
    hspice bjt_esd_method2_pwl.ckt -o method2_results.lis
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Method 2 simulation completed
    ) else (
        echo [FAIL] Method 2 simulation failed
    )
) else (
    echo [FAIL] Method 2 file not found
)

echo.
echo Method 3: Multi-Diode Model
if exist bjt_esd_method3_multidiode.ckt (
    echo Running multi-diode model simulation...
    hspice bjt_esd_method3_multidiode.ckt -o method3_results.lis
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Method 3 simulation completed
    ) else (
        echo [FAIL] Method 3 simulation failed
    )
) else (
    echo [FAIL] Method 3 file not found
)

echo.
echo ==========================================
echo All simulations completed!
echo Check .lis files for results
echo ==========================================

pause
"""

        with open("compare_all_methods.bat", "w", encoding='utf-8') as f:
            f.write(script_content)

        print("✓ Comparison script saved to compare_all_methods.bat")
