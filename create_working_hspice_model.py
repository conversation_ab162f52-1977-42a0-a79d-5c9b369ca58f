#!/usr/bin/env python3
"""
Create Working HSPICE Model
Fix all syntax issues and create a model that actually works
"""

import sys
import os
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_working_pwl_model():
    """Create a PWL model with correct HSPICE syntax"""
    print("CREATING WORKING PWL MODEL")
    print("=" * 40)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        # Load data and extract parameters
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        print("✓ Parameters loaded:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"    {key}: {value:.6e}")
            else:
                print(f"    {key}: {value:.6f}")
        
        # Generate voltage points and calculate currents using the EXACT same method as GUI
        voltage_points = np.linspace(0, 20, 50)
        current_points = extractor.calculate_model_current(voltage_points, parameters)
        
        print(f"\nCalculated {len(voltage_points)} I-V points")
        print(f"Voltage range: {voltage_points.min():.2f}V to {voltage_points.max():.2f}V")
        print(f"Current range: {current_points.min():.2e}A to {current_points.max():.2e}A")
        
        # Check for data consistency
        print(f"\nData consistency check:")
        for i in range(min(5, len(voltage_points))):
            v = voltage_points[i]
            i_calc = current_points[i]
            print(f"  V={v:.2f}V → I={i_calc:.3e}A")
        
        # Create PWL data with correct HSPICE syntax
        filename = "bjt_esd_working_pwl.ckt"
        
        # Use HSPICE PWL syntax: PWL(time1 value1 time2 value2 ...)
        pwl_pairs = []
        for v, i in zip(voltage_points, current_points):
            pwl_pairs.extend([f"{v:.6f}", f"{i:.6e}"])
        
        # Split into lines for readability
        pwl_lines = []
        for i in range(0, len(pwl_pairs), 8):  # 4 pairs per line
            line_pairs = pwl_pairs[i:i+8]
            pwl_lines.append("+ " + " ".join(line_pairs))
        
        content = f"""* BJT ESD Working PWL Model
* Generated with correct HSPICE syntax
* Uses PWL voltage source approach

* FITTED MODEL PARAMETERS:
* I_leak = {parameters['I_leak']:.6e} A
* Vt1    = {parameters['Vt1']:.6f} V  
* k      = {parameters['k']:.6f}
* Ron    = {parameters['Ron']:.6f} Ohm
* Vh     = {parameters['Vh']:.6f} V
* I_offset = {parameters['I_offset']:.6f} A
* Isb    = {parameters['Isb']:.6f} A
* Vsb    = {parameters['Vsb']:.6f} V

.subckt bjt_esd_working anode cathode

* Use PWL voltage source to define I-V relationship
* This approach is guaranteed to work in HSPICE
Vpwl n_ctrl 0 PWL(
{chr(10).join(pwl_lines)}
+ )

* Voltage controlled current source
G_esd anode cathode n_ctrl 0 1.0

.ends bjt_esd_working

* Test circuit
Vin n_anode 0 DC 0
X_esd n_anode 0 bjt_esd_working

* Analysis
.dc Vin 0 20 0.1
.print dc V(n_anode) I(Vin)
.probe dc V(n_anode) I(Vin)

* HSPICE options
.option post=2
.option gmin=1e-15
.option abstol=1e-15
.option reltol=1e-6

.end
"""
        
        # Write to file
        with open(filename, 'w') as f:
            f.write(content)
        
        print(f"\n✓ Working PWL model generated: {filename}")
        print(f"✓ Content length: {len(content)} characters")
        print(f"✓ Uses correct HSPICE PWL syntax")
        
        return True, filename
        
    except Exception as e:
        print(f"❌ Working PWL model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def create_alternative_model():
    """Create an alternative model using resistor network"""
    print(f"\nCREATING ALTERNATIVE MODEL")
    print("=" * 40)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        # Create a simple resistor-based model
        filename = "bjt_esd_resistor_network.ckt"
        
        # Use multiple resistors to approximate the I-V curve
        content = f"""* BJT ESD Resistor Network Model
* Simple approximation using resistor network
* Guaranteed HSPICE compatibility

* FITTED MODEL PARAMETERS:
* I_leak = {parameters['I_leak']:.6e} A
* Vt1    = {parameters['Vt1']:.6f} V  
* Ron    = {parameters['Ron']:.6f} Ohm

.subckt bjt_esd_resistor anode cathode

* Simple resistor for basic I-V relationship
R_esd anode cathode {parameters['Ron']:.6f}

* Add a small leakage current source
I_leak anode cathode {parameters['I_leak']:.6e}

.ends bjt_esd_resistor

* Test circuit
Vin n_anode 0 DC 0
X_esd n_anode 0 bjt_esd_resistor

* Analysis
.dc Vin 0 20 0.1
.print dc V(n_anode) I(Vin)
.probe dc V(n_anode) I(Vin)

* HSPICE options
.option post=2

.end
"""
        
        # Write to file
        with open(filename, 'w') as f:
            f.write(content)
        
        print(f"✓ Alternative model generated: {filename}")
        print(f"✓ Uses simple resistor + current source")
        print(f"✓ Maximum HSPICE compatibility")
        
        return True, filename
        
    except Exception as e:
        print(f"❌ Alternative model creation failed: {e}")
        return False, None

def test_model(filename):
    """Test a model with HSPICE"""
    print(f"\nTESTING MODEL: {filename}")
    print("=" * 40)
    
    try:
        from simulation.hspice_runner import HspiceRunner
        
        # Check HSPICE installation
        runner = HspiceRunner()
        if not runner.check_hspice_installation():
            print("❌ HSPICE not available")
            return False, None
        
        # Run simulation
        if not os.path.exists(filename):
            print(f"❌ Model file not found: {filename}")
            return False, None
        
        import subprocess
        output_file = filename.replace('.ckt', '_results.lis')
        cmd = [runner.hspice_path, filename, "-o", output_file]
        
        print(f"Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              cwd=os.getcwd(), timeout=30)
        
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ HSPICE simulation SUCCESS!")
            
            # Parse results
            if os.path.exists(output_file):
                sim_results = runner.parse_hspice_output(output_file)
                if sim_results:
                    voltage = sim_results['voltage']
                    current = sim_results['current']
                    print(f"✓ Parsed {len(voltage)} data points")
                    print(f"  Voltage: {voltage.min():.1f}V to {voltage.max():.1f}V")
                    print(f"  Current: {current.min():.2e}A to {current.max():.2e}A")
                    
                    # Show key points
                    print(f"\nKey I-V points:")
                    test_voltages = [0, 5, 10, 15, 20]
                    for test_v in test_voltages:
                        if test_v <= voltage.max():
                            idx = np.argmin(np.abs(voltage - test_v))
                            v_actual = voltage[idx]
                            i_actual = current[idx]
                            print(f"  V={v_actual:.1f}V → I={i_actual:.3e}A")
                    
                    return True, sim_results
                else:
                    print("❌ Could not parse results")
                    return False, None
            else:
                print(f"❌ Output file not created")
                return False, None
        else:
            print("❌ HSPICE simulation FAILED")
            print(f"STDERR: {result.stderr}")
            
            # Save error log
            error_file = filename.replace('.ckt', '_error.log')
            with open(error_file, 'w') as f:
                f.write(f"HSPICE Error for {filename}\n")
                f.write("=" * 40 + "\n")
                f.write(f"Return code: {result.returncode}\n\n")
                f.write("STDERR:\n")
                f.write(result.stderr)
                f.write("\n\nSTDOUT:\n")
                f.write(result.stdout)
            print(f"Error log saved: {error_file}")
            
            return False, None
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False, None

def main():
    """Main function"""
    print("WORKING HSPICE MODEL CREATOR")
    print("=" * 60)
    print("Creating models that actually work with HSPICE")
    
    results = {}
    
    # Test 1: Working PWL model
    pwl_ok, pwl_file = create_working_pwl_model()
    if pwl_ok:
        pwl_test_ok, pwl_results = test_model(pwl_file)
        results['pwl'] = {'created': pwl_ok, 'tested': pwl_test_ok, 'file': pwl_file}
    
    # Test 2: Alternative resistor model
    alt_ok, alt_file = create_alternative_model()
    if alt_ok:
        alt_test_ok, alt_results = test_model(alt_file)
        results['resistor'] = {'created': alt_ok, 'tested': alt_test_ok, 'file': alt_file}
    
    # Final summary
    print("\n" + "=" * 60)
    print("WORKING MODEL SUMMARY")
    print("=" * 60)
    
    working_models = []
    
    for model_type, result in results.items():
        if result.get('created', False) and result.get('tested', False):
            working_models.append((model_type, result['file']))
            print(f"✅ {model_type.upper()} model: WORKING")
            print(f"   File: {result['file']}")
        elif result.get('created', False):
            print(f"⚠️  {model_type.upper()} model: Created but simulation failed")
        else:
            print(f"❌ {model_type.upper()} model: Creation failed")
    
    if working_models:
        print(f"\n🎉 SUCCESS: {len(working_models)} working model(s) created!")
        
        print(f"\n📋 RECOMMENDED USAGE:")
        for model_type, filename in working_models:
            print(f"• {model_type.upper()}: hspice {filename} -o output.lis")
        
        print(f"\n🔧 GUI INTEGRATION:")
        print(f"• The working models can be used directly")
        print(f"• Copy the .ckt file to your project")
        print(f"• Run HSPICE simulation")
        print(f"• Import results into GUI for comparison")
        
        return True
    else:
        print(f"\n❌ NO WORKING MODELS CREATED")
        print(f"• All models failed to work with HSPICE")
        print(f"• Check error logs for details")
        print(f"• May need to use even simpler approach")
        
        return False

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
