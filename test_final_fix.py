#!/usr/bin/env python3
"""
Test Final Fix for HSPICE Model
Verify that the corrected model generates proper I-V curves
"""

import sys
import os
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_model_calculation():
    """Test that our model calculation matches the fitted model exactly"""
    print("TESTING MODEL CALCULATION CONSISTENCY")
    print("=" * 50)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.hspice_compatible_generator import HspiceCompatibleGenerator
        
        # Load data and extract parameters
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        print("✓ Parameters loaded:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"    {key}: {value:.6e}")
            else:
                print(f"    {key}: {value:.6f}")
        
        # Test voltage range
        test_voltages = np.array([0, 1, 5, 9, 10, 12, 15, 18, 20])
        
        # Calculate using improved_parameter_extractor (reference)
        reference_current = extractor.calculate_model_current(test_voltages, parameters)
        
        # Calculate using our PWL generator
        generator = HspiceCompatibleGenerator()
        generator_current = generator.compare_with_fitted(parameters, test_voltages)
        
        print(f"\nModel calculation comparison:")
        print(f"{'V':>3} | {'Reference':>12} | {'Generator':>12} | {'Match':>8}")
        print("-" * 50)
        
        all_match = True
        for i, v in enumerate(test_voltages):
            ref_i = reference_current[i]
            gen_i = generator_current[i]
            
            # Check if they match (within floating point precision)
            relative_error = abs(ref_i - gen_i) / (ref_i + 1e-15)
            match = relative_error < 1e-10
            
            if not match:
                all_match = False
            
            print(f"{v:3.0f} | {ref_i:12.3e} | {gen_i:12.3e} | {'✓' if match else '❌'}")
        
        if all_match:
            print("\n🎉 PERFECT: All calculations match exactly!")
            return True
        else:
            print("\n❌ MISMATCH: Calculations don't match")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_pwl_generation():
    """Test PWL model generation with corrected syntax"""
    print(f"\nTESTING PWL MODEL GENERATION")
    print("=" * 50)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.hspice_compatible_generator import HspiceCompatibleGenerator
        
        # Load data and extract parameters
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        # Generate PWL model
        generator = HspiceCompatibleGenerator()
        generator.set_model_type("pwl")
        
        filename = "bjt_esd_final_fix.ckt"
        content = generator.generate_hspice_model(parameters, filename)
        
        print(f"✓ PWL model generated: {filename}")
        print(f"✓ Content length: {len(content)} characters")
        
        # Check the generated content
        lines = content.split('\n')
        
        has_table = False
        has_subckt = False
        syntax_issues = []
        
        for i, line in enumerate(lines):
            line_clean = line.strip()
            
            if 'G_esd' in line_clean and 'TABLE' in line_clean:
                has_table = True
                print(f"  ✓ Found TABLE syntax at line {i+1}")
                print(f"    {line_clean}")
                
            if '.subckt bjt_esd_pwl' in line_clean:
                has_subckt = True
                print(f"  ✓ Found subckt definition at line {i+1}")
                
            # Check for syntax issues
            if 'PWL(' in line_clean and 'CUR=' not in line_clean and 'TABLE' not in line_clean:
                syntax_issues.append(f"Line {i+1}: Old PWL syntax found")
        
        print(f"\nSyntax verification:")
        print(f"  TABLE syntax: {'✓' if has_table else '❌'}")
        print(f"  Subckt definition: {'✓' if has_subckt else '❌'}")
        
        if syntax_issues:
            print(f"  ⚠️  Issues found:")
            for issue in syntax_issues:
                print(f"    • {issue}")
            return False
        else:
            print(f"  ✓ No syntax issues detected")
            return True
            
    except Exception as e:
        print(f"❌ PWL generation failed: {e}")
        return False

def test_hspice_simulation():
    """Test HSPICE simulation with the final fixed model"""
    print(f"\nTESTING HSPICE SIMULATION")
    print("=" * 50)
    
    try:
        from simulation.hspice_runner import HspiceRunner
        
        # Check HSPICE installation
        runner = HspiceRunner()
        if not runner.check_hspice_installation():
            print("❌ HSPICE not available - skipping simulation test")
            return False
        
        print("✓ HSPICE installation detected")
        
        # Run simulation
        filename = "bjt_esd_final_fix.ckt"
        if not os.path.exists(filename):
            print(f"❌ Model file not found: {filename}")
            return False
        
        import subprocess
        output_file = filename.replace('.ckt', '_results.lis')
        cmd = [runner.hspice_path, filename, "-o", output_file]
        
        print(f"Running command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              cwd=os.getcwd(), timeout=30)
        
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ HSPICE simulation completed successfully!")
            
            # Check if output file exists and parse results
            if os.path.exists(output_file):
                print(f"✓ Output file created: {output_file}")
                
                # Try to parse results
                sim_results = runner.parse_hspice_output(output_file)
                if sim_results:
                    voltage = sim_results['voltage']
                    current = sim_results['current']
                    print(f"✓ Parsed {len(voltage)} data points")
                    print(f"  Voltage range: {voltage.min():.2f}V to {voltage.max():.2f}V")
                    print(f"  Current range: {current.min():.2e}A to {current.max():.2e}A")
                    
                    # Quick sanity check
                    if current.min() > 1e-12 and current.max() < 100:
                        print("✓ Current values look reasonable")
                        return True
                    else:
                        print("⚠️  Current values may be unrealistic")
                        return False
                else:
                    print("❌ Could not parse simulation results")
                    return False
            else:
                print(f"❌ Output file not created: {output_file}")
                return False
        else:
            print("❌ HSPICE simulation failed")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ HSPICE simulation error: {e}")
        return False

def main():
    """Main test function"""
    print("FINAL FIX VERIFICATION TEST")
    print("=" * 60)
    print("Testing all fixes for HSPICE model generation and simulation")
    
    # Test 1: Model calculation consistency
    calc_ok = test_model_calculation()
    
    # Test 2: PWL model generation
    gen_ok = test_pwl_generation()
    
    # Test 3: HSPICE simulation
    sim_ok = test_hspice_simulation()
    
    # Final summary
    print("\n" + "=" * 60)
    print("FINAL TEST SUMMARY")
    print("=" * 60)
    
    print(f"Model calculation consistency: {'✅ PASS' if calc_ok else '❌ FAIL'}")
    print(f"PWL model generation: {'✅ PASS' if gen_ok else '❌ FAIL'}")
    print(f"HSPICE simulation: {'✅ PASS' if sim_ok else '❌ FAIL'}")
    
    overall_success = calc_ok and gen_ok and sim_ok
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The HSPICE compatibility issue is RESOLVED")
        print("✅ PWL model should now provide exact matching")
        print("✅ Green line should overlap with red line in GUI")
        
        print(f"\n📋 NEXT STEPS:")
        print("1. Use GUI: File → Save HSPICE Compatible Model...")
        print("2. Select 'pwl' model type")
        print("3. Run HSPICE simulation")
        print("4. Verify results match fitted model")
        
    else:
        print("\n❌ SOME TESTS FAILED")
        print("❌ Further debugging may be needed")
        
        if not calc_ok:
            print("• Fix model calculation consistency")
        if not gen_ok:
            print("• Fix PWL model generation syntax")
        if not sim_ok:
            print("• Fix HSPICE simulation issues")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
