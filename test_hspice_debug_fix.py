#!/usr/bin/env python3
"""
Test the HSPICE debug fixes
"""

import sys
import os
import numpy as np

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_hspice_parsing_with_debug():
    """Test HSPICE parsing with new debug features"""
    print("Testing HSPICE parsing with debug fixes...")
    print("=" * 50)
    
    try:
        from utils.hspice_interface import HSPICEInterface
        
        hspice = HSPICEInterface()
        
        if os.path.exists('bjt_esd_simulation.lis'):
            print("Parsing bjt_esd_simulation.lis with debug output...")
            result = hspice._parse_hspice_output_file('bjt_esd_simulation.lis')
            
            if result:
                voltage, current = result
                print(f"\n✓ Successfully parsed and processed data")
                print(f"Final data points: {len(voltage)}")
                
                # Verify data quality
                print(f"\nData Quality Check:")
                print(f"Voltage range: {voltage.min():.3f} to {voltage.max():.3f} V")
                print(f"Current range: {current.min():.3e} to {current.max():.3e} A")
                
                # Check for monotonic voltage
                is_monotonic = np.all(voltage[:-1] <= voltage[1:])
                print(f"Voltage is monotonic: {is_monotonic}")
                
                # Check voltage steps
                if len(voltage) > 1:
                    voltage_steps = np.diff(voltage)
                    print(f"Voltage steps - min: {voltage_steps.min():.6f}, max: {voltage_steps.max():.6f}")
                
                # Show key data points
                print(f"\nKey data points:")
                test_voltages = [0, 5, 10, 15, 20]
                for v_test in test_voltages:
                    if v_test <= voltage.max():
                        i_test = np.interp(v_test, voltage, current)
                        print(f"  {v_test:2.0f}V: {i_test:.6f}A")
                
                return voltage, current
            else:
                print("✗ Failed to parse HSPICE output")
                return None
        else:
            print("! No bjt_esd_simulation.lis file found")
            return None
            
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_gui_integration():
    """Test GUI integration with debug fixes"""
    print("\nTesting GUI integration...")
    print("=" * 30)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.plot_widget import PlotWidget
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create plot widget
        plot_widget = PlotWidget()
        
        # Test with sample data
        voltage_test = np.linspace(0, 20, 50)
        current_test = voltage_test * 0.2 + 0.1 * voltage_test**2  # Quadratic curve
        
        print("Testing plot widget with sample data...")
        plot_widget.plot_hspice_data(voltage_test, current_test)
        
        # Check stored data
        plot_data = plot_widget.get_plot_data()
        if 'hspice' in plot_data:
            stored_voltage = plot_data['hspice']['voltage']
            stored_current = plot_data['hspice']['current']
            print(f"✓ Data stored successfully: {len(stored_voltage)} points")
            return True
        else:
            print("✗ Data not stored properly")
            return False
            
    except Exception as e:
        print(f"✗ GUI test failed: {e}")
        return False

def create_comparison_plot(hspice_data):
    """Create a comparison plot to show the fix"""
    if hspice_data is None:
        print("No HSPICE data for plotting")
        return
    
    try:
        import matplotlib.pyplot as plt
        
        voltage, current = hspice_data
        
        plt.figure(figsize=(12, 6))
        
        # Plot processed HSPICE data
        plt.plot(voltage, current, 'g--', linewidth=2, marker='o', markersize=3, 
                 label=f'HSPICE (Processed, {len(voltage)} points)')
        
        plt.xlabel('Voltage (V)')
        plt.ylabel('Current (A)')
        plt.title('HSPICE Data After Debug Fixes')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Add annotations
        plt.text(0.02, 0.98, f'Data points: {len(voltage)}', 
                transform=plt.gca().transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig('hspice_debug_fixed.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("Comparison plot saved as: hspice_debug_fixed.png")
        
    except Exception as e:
        print(f"Plotting failed: {e}")

def run_complete_simulation_test():
    """Run a complete simulation test with debug output"""
    print("\nRunning complete simulation test...")
    print("=" * 40)
    
    try:
        from utils.hspice_interface import HSPICEInterface
        from models.bjt_esd_model import BJTESDModel
        from utils.data_loader import DataLoader
        
        if not os.path.exists('1.csv'):
            print("! No experimental data file, skipping simulation test")
            return True
        
        # Load and fit data
        loader = DataLoader()
        voltage_exp, current_exp, _ = loader.load_data('1.csv')
        
        model = BJTESDModel()
        fitted_params = model.fit_parameters(voltage_exp, current_exp)
        
        print(f"✓ Loaded experimental data: {len(voltage_exp)} points")
        print(f"✓ Model fitted with R² = {fitted_params.get('r_squared', 0):.4f}")
        
        # Test HSPICE simulation
        hspice = HSPICEInterface()
        if hspice.verify_hspice_installation():
            print("✓ HSPICE available")
            
            # Use existing simulation file
            if os.path.exists('bjt_esd_simulation.sp'):
                print("Running HSPICE simulation with debug output...")
                result = hspice.run_simulation_from_netlist('bjt_esd_simulation.sp')
                
                if result:
                    voltage_sim, current_sim = result
                    print(f"✓ HSPICE simulation completed with debug processing")
                    print(f"  Final data points: {len(voltage_sim)}")
                    
                    # Compare with fitted model
                    voltage_fitted = np.linspace(0, 20, 100)
                    current_fitted = model.current_equation(voltage_fitted, **fitted_params)
                    
                    # Compare at key points
                    print(f"\nComparison at key voltages:")
                    for v_test in [5, 10, 15, 20]:
                        if v_test <= voltage_sim.max():
                            i_sim = np.interp(v_test, voltage_sim, current_sim)
                            i_fitted = np.interp(v_test, voltage_fitted, current_fitted)
                            error = abs(i_sim - i_fitted) / i_fitted * 100 if i_fitted > 0 else 0
                            print(f"  {v_test:2.0f}V: HSPICE={i_sim:.3f}A, Fitted={i_fitted:.3f}A, Error={error:.1f}%")
                    
                    return True
                else:
                    print("✗ HSPICE simulation failed")
                    return False
            else:
                print("! No simulation netlist found")
                return True
        else:
            print("! HSPICE not available")
            return True
            
    except Exception as e:
        print(f"✗ Complete simulation test failed: {e}")
        return False

def main():
    """Main test function"""
    print("BJT ESD Parameter Extractor - HSPICE Debug Fix Test")
    print("=" * 60)
    
    tests = [
        ("HSPICE Parsing", test_hspice_parsing_with_debug),
        ("GUI Integration", test_gui_integration),
        ("Complete Simulation", run_complete_simulation_test)
    ]
    
    passed = 0
    total = len(tests)
    hspice_data = None
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            if result:
                passed += 1
                if test_name == "HSPICE Parsing" and isinstance(result, tuple):
                    hspice_data = result
            else:
                print(f"✗ {test_name} failed")
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
    
    # Create comparison plot if we have data
    if hspice_data:
        create_comparison_plot(hspice_data)
    
    print("\n" + "=" * 60)
    print(f"HSPICE Debug Fix Test Results: {passed}/{total} tests passed")
    
    if passed >= 2:
        print("✓ HSPICE debug fixes appear to be working!")
        print("The HSPICE simulation curve should now display correctly.")
    else:
        print("✗ Some issues remain. Please check the debug output above.")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
