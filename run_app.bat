@echo off
echo BJT ESD Parameter Extractor
echo ===========================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Check if virtual environment exists
if exist ".venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call .venv\Scripts\activate.bat
)

REM Install requirements if needed
if not exist ".requirements_installed" (
    echo Installing requirements...
    pip install -r requirements.txt
    if errorlevel 0 (
        echo. > .requirements_installed
    ) else (
        echo Failed to install requirements
        pause
        exit /b 1
    )
)

REM Run the application
echo Starting BJT ESD Parameter Extractor...
python main.py

pause
