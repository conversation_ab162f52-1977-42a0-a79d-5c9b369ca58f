# BJT ESD Parameter Extractor - Quick Start Guide

## 快速开始指南

这是一个完整的BJT ESD器件参数提取工具，支持从I-V测量数据自动提取参数并生成HSPICE兼容的SPICE模型。

## 🚀 快速启动

### 1. 检查环境
确保您的系统已安装：
- Python 3.7+
- HSPICE (可选，用于仿真验证)

### 2. 安装依赖
```bash
pip install PyQt5 matplotlib numpy pandas scipy
```

### 3. 运行演示
```bash
python demo.py
```

### 4. 启动GUI应用
```bash
python main.py
```

## 📁 项目文件说明

### 核心文件
- `main.py` - GUI应用程序主入口
- `demo.py` - 命令行演示脚本
- `1.csv` - 测试数据文件
- `requirements.txt` - Python依赖列表

### 模块目录
- `gui/` - PyQt5图形界面模块
- `data/` - 数据处理和参数提取模块  
- `models/` - BJT ESD模型和SPICE生成模块
- `simulation/` - HSPICE仿真接口模块

## 🔧 功能特点

### GUI界面特点
- ✅ **上方绘图区域**：matplotlib集成，支持导航工具栏
- ✅ **下方参数面板**：滑块+直接输入，纵向排列
- ✅ **实时参数调整**：参数变化立即反映在图形中
- ✅ **模块化设计**：结构清晰，易于扩展

### 参数提取功能
- ✅ **自动参数提取**：从I-V数据自动识别三个区域
- ✅ **手动参数调整**：支持滑块和直接数值输入
- ✅ **实时模型更新**：参数变化时模型曲线实时更新

### SPICE模型生成
- ✅ **HSPICE兼容**：生成标准.ckt格式文件
- ✅ **完整子电路**：包含参数定义和测试电路
- ✅ **优化收敛性**：使用稳定的等效电路模型

### HSPICE集成
- ✅ **自动仿真**：一键运行HSPICE仿真
- ✅ **结果比较**：仿真结果与测量数据对比
- ✅ **脚本生成**：生成独立的仿真脚本

## 📊 BJT ESD模型

### 器件结构
- **发射极(Emitter)** → 阳极 (保护的信号线)
- **基极(Base) + 集电极(Collector)** → 阴极 (地线)

### 三区域模型
1. **漏电区域** (V < Vt1): `I = I_leak × exp(V/1.0)`
2. **触发区域** (Vt1 ≤ V < Vh): `I = I_leak × exp(k×(V-Vt1)/Vt1)`
3. **回滞区域** (V ≥ Vh): `I = I_offset + (V-Vsb)/Ron + Isb×exp(-(V-Vsb))`

### 关键参数
- **I_leak**: 漏电流 (A)
- **Vt1**: 触发电压 (V) 
- **k**: 指数因子
- **Ron**: 导通电阻 (Ω)
- **Vh**: 保持电压 (V)
- **I_offset**: 电流偏移 (A)
- **Isb**: 回滞电流 (A)
- **Vsb**: 回滞电压 (V)

## 📝 使用流程

### 方法1: GUI应用 (推荐)
1. 运行 `python main.py`
2. 点击 "Load Data" 加载CSV文件
3. 自动提取参数或手动调整
4. 点击 "Save Model" 保存.ckt文件
5. 点击 "HSPICE Simulation" 运行仿真

### 方法2: 命令行演示
1. 运行 `python demo.py`
2. 查看自动生成的结果文件
3. 运行 `run_hspice.bat` 进行HSPICE仿真

### 方法3: 批处理启动
1. 双击 `run_app.bat`
2. 先运行简单测试，再启动GUI

## 📋 数据格式要求

### CSV输入格式
```csv
Voltage,Current
0.0,0.0
0.1,1.23e-09
0.2,2.45e-09
...
```

### 数据要求
- 第一列：电压值 (V)
- 第二列：电流值 (A)
- 支持科学计数法
- 电压范围建议：0-25V
- 电流范围：pA到A级别

## 🔍 HSPICE仿真

### 环境要求
```bash
# 检查HSPICE安装
hspice -v
```

### 仿真命令
```bash
# 运行生成的模型
hspice bjt_esd_model.ckt -o results.lis

# 或使用生成的脚本
run_hspice.bat
```

### 输出文件
- `.lis` - HSPICE仿真结果
- `.sw0` - 扫描数据文件
- `.pa0` - 后处理文件

## 🛠️ 故障排除

### 常见问题

**1. PyQt5导入错误**
```bash
pip install PyQt5
```

**2. matplotlib显示问题**
```bash
pip install matplotlib --upgrade
```

**3. HSPICE未找到**
- 检查HSPICE安装
- 确认环境变量PATH设置
- 尝试完整路径：`C:\synopsys\Hspice_P-2019.06\WIN64\hspice.exe`

**4. CSV加载失败**
- 检查文件格式（逗号分隔）
- 确认数据列数（至少2列）
- 检查数值格式（支持科学计数法）

**5. 参数提取异常**
- 检查数据质量（无NaN值）
- 确认电压范围合理
- 验证电流值为正数

### 调试模式
```bash
# Windows
set DEBUG=1
python main.py

# Linux/Mac
export DEBUG=1
python main.py
```

## 📈 扩展功能

### 可添加的功能
- 温度建模
- 工艺变化分析
- 频域特性
- 批量处理
- 参数数据库

### 自定义模型
修改 `models/bjt_esd_model.py` 实现自定义ESD器件模型。

## 📞 技术支持

### 文档
- `README.md` - 详细技术文档
- `PROJECT_STRUCTURE.md` - 项目结构说明
- 代码注释 - 详细的函数和类说明

### 测试
- `simple_test.py` - 基本功能测试
- `demo.py` - 完整流程演示
- `test_app.py` - 模块导入测试

## 🎯 成功标志

运行成功后，您应该看到：
- ✅ GUI界面正常显示
- ✅ 数据加载和绘图正常
- ✅ 参数提取和调整功能正常
- ✅ SPICE模型生成成功
- ✅ HSPICE仿真运行正常

## 📄 许可证

本项目采用MIT许可证，可自由使用和修改。
