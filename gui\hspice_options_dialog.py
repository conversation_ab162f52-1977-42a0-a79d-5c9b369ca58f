"""
HSPICE Options Dialog for BJT ESD Parameter Extractor
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QPushButton, QLineEdit, QGroupBox,
                             QCheckBox, QComboBox, QDoubleSpinBox, QSpinBox,
                             QFileDialog, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class HspiceOptionsDialog(QDialog):
    """Dialog for HSPICE simulation options"""
    
    def __init__(self, input_file, parent=None):
        super().__init__(parent)
        self.input_file = input_file
        self.options = {}
        
        self.init_ui()
        self.load_default_options()
        
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("HSPICE Simulation Options")
        self.setGeometry(300, 300, 600, 500)
        
        layout = QVBoxLayout(self)
        
        # Input file info
        self.create_file_info_group(layout)
        
        # Simulation options
        self.create_simulation_options_group(layout)
        
        # Output options
        self.create_output_options_group(layout)
        
        # Advanced options
        self.create_advanced_options_group(layout)
        
        # Preview
        self.create_preview_group(layout)
        
        # Buttons
        self.create_buttons(layout)
        
    def create_file_info_group(self, parent_layout):
        """Create file information group"""
        group = QGroupBox("Input File Information")
        layout = QGridLayout(group)
        
        # File path
        layout.addWidget(QLabel("File:"), 0, 0)
        self.file_label = QLabel(os.path.basename(self.input_file))
        self.file_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(self.file_label, 0, 1)
        
        # File size
        if os.path.exists(self.input_file):
            file_size = os.path.getsize(self.input_file)
            layout.addWidget(QLabel("Size:"), 1, 0)
            layout.addWidget(QLabel("{} bytes".format(file_size)), 1, 1)
        
        parent_layout.addWidget(group)
        
    def create_simulation_options_group(self, parent_layout):
        """Create simulation options group"""
        group = QGroupBox("Simulation Options")
        layout = QGridLayout(group)
        
        # Analysis type
        layout.addWidget(QLabel("Analysis Type:"), 0, 0)
        self.analysis_combo = QComboBox()
        self.analysis_combo.addItems(["DC Analysis", "AC Analysis", "Transient Analysis"])
        self.analysis_combo.setCurrentText("DC Analysis")
        layout.addWidget(self.analysis_combo, 0, 1)
        
        # Voltage range
        layout.addWidget(QLabel("Voltage Start (V):"), 1, 0)
        self.voltage_start_spin = QDoubleSpinBox()
        self.voltage_start_spin.setRange(-100, 100)
        self.voltage_start_spin.setValue(0)
        self.voltage_start_spin.setDecimals(3)
        layout.addWidget(self.voltage_start_spin, 1, 1)
        
        layout.addWidget(QLabel("Voltage Stop (V):"), 2, 0)
        self.voltage_stop_spin = QDoubleSpinBox()
        self.voltage_stop_spin.setRange(-100, 100)
        self.voltage_stop_spin.setValue(20)
        self.voltage_stop_spin.setDecimals(3)
        layout.addWidget(self.voltage_stop_spin, 2, 1)
        
        layout.addWidget(QLabel("Voltage Step (V):"), 3, 0)
        self.voltage_step_spin = QDoubleSpinBox()
        self.voltage_step_spin.setRange(0.001, 10)
        self.voltage_step_spin.setValue(0.1)
        self.voltage_step_spin.setDecimals(3)
        layout.addWidget(self.voltage_step_spin, 3, 1)
        
        # Temperature
        layout.addWidget(QLabel("Temperature (°C):"), 4, 0)
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(-273, 200)
        self.temperature_spin.setValue(27)
        self.temperature_spin.setDecimals(1)
        layout.addWidget(self.temperature_spin, 4, 1)
        
        parent_layout.addWidget(group)
        
    def create_output_options_group(self, parent_layout):
        """Create output options group"""
        group = QGroupBox("Output Options")
        layout = QGridLayout(group)
        
        # Output file
        layout.addWidget(QLabel("Output File:"), 0, 0)
        self.output_file_edit = QLineEdit()
        base_name = os.path.splitext(os.path.basename(self.input_file))[0]
        self.output_file_edit.setText("{}_results.lis".format(base_name))
        layout.addWidget(self.output_file_edit, 0, 1)
        
        browse_button = QPushButton("Browse...")
        browse_button.clicked.connect(self.browse_output_file)
        layout.addWidget(browse_button, 0, 2)
        
        # Keep files
        self.keep_files_check = QCheckBox("Keep intermediate files")
        self.keep_files_check.setChecked(True)
        layout.addWidget(self.keep_files_check, 1, 0, 1, 3)
        
        # Generate plots
        self.generate_plots_check = QCheckBox("Generate plot data")
        self.generate_plots_check.setChecked(True)
        layout.addWidget(self.generate_plots_check, 2, 0, 1, 3)
        
        parent_layout.addWidget(group)
        
    def create_advanced_options_group(self, parent_layout):
        """Create advanced options group"""
        group = QGroupBox("Advanced Options")
        layout = QGridLayout(group)
        
        # Convergence options
        layout.addWidget(QLabel("GMIN:"), 0, 0)
        self.gmin_edit = QLineEdit("1e-12")
        layout.addWidget(self.gmin_edit, 0, 1)
        
        layout.addWidget(QLabel("RELTOL:"), 1, 0)
        self.reltol_edit = QLineEdit("1e-3")
        layout.addWidget(self.reltol_edit, 1, 1)
        
        layout.addWidget(QLabel("ABSTOL:"), 2, 0)
        self.abstol_edit = QLineEdit("1e-12")
        layout.addWidget(self.abstol_edit, 2, 1)
        
        # Iteration limits
        layout.addWidget(QLabel("ITL1:"), 3, 0)
        self.itl1_spin = QSpinBox()
        self.itl1_spin.setRange(10, 1000)
        self.itl1_spin.setValue(100)
        layout.addWidget(self.itl1_spin, 3, 1)
        
        layout.addWidget(QLabel("ITL2:"), 4, 0)
        self.itl2_spin = QSpinBox()
        self.itl2_spin.setRange(10, 1000)
        self.itl2_spin.setValue(50)
        layout.addWidget(self.itl2_spin, 4, 1)
        
        # Additional options
        self.accurate_check = QCheckBox("Use accurate mode")
        self.accurate_check.setChecked(True)
        layout.addWidget(self.accurate_check, 5, 0, 1, 2)
        
        self.post_check = QCheckBox("Generate POST output")
        self.post_check.setChecked(True)
        layout.addWidget(self.post_check, 6, 0, 1, 2)
        
        parent_layout.addWidget(group)
        
    def create_preview_group(self, parent_layout):
        """Create command preview group"""
        group = QGroupBox("Command Preview")
        layout = QVBoxLayout(group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(100)
        self.preview_text.setReadOnly(True)
        self.preview_text.setFont(QFont("Courier", 9))
        layout.addWidget(self.preview_text)
        
        # Update preview when options change
        self.connect_preview_updates()
        
        parent_layout.addWidget(group)
        
    def create_buttons(self, parent_layout):
        """Create dialog buttons"""
        button_layout = QHBoxLayout()
        
        # Preview button
        preview_button = QPushButton("Update Preview")
        preview_button.clicked.connect(self.update_preview)
        button_layout.addWidget(preview_button)
        
        button_layout.addStretch()
        
        # Cancel button
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        # OK button
        ok_button = QPushButton("Run Simulation")
        ok_button.clicked.connect(self.accept)
        ok_button.setDefault(True)
        button_layout.addWidget(ok_button)
        
        parent_layout.addLayout(button_layout)
        
    def connect_preview_updates(self):
        """Connect signals for preview updates"""
        # Connect all relevant controls to update preview
        self.analysis_combo.currentTextChanged.connect(self.update_preview)
        self.voltage_start_spin.valueChanged.connect(self.update_preview)
        self.voltage_stop_spin.valueChanged.connect(self.update_preview)
        self.voltage_step_spin.valueChanged.connect(self.update_preview)
        self.temperature_spin.valueChanged.connect(self.update_preview)
        self.output_file_edit.textChanged.connect(self.update_preview)
        self.gmin_edit.textChanged.connect(self.update_preview)
        self.reltol_edit.textChanged.connect(self.update_preview)
        self.abstol_edit.textChanged.connect(self.update_preview)
        self.accurate_check.toggled.connect(self.update_preview)
        self.post_check.toggled.connect(self.update_preview)
        
    def load_default_options(self):
        """Load default options"""
        self.update_preview()
        
    def browse_output_file(self):
        """Browse for output file"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Select Output File",
            self.output_file_edit.text(),
            "HSPICE Output Files (*.lis);;All Files (*)"
        )
        
        if file_path:
            self.output_file_edit.setText(file_path)
            
    def update_preview(self):
        """Update command preview"""
        try:
            # Build HSPICE command
            cmd_parts = ["hspice", os.path.basename(self.input_file)]
            cmd_parts.extend(["-o", self.output_file_edit.text()])
            
            # Add options
            options = []
            
            if self.accurate_check.isChecked():
                options.append(".option accurate")
                
            if self.post_check.isChecked():
                options.append(".option post=2")
                
            options.append(".option gmin={}".format(self.gmin_edit.text()))
            options.append(".option reltol={}".format(self.reltol_edit.text()))
            options.append(".option abstol={}".format(self.abstol_edit.text()))
            options.append(".option itl1={}".format(self.itl1_spin.value()))
            options.append(".option itl2={}".format(self.itl2_spin.value()))
            
            # Build preview text
            preview_text = "HSPICE Command:\n"
            preview_text += " ".join(cmd_parts) + "\n\n"
            
            preview_text += "Additional Options:\n"
            for option in options:
                preview_text += option + "\n"
                
            preview_text += "\nAnalysis:\n"
            if self.analysis_combo.currentText() == "DC Analysis":
                preview_text += ".dc Vin {} {} {}\n".format(
                    self.voltage_start_spin.value(),
                    self.voltage_stop_spin.value(),
                    self.voltage_step_spin.value()
                )
                
            preview_text += ".temp {}\n".format(self.temperature_spin.value())
            
            self.preview_text.setText(preview_text)
            
        except Exception as e:
            self.preview_text.setText("Error generating preview: {}".format(str(e)))
            
    def get_options(self):
        """Get simulation options"""
        return {
            'output_file': self.output_file_edit.text(),
            'keep_files': self.keep_files_check.isChecked(),
            'generate_plots': self.generate_plots_check.isChecked(),
            'analysis_type': self.analysis_combo.currentText().lower().replace(' ', '_'),
            'voltage_range': (self.voltage_start_spin.value(), self.voltage_stop_spin.value()),
            'voltage_step': self.voltage_step_spin.value(),
            'temperature': self.temperature_spin.value(),
            'gmin': self.gmin_edit.text(),
            'reltol': self.reltol_edit.text(),
            'abstol': self.abstol_edit.text(),
            'itl1': self.itl1_spin.value(),
            'itl2': self.itl2_spin.value(),
            'accurate': self.accurate_check.isChecked(),
            'post': self.post_check.isChecked()
        }
