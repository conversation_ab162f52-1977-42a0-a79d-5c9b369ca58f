#!/usr/bin/env python3
"""
Test HSPICE model matching with fitted model
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_model_matching():
    """Test how well HSPICE model matches fitted model"""
    try:
        from utils.hspice_interface import HSPICEInterface
        from models.bjt_esd_model import BJTESDModel
        from utils.data_loader import DataLoader
        
        print("Testing HSPICE model matching...")
        print("=" * 50)
        
        # Load experimental data
        if not os.path.exists('1.csv'):
            print("No experimental data file found")
            return None
        
        loader = DataLoader()
        voltage_exp, current_exp, _ = loader.load_data('1.csv')
        
        # Fit model
        model = BJTESDModel()
        fitted_params = model.fit_parameters(voltage_exp, current_exp)
        
        print("Fitted parameters:")
        for key, value in fitted_params.items():
            if key != 'r_squared':
                print(f"  {key}: {value:.6e}")
        
        # Generate fitted curve
        voltage_fitted = np.linspace(0, 20, 100)
        current_fitted = model.current_equation(voltage_fitted, **fitted_params)
        
        # Generate new HSPICE model
        hspice = HSPICEInterface()
        model_content = hspice.generate_spice_model(fitted_params)
        
        # Save and test new model
        with open('test_matching_model.ckt', 'w') as f:
            f.write(model_content)
        
        netlist_content = hspice.generate_netlist(fitted_params, (0, 20), 100, 'test_matching_model.ckt')
        with open('test_matching_simulation.sp', 'w') as f:
            f.write(netlist_content)
        
        print("\nGenerated new simplified HSPICE model")
        
        # Run HSPICE simulation
        if hspice.verify_hspice_installation():
            print("Running HSPICE simulation...")
            result = hspice.run_simulation_from_netlist('test_matching_simulation.sp')
            
            if result:
                voltage_hspice, current_hspice = result
                print(f"✓ HSPICE simulation completed: {len(voltage_hspice)} points")
                
                # Compare models
                print(f"\nModel comparison at key voltages:")
                test_voltages = [1, 5, 10, 15, 20]
                
                for v_test in test_voltages:
                    if v_test <= voltage_fitted.max() and v_test <= voltage_hspice.max():
                        i_fitted = np.interp(v_test, voltage_fitted, current_fitted)
                        i_hspice = np.interp(v_test, voltage_hspice, current_hspice)
                        error = abs(i_hspice - i_fitted) / i_fitted * 100 if i_fitted > 0 else 0
                        
                        print(f"  {v_test:2.0f}V: Fitted={i_fitted:.4f}A, HSPICE={i_hspice:.4f}A, Error={error:.1f}%")
                
                return voltage_fitted, current_fitted, voltage_hspice, current_hspice, fitted_params
            else:
                print("✗ HSPICE simulation failed")
                return None
        else:
            print("! HSPICE not available")
            return None
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_comparison_plot(data):
    """Create detailed comparison plot"""
    if data is None:
        print("No data for plotting")
        return
    
    voltage_fitted, current_fitted, voltage_hspice, current_hspice, fitted_params = data
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # Plot 1: Direct comparison
    ax1.plot(voltage_fitted, current_fitted, 'r-', linewidth=2, label='Fitted Model')
    ax1.plot(voltage_hspice, current_hspice, 'g--', linewidth=2, label='HSPICE Simulation')
    
    ax1.set_xlabel('Voltage (V)')
    ax1.set_ylabel('Current (A)')
    ax1.set_title('Model Matching Comparison')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Add parameter info
    param_text = f"R² = {fitted_params.get('r_squared', 0):.4f}\n"
    param_text += f"Vt1 = {fitted_params.get('Vt1', 0):.2f}V\n"
    param_text += f"Vh = {fitted_params.get('Vh', 0):.2f}V\n"
    param_text += f"k = {fitted_params.get('k', 0):.2f}"
    
    ax1.text(0.02, 0.98, param_text, transform=ax1.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # Plot 2: Error analysis
    # Interpolate HSPICE to fitted voltage points
    current_hspice_interp = np.interp(voltage_fitted, voltage_hspice, current_hspice)
    error_abs = current_hspice_interp - current_fitted
    error_rel = error_abs / current_fitted * 100
    
    ax2.plot(voltage_fitted, error_rel, 'b-', linewidth=2, label='Relative Error (%)')
    ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax2.axhline(y=10, color='r', linestyle=':', alpha=0.5, label='±10% Error')
    ax2.axhline(y=-10, color='r', linestyle=':', alpha=0.5)
    
    ax2.set_xlabel('Voltage (V)')
    ax2.set_ylabel('Relative Error (%)')
    ax2.set_title('HSPICE vs Fitted Model Error')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # Calculate RMS error
    rms_error = np.sqrt(np.mean(error_rel**2))
    max_error = np.max(np.abs(error_rel))
    
    ax2.text(0.02, 0.98, f'RMS Error: {rms_error:.1f}%\nMax Error: {max_error:.1f}%', 
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('model_matching_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("Comparison plot saved as: model_matching_analysis.png")
    
    return rms_error, max_error

def suggest_model_improvements(data):
    """Suggest improvements based on error analysis"""
    if data is None:
        return
    
    voltage_fitted, current_fitted, voltage_hspice, current_hspice, fitted_params = data
    
    print(f"\nModel Improvement Suggestions:")
    print("=" * 40)
    
    # Interpolate for comparison
    current_hspice_interp = np.interp(voltage_fitted, voltage_hspice, current_hspice)
    error_rel = (current_hspice_interp - current_fitted) / current_fitted * 100
    
    # Analyze error in different regions
    low_v_mask = voltage_fitted <= 5
    mid_v_mask = (voltage_fitted > 5) & (voltage_fitted <= 15)
    high_v_mask = voltage_fitted > 15
    
    low_v_error = np.mean(np.abs(error_rel[low_v_mask])) if np.any(low_v_mask) else 0
    mid_v_error = np.mean(np.abs(error_rel[mid_v_mask])) if np.any(mid_v_mask) else 0
    high_v_error = np.mean(np.abs(error_rel[high_v_mask])) if np.any(high_v_mask) else 0
    
    print(f"Error by region:")
    print(f"  Low voltage (0-5V):   {low_v_error:.1f}%")
    print(f"  Mid voltage (5-15V):  {mid_v_error:.1f}%")
    print(f"  High voltage (15-20V): {high_v_error:.1f}%")
    
    # Suggestions
    if low_v_error > 20:
        print(f"\n⚠ High error in low voltage region:")
        print(f"  - Consider adjusting IS parameter")
        print(f"  - Current IS: {fitted_params.get('I_leak', 1e-7)*1e12:.3e}")
    
    if mid_v_error > 20:
        print(f"\n⚠ High error in mid voltage region:")
        print(f"  - Consider adjusting N parameter")
        print(f"  - Current N: {fitted_params.get('k', 3.0):.2f}")
    
    if high_v_error > 20:
        print(f"\n⚠ High error in high voltage region:")
        print(f"  - Consider adjusting RS parameter")
        print(f"  - Current RS: {max(fitted_params.get('Ron', 2.0)/5, 0.01):.3f}")

def main():
    """Main test function"""
    print("BJT ESD Parameter Extractor - Model Matching Test")
    print("=" * 60)
    
    # Test model matching
    data = test_model_matching()
    
    if data:
        # Create comparison plot
        rms_error, max_error = create_comparison_plot(data)
        
        # Suggest improvements
        suggest_model_improvements(data)
        
        # Summary
        print(f"\n" + "=" * 60)
        print(f"Model Matching Summary:")
        print(f"RMS Error: {rms_error:.1f}%")
        print(f"Max Error: {max_error:.1f}%")
        
        if rms_error < 15:
            print("✓ Good model matching (RMS < 15%)")
        elif rms_error < 30:
            print("⚠ Acceptable model matching (15% < RMS < 30%)")
        else:
            print("✗ Poor model matching (RMS > 30%)")
            print("Consider model parameter adjustments")
    else:
        print("✗ Model matching test failed")
    
    # Cleanup
    for filename in ['test_matching_model.ckt', 'test_matching_simulation.sp']:
        if os.path.exists(filename):
            try:
                os.remove(filename)
            except:
                pass

if __name__ == "__main__":
    main()
