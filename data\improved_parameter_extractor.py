"""
Improved Parameter Extractor for BJT ESD Device
Addresses poor fitting issues with better algorithms
"""

import numpy as np
from scipy.optimize import curve_fit, differential_evolution
import warnings
warnings.filterwarnings('ignore')

class ImprovedParameterExtractor:
    """Improved class for extracting BJT ESD device parameters from I-V data"""

    def __init__(self):
        self.debug = True

    def extract_parameters(self, data):
        """
        Extract BJT ESD parameters using improved algorithms

        Args:
            data (dict): Dictionary containing voltage and current arrays

        Returns:
            dict: Dictionary containing extracted parameters
        """
        voltage = data['voltage']
        current = np.abs(data['current'])  # Use absolute values

        if self.debug:
            print(f"Data range: V=[{voltage.min():.2f}, {voltage.max():.2f}], I=[{current.min():.2e}, {current.max():.2e}]")

        try:
            # Step 1: Identify key regions more accurately
            regions = self.identify_regions_improved(voltage, current)

            # Step 2: Extract parameters using global optimization
            parameters = self.extract_parameters_global(voltage, current, regions)

            # Step 3: Refine parameters with local optimization
            refined_params = self.refine_parameters(voltage, current, parameters)

            if self.debug:
                self.print_parameters(refined_params)

            return refined_params

        except Exception as e:
            print(f"Improved parameter extraction failed: {e}")
            return self.get_smart_default_parameters(voltage, current)

    def identify_regions_improved(self, voltage, current):
        """Improved region identification using multiple criteria"""

        # Smooth the data for better analysis
        from scipy.signal import savgol_filter
        if len(current) > 10:
            current_smooth = savgol_filter(current, min(11, len(current)//2*2+1), 3)
        else:
            current_smooth = current

        # Calculate log derivative
        log_current = np.log10(current_smooth + 1e-15)
        dlogI_dV = np.gradient(log_current, voltage)

        # Find leakage region end (where derivative exceeds threshold)
        leakage_threshold = 0.2  # Lower threshold for better detection
        leakage_end_idx = len(voltage) // 3  # Default to 1/3 of data

        for i in range(10, len(dlogI_dV) - 10):
            if dlogI_dV[i] > leakage_threshold and current[i] > current[0] * 10:
                leakage_end_idx = i
                break

        # Find trigger voltage (steepest slope)
        trigger_start_idx = leakage_end_idx
        max_slope_idx = trigger_start_idx + np.argmax(dlogI_dV[trigger_start_idx:])
        trigger_voltage = voltage[max_slope_idx]

        # Find snapback region (where current is high)
        snapback_threshold = current.max() * 0.1  # 10% of max current
        snapback_start_idx = len(voltage) - 1

        for i in range(max_slope_idx, len(current)):
            if current[i] > snapback_threshold:
                snapback_start_idx = i
                break

        if self.debug:
            print(f"Regions: leakage_end={leakage_end_idx}, trigger_V={trigger_voltage:.2f}, snapback_start={snapback_start_idx}")

        return {
            'leakage_end': leakage_end_idx,
            'trigger_voltage': trigger_voltage,
            'trigger_idx': max_slope_idx,
            'snapback_start': snapback_start_idx
        }

    def extract_parameters_global(self, voltage, current, regions):
        """Extract parameters using global optimization"""

        # Define the complete ESD model
        def esd_model(v, i_leak, vt1, k, ron, vh, i_offset, isb, vsb):
            result = np.zeros_like(v)

            for i, volt in enumerate(v):
                if volt < vt1:
                    # Leakage region: exponential
                    result[i] = i_leak * np.exp(volt / 2.0)  # Thermal voltage ~2V
                elif volt < vh:
                    # Trigger region: exponential increase
                    result[i] = i_leak * np.exp(2.0) * np.exp(k * (volt - vt1))
                else:
                    # Snapback region: linear + exponential decay
                    linear_part = (volt - vsb) / ron if ron > 0 else 0
                    exp_part = isb * np.exp(-(volt - vsb) / 2.0) if volt > vsb else isb
                    result[i] = i_offset + max(0, linear_part) + exp_part

            return result

        # Smart initial guess based on data analysis
        initial_guess = self.get_smart_initial_guess(voltage, current, regions)

        # Parameter bounds (wider ranges for global search)
        bounds = [
            (1e-12, 1e-6),    # I_leak
            (5.0, 18.0),      # Vt1
            (0.1, 2.0),       # k
            (0.1, 20.0),      # Ron
            (8.0, 20.0),      # Vh
            (1e-6, 1.0),      # I_offset
            (1e-6, 1.0),      # Isb
            (8.0, 20.0)       # Vsb
        ]

        try:
            # Use differential evolution for global optimization (faster settings)
            result = differential_evolution(
                lambda params: self.objective_function(params, voltage, current, esd_model),
                bounds,
                seed=42,
                maxiter=30,  # Reduced iterations for speed
                popsize=8    # Reduced population for speed
            )

            if result.success:
                params = result.x
                return {
                    'I_leak': params[0],
                    'Vt1': params[1],
                    'k': params[2],
                    'Ron': params[3],
                    'Vh': params[4],
                    'I_offset': params[5],
                    'Isb': params[6],
                    'Vsb': params[7]
                }
            else:
                return initial_guess

        except Exception as e:
            print(f"Global optimization failed: {e}")
            return initial_guess

    def objective_function(self, params, voltage, current, model_func):
        """Objective function for optimization"""
        try:
            model_current = model_func(voltage, *params)

            # Use log-scale error with weights
            log_measured = np.log10(current + 1e-15)
            log_model = np.log10(model_current + 1e-15)

            # Weight different regions differently
            weights = np.ones_like(voltage)
            weights[current < current.max() * 0.01] = 0.5  # Less weight on very low currents
            weights[current > current.max() * 0.1] = 2.0   # More weight on high currents

            error = np.sum(weights * (log_measured - log_model) ** 2)

            # Add penalty for unrealistic parameters
            penalty = 0
            if params[1] < 5 or params[1] > 18:  # Vt1 should be reasonable
                penalty += 1000
            if params[4] < params[1]:  # Vh should be > Vt1
                penalty += 1000

            return error + penalty

        except:
            return 1e10  # Large error for invalid parameters

    def get_smart_initial_guess(self, voltage, current, regions):
        """Generate smart initial guess based on data analysis"""

        # Leakage current: minimum current in first region
        leakage_end = regions['leakage_end']
        i_leak = np.min(current[:leakage_end]) if leakage_end > 0 else current[0]
        i_leak = max(1e-12, i_leak)

        # Trigger voltage: from region analysis
        vt1 = regions['trigger_voltage']
        vt1 = max(5.0, min(18.0, vt1))

        # Exponential factor: estimate from slope
        trigger_idx = regions['trigger_idx']
        if trigger_idx < len(current) - 5:
            v_range = voltage[trigger_idx:trigger_idx+5]
            i_range = current[trigger_idx:trigger_idx+5]
            if len(v_range) > 2:
                log_i = np.log10(i_range + 1e-15)
                slope = np.polyfit(v_range, log_i, 1)[0]
                k = slope / np.log10(np.e)  # Convert to natural log
                k = max(0.1, min(2.0, abs(k)))
            else:
                k = 1.0
        else:
            k = 1.0

        # Holding voltage: slightly higher than trigger
        vh = vt1 + 2.0

        # Snapback parameters
        snapback_start = regions['snapback_start']
        if snapback_start < len(current):
            i_offset = current[snapback_start] * 0.1
            isb = current[snapback_start] * 0.5
            vsb = voltage[snapback_start]

            # On-resistance from high current region
            if snapback_start < len(voltage) - 5:
                v_high = voltage[snapback_start:]
                i_high = current[snapback_start:]
                if len(v_high) > 2:
                    slope = np.polyfit(v_high, i_high, 1)[0]
                    ron = 1.0 / slope if slope > 0 else 5.0
                    ron = max(0.1, min(20.0, ron))
                else:
                    ron = 5.0
            else:
                ron = 5.0
        else:
            i_offset = current[-1] * 0.1
            isb = current[-1] * 0.5
            vsb = voltage[-1]
            ron = 5.0

        return {
            'I_leak': i_leak,
            'Vt1': vt1,
            'k': k,
            'Ron': ron,
            'Vh': vh,
            'I_offset': i_offset,
            'Isb': isb,
            'Vsb': vsb
        }

    def refine_parameters(self, voltage, current, initial_params):
        """Refine parameters using local optimization"""

        def refined_model(v, i_leak, vt1, k, ron, vh, i_offset, isb, vsb):
            result = np.zeros_like(v)

            for i, volt in enumerate(v):
                if volt < vt1:
                    # Leakage region
                    result[i] = i_leak * np.exp(volt / 2.0)
                elif volt < vh:
                    # Trigger region
                    result[i] = i_leak * np.exp(2.0) * np.exp(k * (volt - vt1))
                else:
                    # Snapback region
                    linear_part = (volt - vsb) / ron if ron > 0 else 0
                    exp_part = isb * np.exp(-(volt - vsb) / 2.0) if volt > vsb else isb
                    result[i] = i_offset + max(0, linear_part) + exp_part

            return result

        try:
            # Prepare initial guess and bounds for curve_fit
            p0 = [
                initial_params['I_leak'],
                initial_params['Vt1'],
                initial_params['k'],
                initial_params['Ron'],
                initial_params['Vh'],
                initial_params['I_offset'],
                initial_params['Isb'],
                initial_params['Vsb']
            ]

            # Tighter bounds for local optimization
            bounds = (
                [1e-12, 5.0, 0.1, 0.1, 8.0, 1e-6, 1e-6, 8.0],
                [1e-6, 18.0, 2.0, 20.0, 20.0, 1.0, 1.0, 20.0]
            )

            popt, _ = curve_fit(refined_model, voltage, current, p0=p0, bounds=bounds, maxfev=1000)

            return {
                'I_leak': popt[0],
                'Vt1': popt[1],
                'k': popt[2],
                'Ron': popt[3],
                'Vh': popt[4],
                'I_offset': popt[5],
                'Isb': popt[6],
                'Vsb': popt[7]
            }

        except Exception as e:
            print(f"Parameter refinement failed: {e}")
            return initial_params

    def get_smart_default_parameters(self, voltage, current):
        """Get smart default parameters based on data characteristics"""

        v_max = voltage.max()
        i_max = current.max()
        i_min = current.min()

        return {
            'I_leak': max(1e-12, i_min),
            'Vt1': v_max * 0.6,  # 60% of max voltage
            'k': 1.0,
            'Ron': v_max / i_max if i_max > 0 else 5.0,
            'Vh': v_max * 0.7,   # 70% of max voltage
            'I_offset': i_max * 0.1,
            'Isb': i_max * 0.5,
            'Vsb': v_max * 0.8   # 80% of max voltage
        }

    def print_parameters(self, params):
        """Print parameters for debugging"""
        print("Extracted parameters:")
        for key, value in params.items():
            if 'I_' in key or 'Isb' in key:
                print(f"  {key}: {value:.3e}")
            else:
                print(f"  {key}: {value:.3f}")

    def calculate_model_current(self, voltage, parameters):
        """Calculate model current using improved BJT ESD model"""

        i_leak = parameters.get('I_leak', 1e-9)
        vt1 = parameters.get('Vt1', 12.0)
        k = parameters.get('k', 1.0)
        ron = parameters.get('Ron', 5.0)
        vh = parameters.get('Vh', 14.0)
        i_offset = parameters.get('I_offset', 0.05)
        isb = parameters.get('Isb', 0.04)
        vsb = parameters.get('Vsb', 14.0)

        current = np.zeros_like(voltage)

        for i, v in enumerate(voltage):
            if v < vt1:
                # Leakage region: exponential with thermal voltage
                current[i] = i_leak * np.exp(v / 2.0)
            elif v < vh:
                # Trigger region: exponential increase
                current[i] = i_leak * np.exp(2.0) * np.exp(k * (v - vt1))
            else:
                # Snapback region: linear + exponential decay
                linear_part = (v - vsb) / ron if ron > 0 else 0
                exp_part = isb * np.exp(-(v - vsb) / 2.0) if v > vsb else isb
                current[i] = i_offset + max(0, linear_part) + exp_part

        return current
