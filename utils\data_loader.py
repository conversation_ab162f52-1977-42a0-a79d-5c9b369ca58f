"""
Data loading utilities for BJT ESD parameter extraction
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional, Dict, Any
import logging
import os

logger = logging.getLogger(__name__)

class DataLoader:
    """
    Data loader for experimental I-V data
    """

    def __init__(self):
        self.supported_formats = ['.csv', '.txt', '.dat']

    def load_csv_data(self, file_path: str) -> Tuple[np.ndarray, np.ndarray, Dict[str, Any]]:
        """
        Load I-V data from CSV file

        Args:
            file_path: Path to CSV file

        Returns:
            Tuple of (voltage, current, metadata)
        """
        try:
            # Read CSV file, skip comment lines and empty lines
            df = pd.read_csv(file_path, comment='#', skip_blank_lines=True)

            # Remove any completely empty rows
            df = df.dropna(how='all')

            # Extract voltage and current columns
            if 'Voltage' in df.columns and 'Current' in df.columns:
                voltage = df['Voltage'].values
                current = df['Current'].values
            elif len(df.columns) >= 2:
                # Assume first two columns are voltage and current
                voltage = df.iloc[:, 0].values
                current = df.iloc[:, 1].values
            else:
                raise ValueError("CSV file must contain at least two columns")

            # Convert to numeric, coercing errors to NaN
            voltage = pd.to_numeric(voltage, errors='coerce')
            current = pd.to_numeric(current, errors='coerce')

            # Remove any NaN values
            mask = ~(np.isnan(voltage) | np.isnan(current))
            voltage = voltage[mask]
            current = current[mask]

            # Convert to numpy arrays
            voltage = np.array(voltage, dtype=float)
            current = np.array(current, dtype=float)

            # Extract metadata if present (lines starting with #)
            metadata = self._extract_metadata_from_csv(file_path)

            logger.info(f"Loaded {len(voltage)} data points from {file_path}")
            return voltage, current, metadata

        except Exception as e:
            logger.error(f"Failed to load CSV data from {file_path}: {str(e)}")
            raise

    def _extract_metadata_from_csv(self, file_path: str) -> Dict[str, Any]:
        """
        Extract metadata from CSV file (lines starting with #)

        Args:
            file_path: Path to CSV file

        Returns:
            Dictionary of metadata
        """
        metadata = {}

        try:
            with open(file_path, 'r') as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if line.startswith('#') and ',' in line:
                    # Parse parameter lines like "# I_leak,1.9981e-07"
                    parts = line[1:].strip().split(',')
                    if len(parts) == 2:
                        key = parts[0].strip()
                        try:
                            value = float(parts[1].strip())
                            metadata[key] = value
                        except ValueError:
                            metadata[key] = parts[1].strip()

        except Exception as e:
            logger.warning(f"Could not extract metadata from {file_path}: {str(e)}")

        return metadata

    def load_data(self, file_path: str) -> Tuple[np.ndarray, np.ndarray, Dict[str, Any]]:
        """
        Load I-V data from file (auto-detect format)

        Args:
            file_path: Path to data file

        Returns:
            Tuple of (voltage, current, metadata)
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == '.csv':
            return self.load_csv_data(file_path)
        elif file_ext in ['.txt', '.dat']:
            return self.load_text_data(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")

    def load_text_data(self, file_path: str) -> Tuple[np.ndarray, np.ndarray, Dict[str, Any]]:
        """
        Load I-V data from text file

        Args:
            file_path: Path to text file

        Returns:
            Tuple of (voltage, current, metadata)
        """
        try:
            # Try to load as space/tab separated values
            data = np.loadtxt(file_path, comments='#')

            if data.shape[1] < 2:
                raise ValueError("Text file must contain at least two columns")

            voltage = data[:, 0]
            current = data[:, 1]

            # Extract metadata
            metadata = self._extract_metadata_from_text(file_path)

            logger.info(f"Loaded {len(voltage)} data points from {file_path}")
            return voltage, current, metadata

        except Exception as e:
            logger.error(f"Failed to load text data from {file_path}: {str(e)}")
            raise

    def _extract_metadata_from_text(self, file_path: str) -> Dict[str, Any]:
        """
        Extract metadata from text file (comment lines)

        Args:
            file_path: Path to text file

        Returns:
            Dictionary of metadata
        """
        metadata = {}

        try:
            with open(file_path, 'r') as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if line.startswith('#'):
                    # Try to parse key-value pairs
                    content = line[1:].strip()
                    if ':' in content:
                        parts = content.split(':', 1)
                        key = parts[0].strip()
                        try:
                            value = float(parts[1].strip())
                            metadata[key] = value
                        except ValueError:
                            metadata[key] = parts[1].strip()

        except Exception as e:
            logger.warning(f"Could not extract metadata from {file_path}: {str(e)}")

        return metadata

    def validate_data(self, voltage: np.ndarray, current: np.ndarray) -> bool:
        """
        Validate I-V data

        Args:
            voltage: Voltage array
            current: Current array

        Returns:
            True if data is valid
        """
        if len(voltage) != len(current):
            logger.error("Voltage and current arrays must have same length")
            return False

        if len(voltage) < 10:
            logger.error("Need at least 10 data points for parameter extraction")
            return False

        if np.any(np.isnan(voltage)) or np.any(np.isnan(current)):
            logger.error("Data contains NaN values")
            return False

        if np.any(np.isinf(voltage)) or np.any(np.isinf(current)):
            logger.error("Data contains infinite values")
            return False

        return True

    def preprocess_data(self, voltage: np.ndarray, current: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess I-V data (sorting, filtering)

        Args:
            voltage: Voltage array
            current: Current array

        Returns:
            Tuple of preprocessed (voltage, current)
        """
        # Sort by voltage
        sort_idx = np.argsort(voltage)
        voltage_sorted = voltage[sort_idx]
        current_sorted = current[sort_idx]

        # Remove duplicates
        unique_idx = np.unique(voltage_sorted, return_index=True)[1]
        voltage_unique = voltage_sorted[unique_idx]
        current_unique = current_sorted[unique_idx]

        logger.info(f"Preprocessed data: {len(voltage)} -> {len(voltage_unique)} points")
        return voltage_unique, current_unique
