#!/usr/bin/env python3
"""
Test the new high-accuracy HSPICE model
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_new_model():
    """Test the new high-accuracy model generation"""
    try:
        from utils.hspice_interface import HSPICEInterface
        from models.bjt_esd_model import BJTESDModel
        from utils.data_loader import DataLoader
        
        print("Testing new high-accuracy HSPICE model...")
        
        # Load experimental data and fit model
        loader = DataLoader()
        voltage, current, _ = loader.load_data('1.csv')
        model = BJTESDModel()
        fitted_params = model.fit_parameters(voltage, current)
        
        print("Fitted parameters:")
        for key, value in fitted_params.items():
            if key != 'r_squared':
                print(f"  {key}: {value:.6e}")
        
        # Generate new high-accuracy model
        hspice = HSPICEInterface()
        model_content = hspice.generate_spice_model(fitted_params)
        
        # Save the model
        with open('high_accuracy_bjt_esd_model.ckt', 'w') as f:
            f.write(model_content)
        
        print(f"\n✓ High-accuracy model saved to: high_accuracy_bjt_esd_model.ckt")
        print(f"Model size: {len(model_content)} characters")
        
        # Show the circuit definition
        lines = model_content.split('\n')
        print("\nNew circuit definition:")
        in_circuit = False
        for i, line in enumerate(lines):
            if '.subckt bjt_esd_device' in line:
                in_circuit = True
            if in_circuit:
                print(f"  {i+1}: {line}")
                if '.ends' in line:
                    break
        
        # Generate corresponding netlist
        netlist_content = hspice.generate_netlist(fitted_params, (0, 20), 100, 'high_accuracy_bjt_esd_model.ckt')
        with open('high_accuracy_bjt_esd_simulation.sp', 'w') as f:
            f.write(netlist_content)
        
        print(f"\n✓ High-accuracy netlist saved to: high_accuracy_bjt_esd_simulation.sp")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hspice_simulation():
    """Test HSPICE simulation with the new model"""
    try:
        from utils.hspice_interface import HSPICEInterface
        
        hspice = HSPICEInterface()
        
        if not hspice.verify_hspice_installation():
            print("! HSPICE not available, skipping simulation test")
            return True
        
        print("\nRunning HSPICE simulation with high-accuracy model...")
        result = hspice.run_simulation_from_netlist('high_accuracy_bjt_esd_simulation.sp')
        
        if result:
            voltage, current = result
            print(f"✓ HSPICE simulation completed successfully")
            print(f"  Generated {len(voltage)} data points")
            print(f"  Voltage range: {voltage.min():.3f} to {voltage.max():.3f} V")
            print(f"  Current range: {abs(current.min()):.3e} to {abs(current.max()):.3e} A")
            
            # Show key data points
            print("\nKey simulation results:")
            test_voltages = [1, 5, 10, 15, 20]
            for v_test in test_voltages:
                if v_test <= voltage.max():
                    import numpy as np
                    i_sim = abs(np.interp(v_test, voltage, current))
                    print(f"  V={v_test:2.0f}V: I={i_sim:.3f}A")
            
            return True
        else:
            print("✗ HSPICE simulation failed")
            return False
            
    except Exception as e:
        print(f"✗ HSPICE simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run high-accuracy model tests"""
    print("BJT ESD Parameter Extractor - High-Accuracy Model Test")
    print("=" * 60)
    
    tests = [
        test_new_model,
        test_hspice_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"High-Accuracy Model Test Results: {passed}/{total} tests passed")
    
    if passed >= 1:
        print("✓ High-accuracy model generation is working!")
        if passed < total:
            print("Note: Some tests failed, but core functionality is working.")
    else:
        print("✗ High-accuracy model has issues. Please check the error messages above.")
    
    print("\nGenerated files:")
    for filename in ["high_accuracy_bjt_esd_model.ckt", "high_accuracy_bjt_esd_simulation.sp"]:
        if os.path.exists(filename):
            print(f"  {filename} - {os.path.getsize(filename)} bytes")
    
    return passed >= 1

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
