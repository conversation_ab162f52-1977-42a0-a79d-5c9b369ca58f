****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******
Input File: mos.sp
lic:
lic: FLEXlm: SDK_12.3
lic: USER:   e02727               HOSTNAME: ascend27
lic: HOSTID: "982cbcdcead7"       PID:      32348
lic: Using FLEXlm license file:
lic: 27000@ascend27
lic: Checkout 1 hspice
lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12
lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@ascend27
lic:

 init: begin read circuit files, cpu clock= 3.04E-01
       option post =     2.00
       option accurate
       option probe
       option converge =     1.00
       option ingold =     1.00
       option gmindc =    1.000E-14
 init: end read circuit files, cpu clock= 3.08E-01 peak memory=      62 mb
 init: begin check errors, cpu clock= 3.08E-01
 init: end check errors, cpu clock= 3.13E-01 peak memory=      62 mb
 init: begin setup matrix, pivot=     0 cpu clock= 3.13E-01
       establish matrix -- done, cpu clock= 3.13E-01 peak memory=      62 mb
       re-order matrix -- done, cpu clock= 3.14E-01 peak memory=      62 mb
 init: end setup matrix, cpu clock= 3.14E-01 peak memory=      62 mb
 voltage: vg              =    0.00E+00
 output: mos.sw0
 sweep: dc dc1    begin, #sweeps= 101 cpu clock= 3.15E-01
 sweep: dc dc1    end, cpu clock= 3.20E-01 peak memory=      62 mb
 voltage: vg              =    5.00E-01
 output: mos.sw1
 sweep: dc dc1    begin, #sweeps= 101 cpu clock= 3.22E-01
 sweep: dc dc1    end, cpu clock= 3.26E-01 peak memory=      62 mb
 voltage: vg              =    1.00E+00
 output: mos.sw2
 sweep: dc dc1    begin, #sweeps= 101 cpu clock= 3.27E-01
 sweep: dc dc1    end, cpu clock= 3.34E-01 peak memory=      62 mb
>info:         ***** hspice job concluded
 lic: Release hspice token(s)
