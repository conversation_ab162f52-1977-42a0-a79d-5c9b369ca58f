#!/usr/bin/env python3
"""
Debug Model Calculation
Find out why calculate_model_current returns zeros
"""

import sys
import os
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_model_calculation():
    """Debug the model calculation step by step"""
    print("DEBUGGING MODEL CALCULATION")
    print("=" * 40)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        # Load data and extract parameters
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        print("Parameters:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"  {key}: {value:.6e}")
            else:
                print(f"  {key}: {value:.6f}")
        
        # Test with a single voltage point
        test_voltage = np.array([10.0])
        
        print(f"\nTesting with voltage = {test_voltage[0]}V")
        print(f"Vt1 = {parameters['Vt1']:.3f}V")
        print(f"Vh = {parameters['Vh']:.3f}V")
        
        # Determine which region
        v = test_voltage[0]
        if v < parameters['Vt1']:
            region = "Leakage"
            expected = parameters['I_leak'] * np.exp(v / 1.0)
        elif v < parameters['Vh']:
            region = "Trigger"
            expected = parameters['I_leak'] * np.exp(parameters['k'] * (v - parameters['Vt1']) / parameters['Vt1'])
        else:
            region = "Snapback"
            linear_part = (v - parameters['Vsb']) / parameters['Ron'] if parameters['Ron'] > 0 else 0
            exp_part = parameters['Isb'] * np.exp(-(v - parameters['Vsb'])) if v > parameters['Vsb'] else parameters['Isb']
            expected = parameters['I_offset'] + linear_part + exp_part
        
        print(f"Expected region: {region}")
        print(f"Expected current: {expected:.6e}A")
        
        # Call the method
        result = extractor.calculate_model_current(test_voltage, parameters)
        print(f"Method result: {result[0]:.6e}A")
        
        if abs(result[0] - expected) < 1e-15:
            print("✓ Method works correctly")
        else:
            print("❌ Method has issues")
            
        # Test with multiple points
        test_voltages = np.array([0, 1, 5, 9, 10, 12, 15, 18, 20])
        results = extractor.calculate_model_current(test_voltages, parameters)
        
        print(f"\nMultiple voltage test:")
        print(f"{'V':>3} | {'Current':>12} | {'Region':>10}")
        print("-" * 30)
        
        for i, v in enumerate(test_voltages):
            if v < parameters['Vt1']:
                region = "Leakage"
            elif v < parameters['Vh']:
                region = "Trigger"
            else:
                region = "Snapback"
                
            print(f"{v:3.0f} | {results[i]:12.3e} | {region:>10}")
        
        # Check if all results are zero
        if np.all(results == 0):
            print("\n❌ ALL RESULTS ARE ZERO - METHOD IS BROKEN")
            return False
        else:
            print(f"\n✓ Method returns non-zero values")
            return True
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_calculation():
    """Test manual calculation to verify the formula"""
    print(f"\nTESTING MANUAL CALCULATION")
    print("=" * 40)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        # Manual calculation for voltage = 10V
        v = 10.0
        i_leak = parameters['I_leak']
        vt1 = parameters['Vt1']
        k = parameters['k']
        vh = parameters['Vh']
        
        print(f"Manual calculation for V = {v}V:")
        print(f"  I_leak = {i_leak:.6e}")
        print(f"  Vt1 = {vt1:.3f}")
        print(f"  k = {k:.3f}")
        print(f"  Vh = {vh:.3f}")
        
        if v < vt1:
            print(f"  Region: Leakage (V < Vt1)")
            manual_result = i_leak * np.exp(v / 1.0)
            print(f"  Formula: I_leak * exp(V/1.0)")
            print(f"  Calculation: {i_leak:.6e} * exp({v}/1.0)")
            print(f"  Result: {manual_result:.6e}")
        elif v < vh:
            print(f"  Region: Trigger (Vt1 <= V < Vh)")
            manual_result = i_leak * np.exp(k * (v - vt1) / vt1)
            print(f"  Formula: I_leak * exp(k * (V - Vt1) / Vt1)")
            print(f"  Calculation: {i_leak:.6e} * exp({k:.3f} * ({v} - {vt1:.3f}) / {vt1:.3f})")
            exp_arg = k * (v - vt1) / vt1
            print(f"  Exp argument: {exp_arg:.6f}")
            print(f"  Result: {manual_result:.6e}")
        else:
            print(f"  Region: Snapback (V >= Vh)")
            ron = parameters['Ron']
            i_offset = parameters['I_offset']
            isb = parameters['Isb']
            vsb = parameters['Vsb']
            
            linear_part = (v - vsb) / ron if ron > 0 else 0
            exp_part = isb * np.exp(-(v - vsb)) if v > vsb else isb
            manual_result = i_offset + linear_part + exp_part
            
            print(f"  Linear part: ({v} - {vsb:.3f}) / {ron:.3f} = {linear_part:.6e}")
            print(f"  Exp part: {isb:.6e} * exp(-({v} - {vsb:.3f})) = {exp_part:.6e}")
            print(f"  Total: {i_offset:.6e} + {linear_part:.6e} + {exp_part:.6e} = {manual_result:.6e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Manual calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("MODEL CALCULATION DEBUG")
    print("=" * 50)
    
    # Test 1: Debug the method
    method_ok = debug_model_calculation()
    
    # Test 2: Manual calculation
    manual_ok = test_manual_calculation()
    
    print("\n" + "=" * 50)
    print("DEBUG SUMMARY")
    print("=" * 50)
    
    if method_ok:
        print("✅ calculate_model_current method works")
    else:
        print("❌ calculate_model_current method is broken")
        
    if manual_ok:
        print("✅ Manual calculation works")
    else:
        print("❌ Manual calculation failed")
    
    if method_ok and manual_ok:
        print("\n🎉 Model calculation is working correctly")
        print("The issue must be elsewhere")
    else:
        print("\n❌ Model calculation has issues")
        print("Need to fix the calculation method")
    
    return method_ok and manual_ok

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
