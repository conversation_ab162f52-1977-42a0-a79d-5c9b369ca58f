"""
BJT ESD Device Model Class
"""

import numpy as np
from datetime import datetime

class BJTESDModel:
    """Class representing a BJT ESD device model"""
    
    def __init__(self, parameters=None):
        """
        Initialize BJT ESD model
        
        Args:
            parameters (dict): Model parameters
        """
        if parameters is None:
            self.parameters = self.get_default_parameters()
        else:
            self.parameters = parameters.copy()
            
        self.model_type = "BJT_ESD"
        self.version = "1.0"
        
    def get_default_parameters(self):
        """Get default model parameters"""
        return {
            'I_leak': 1e-9,      # Leakage current (A)
            'Vt1': 12.0,         # Trigger voltage (V)
            'k': 3.0,            # Exponential factor
            'Ron': 2.0,          # On resistance (Ohm)
            'Vh': 14.0,          # Holding voltage (V)
            'I_offset': 0.05,    # Current offset (A)
            'Isb': 0.04,         # Snapback current (A)
            'Vsb': 14.0          # Snapback voltage (V)
        }
        
    def set_parameters(self, parameters):
        """Set model parameters"""
        self.parameters.update(parameters)
        
    def get_parameters(self):
        """Get model parameters"""
        return self.parameters.copy()
        
    def calculate_current(self, voltage):
        """
        Calculate current for given voltage using BJT ESD model
        
        Args:
            voltage (float or array): Input voltage(s)
            
        Returns:
            float or array: Calculated current(s)
        """
        # Handle both scalar and array inputs
        is_scalar = np.isscalar(voltage)
        v = np.atleast_1d(voltage)
        
        # Extract parameters
        i_leak = self.parameters['I_leak']
        vt1 = self.parameters['Vt1']
        k = self.parameters['k']
        ron = self.parameters['Ron']
        vh = self.parameters['Vh']
        i_offset = self.parameters['I_offset']
        isb = self.parameters['Isb']
        vsb = self.parameters['Vsb']
        
        current = np.zeros_like(v)
        
        for i, voltage_val in enumerate(v):
            if voltage_val < 0:
                # Negative voltage - simple reverse bias
                current[i] = i_leak * 1e-3
            elif voltage_val < vt1:
                # Leakage region - exponential increase
                current[i] = i_leak * np.exp(voltage_val / 1.0)
            elif voltage_val < vh:
                # Trigger region - rapid exponential increase
                current[i] = i_leak * np.exp(k * (voltage_val - vt1) / vt1)
            else:
                # Snapback region - linear with exponential component
                if ron > 0:
                    linear_term = (voltage_val - vsb) / ron
                else:
                    linear_term = 0
                    
                if voltage_val > vsb:
                    exp_term = isb * np.exp(-(voltage_val - vsb))
                else:
                    exp_term = isb
                    
                current[i] = i_offset + linear_term + exp_term
                
        # Return scalar if input was scalar
        if is_scalar:
            return current[0]
        else:
            return current
            
    def calculate_iv_curve(self, voltage_range, num_points=1000):
        """
        Calculate I-V curve over specified voltage range
        
        Args:
            voltage_range (tuple): (min_voltage, max_voltage)
            num_points (int): Number of points to calculate
            
        Returns:
            tuple: (voltage_array, current_array)
        """
        v_min, v_max = voltage_range
        voltage = np.linspace(v_min, v_max, num_points)
        current = self.calculate_current(voltage)
        
        return voltage, current
        
    def get_trigger_voltage(self):
        """Get trigger voltage"""
        return self.parameters['Vt1']
        
    def get_holding_voltage(self):
        """Get holding voltage"""
        return self.parameters['Vh']
        
    def get_on_resistance(self):
        """Get on resistance"""
        return self.parameters['Ron']
        
    def get_leakage_current(self, voltage=1.0):
        """Get leakage current at specified voltage"""
        if voltage < self.parameters['Vt1']:
            return self.calculate_current(voltage)
        else:
            return self.parameters['I_leak']
            
    def validate_parameters(self):
        """
        Validate model parameters
        
        Returns:
            tuple: (is_valid, error_messages)
        """
        errors = []
        
        # Check parameter ranges
        if self.parameters['I_leak'] <= 0:
            errors.append("I_leak must be positive")
            
        if self.parameters['Vt1'] <= 0:
            errors.append("Vt1 must be positive")
            
        if self.parameters['k'] <= 0:
            errors.append("k must be positive")
            
        if self.parameters['Ron'] <= 0:
            errors.append("Ron must be positive")
            
        if self.parameters['Vh'] <= 0:
            errors.append("Vh must be positive")
            
        if self.parameters['I_offset'] < 0:
            errors.append("I_offset must be non-negative")
            
        if self.parameters['Isb'] < 0:
            errors.append("Isb must be non-negative")
            
        # Check parameter relationships
        if self.parameters['Vh'] < self.parameters['Vt1']:
            errors.append("Vh should be greater than Vt1")
            
        if self.parameters['Vsb'] < self.parameters['Vt1']:
            errors.append("Vsb should be greater than Vt1")
            
        return len(errors) == 0, errors
        
    def get_model_info(self):
        """Get model information"""
        return {
            'model_type': self.model_type,
            'version': self.version,
            'parameters': self.parameters,
            'description': 'BJT ESD Protection Device Model',
            'regions': {
                'leakage': f'V < {self.parameters["Vt1"]:.2f}V',
                'trigger': f'{self.parameters["Vt1"]:.2f}V < V < {self.parameters["Vh"]:.2f}V',
                'snapback': f'V > {self.parameters["Vh"]:.2f}V'
            }
        }
        
    def export_parameters(self, file_path, format='txt'):
        """
        Export parameters to file
        
        Args:
            file_path (str): Output file path
            format (str): Export format ('txt', 'csv', 'json')
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if format.lower() == 'txt':
            with open(file_path, 'w') as f:
                f.write(f"# BJT ESD Model Parameters\n")
                f.write(f"# Generated: {timestamp}\n")
                f.write(f"# Model Type: {self.model_type} v{self.version}\n\n")
                
                for param, value in self.parameters.items():
                    if param == 'I_leak':
                        f.write(f"{param} = {value:.6e} A  # Leakage Current\n")
                    elif param == 'Vt1':
                        f.write(f"{param} = {value:.3f} V  # Trigger Voltage\n")
                    elif param == 'k':
                        f.write(f"{param} = {value:.3f}  # Exponential Factor\n")
                    elif param == 'Ron':
                        f.write(f"{param} = {value:.3f} Ohm  # On Resistance\n")
                    elif param == 'Vh':
                        f.write(f"{param} = {value:.3f} V  # Holding Voltage\n")
                    elif param == 'I_offset':
                        f.write(f"{param} = {value:.6f} A  # Current Offset\n")
                    elif param == 'Isb':
                        f.write(f"{param} = {value:.6f} A  # Snapback Current\n")
                    elif param == 'Vsb':
                        f.write(f"{param} = {value:.3f} V  # Snapback Voltage\n")
                        
        elif format.lower() == 'csv':
            import csv
            with open(file_path, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['Parameter', 'Value', 'Unit', 'Description'])
                
                descriptions = {
                    'I_leak': ('A', 'Leakage Current'),
                    'Vt1': ('V', 'Trigger Voltage'),
                    'k': ('', 'Exponential Factor'),
                    'Ron': ('Ohm', 'On Resistance'),
                    'Vh': ('V', 'Holding Voltage'),
                    'I_offset': ('A', 'Current Offset'),
                    'Isb': ('A', 'Snapback Current'),
                    'Vsb': ('V', 'Snapback Voltage')
                }
                
                for param, value in self.parameters.items():
                    unit, desc = descriptions.get(param, ('', ''))
                    writer.writerow([param, value, unit, desc])
                    
        elif format.lower() == 'json':
            import json
            data = {
                'model_info': {
                    'type': self.model_type,
                    'version': self.version,
                    'timestamp': timestamp
                },
                'parameters': self.parameters
            }
            
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)
                
        else:
            raise ValueError(f"Unsupported export format: {format}")
