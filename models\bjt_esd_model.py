"""
BJT ESD Device Model
Implements the mathematical model for pnp BJT ESD protection device
"""

import numpy as np
from scipy.optimize import curve_fit
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class BJTESDModel:
    """
    BJT ESD device model for parameter extraction
    
    The model describes the I-V characteristics of a pnp BJT ESD device
    with emitter as anode and base-collector shorted as cathode
    """
    
    def __init__(self):
        self.parameters = {
            'I_leak': 1.9981e-07,  # Leakage current (A)
            'Vt1': 12.645,         # Trigger voltage (V)
            'k': 3.0,              # Exponential factor
            'Ron': 1.962,          # On-resistance (Ohm)
            'Vh': 13.876,          # Holding voltage (V)
            'I_offset': 0.05,      # Current offset (A)
            'Isb': 0.03997,        # Snapback current (A)
            'Vsb': 13.82           # Snapback voltage (V)
        }
        
    def current_equation(self, voltage: np.ndarray, **params) -> np.ndarray:
        """
        Calculate current based on voltage using BJT ESD model
        
        Args:
            voltage: Input voltage array
            **params: Model parameters
            
        Returns:
            Current array
        """
        V = np.asarray(voltage)
        
        # Extract parameters
        I_leak = params.get('I_leak', self.parameters['I_leak'])
        Vt1 = params.get('Vt1', self.parameters['Vt1'])
        k = params.get('k', self.parameters['k'])
        Ron = params.get('Ron', self.parameters['Ron'])
        Vh = params.get('Vh', self.parameters['Vh'])
        I_offset = params.get('I_offset', self.parameters['I_offset'])
        Isb = params.get('Isb', self.parameters['Isb'])
        Vsb = params.get('Vsb', self.parameters['Vsb'])
        
        # Initialize current array
        I = np.zeros_like(V)
        
        # Region 1: Leakage region (V < Vt1)
        mask1 = V < Vt1
        I[mask1] = I_leak * np.exp(V[mask1] / Vt1)
        
        # Region 2: Trigger region (Vt1 <= V < Vh)
        mask2 = (V >= Vt1) & (V < Vh)
        I[mask2] = I_leak * np.exp(k * (V[mask2] - Vt1) / Vt1)
        
        # Region 3: Snapback region (V >= Vh)
        mask3 = V >= Vh
        # Linear region with snapback characteristics
        I[mask3] = I_offset + (V[mask3] - Vsb) / Ron + Isb * np.exp(-(V[mask3] - Vsb))
        
        return I
    
    def fit_parameters(self, voltage: np.ndarray, current: np.ndarray) -> Dict[str, float]:
        """
        Fit model parameters to experimental data
        
        Args:
            voltage: Experimental voltage data
            current: Experimental current data
            
        Returns:
            Dictionary of fitted parameters
        """
        try:
            # Define parameter bounds
            lower_bounds = [1e-12, 1.0, 0.1, 0.1, 1.0, 0.001, 0.001, 1.0]
            upper_bounds = [1e-6, 50.0, 10.0, 100.0, 50.0, 1.0, 1.0, 50.0]
            
            # Initial guess
            p0 = list(self.parameters.values())
            
            # Perform curve fitting
            popt, pcov = curve_fit(
                lambda v, I_leak, Vt1, k, Ron, Vh, I_offset, Isb, Vsb: 
                self.current_equation(v, I_leak=I_leak, Vt1=Vt1, k=k, Ron=Ron, 
                                    Vh=Vh, I_offset=I_offset, Isb=Isb, Vsb=Vsb),
                voltage, current,
                p0=p0,
                bounds=(lower_bounds, upper_bounds),
                maxfev=5000
            )
            
            # Update parameters
            param_names = list(self.parameters.keys())
            fitted_params = dict(zip(param_names, popt))
            
            # Calculate fitting quality
            r_squared = self.calculate_r_squared(voltage, current, fitted_params)
            fitted_params['r_squared'] = r_squared
            
            logger.info(f"Parameter fitting completed with R² = {r_squared:.4f}")
            return fitted_params
            
        except Exception as e:
            logger.error(f"Parameter fitting failed: {str(e)}")
            return self.parameters.copy()
    
    def calculate_r_squared(self, voltage: np.ndarray, current_measured: np.ndarray, 
                          params: Dict[str, float]) -> float:
        """
        Calculate R-squared value for model fit quality
        
        Args:
            voltage: Voltage data
            current_measured: Measured current data
            params: Model parameters
            
        Returns:
            R-squared value
        """
        current_fitted = self.current_equation(voltage, **params)
        ss_res = np.sum((current_measured - current_fitted) ** 2)
        ss_tot = np.sum((current_measured - np.mean(current_measured)) ** 2)
        
        if ss_tot == 0:
            return 0.0
        
        return 1 - (ss_res / ss_tot)
    
    def get_parameter_info(self) -> Dict[str, Dict[str, str]]:
        """
        Get parameter information including descriptions and units
        
        Returns:
            Dictionary with parameter information
        """
        return {
            'I_leak': {'description': 'Leakage Current', 'unit': 'A', 'type': 'exponential'},
            'Vt1': {'description': 'Trigger Voltage', 'unit': 'V', 'type': 'linear'},
            'k': {'description': 'Exponential Factor', 'unit': '', 'type': 'linear'},
            'Ron': {'description': 'On Resistance', 'unit': 'Ω', 'type': 'linear'},
            'Vh': {'description': 'Holding Voltage', 'unit': 'V', 'type': 'linear'},
            'I_offset': {'description': 'Current Offset', 'unit': 'A', 'type': 'linear'},
            'Isb': {'description': 'Snapback Current', 'unit': 'A', 'type': 'linear'},
            'Vsb': {'description': 'Snapback Voltage', 'unit': 'V', 'type': 'linear'}
        }
    
    def update_parameters(self, new_params: Dict[str, float]):
        """
        Update model parameters
        
        Args:
            new_params: Dictionary of new parameter values
        """
        for key, value in new_params.items():
            if key in self.parameters:
                self.parameters[key] = value
                
    def get_parameters(self) -> Dict[str, float]:
        """
        Get current model parameters
        
        Returns:
            Dictionary of current parameters
        """
        return self.parameters.copy()
