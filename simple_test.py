#!/usr/bin/env python3
"""
Simple test for BJT ESD Parameter Extractor
"""

import sys
import os
import numpy as np
import pandas as pd

def test_data_loading():
    """Test loading the CSV data"""
    try:
        print("Testing data loading...")
        
        # Load the CSV file
        if os.path.exists("1.csv"):
            df = pd.read_csv("1.csv")
            print(f"✓ Loaded CSV with {len(df)} rows")
            print(f"  Columns: {list(df.columns)}")
            
            # Get voltage and current
            voltage = df.iloc[:, 0].values
            current = df.iloc[:, 1].values
            
            print(f"  Voltage range: {voltage.min():.3f} to {voltage.max():.3f} V")
            print(f"  Current range: {current.min():.3e} to {current.max():.3e} A")
            
            return voltage, current
        else:
            print("✗ File 1.csv not found")
            return None, None
            
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return None, None

def extract_basic_parameters(voltage, current):
    """Extract basic parameters from I-V data"""
    try:
        print("\nExtracting basic parameters...")
        
        # Find trigger voltage (where current starts increasing rapidly)
        log_current = np.log10(np.abs(current) + 1e-15)
        d_log_i = np.gradient(log_current, voltage)
        
        # Trigger voltage
        trigger_idx = np.argmax(d_log_i)
        vt1 = voltage[trigger_idx]
        
        # Leakage current (average of first few points)
        i_leak = np.mean(current[:10])
        
        # Find snapback region (high current region)
        high_current_mask = current > 0.01  # 10mA threshold
        if np.any(high_current_mask):
            vh = voltage[high_current_mask][0]  # First point above threshold
            
            # On resistance from linear region
            linear_region = current > 0.1  # 100mA threshold
            if np.any(linear_region):
                v_linear = voltage[linear_region]
                i_linear = current[linear_region]
                if len(v_linear) > 2:
                    slope = np.polyfit(v_linear, i_linear, 1)[0]
                    ron = 1.0 / slope if slope > 0 else 2.0
                else:
                    ron = 2.0
            else:
                ron = 2.0
        else:
            vh = 14.0
            ron = 2.0
        
        parameters = {
            'I_leak': abs(i_leak),
            'Vt1': vt1,
            'k': 3.0,
            'Ron': ron,
            'Vh': vh,
            'I_offset': 0.05,
            'Isb': 0.04,
            'Vsb': vh
        }
        
        print("✓ Parameters extracted:")
        for param, value in parameters.items():
            if param == 'I_leak':
                print(f"  {param}: {value:.3e} A")
            else:
                print(f"  {param}: {value:.3f}")
        
        return parameters
        
    except Exception as e:
        print(f"✗ Error extracting parameters: {e}")
        return None

def generate_spice_model(parameters):
    """Generate SPICE model"""
    try:
        print("\nGenerating SPICE model...")
        
        spice_content = f"""* BJT ESD Device SPICE Model
* Generated by BJT ESD Parameter Extractor

.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak={parameters['I_leak']:.6e}
.param Vt1={parameters['Vt1']:.3f}
.param k={parameters['k']:.3f}
.param Ron={parameters['Ron']:.3f}
.param Vh={parameters['Vh']:.3f}
.param I_offset={parameters['I_offset']:.6f}
.param Isb={parameters['Isb']:.6f}
.param Vsb={parameters['Vsb']:.3f}

* Simple diode + resistor model for HSPICE compatibility
D_main anode n_main D_ESD
R_main n_main cathode {{Ron}}

.model D_ESD D(
+ IS={{I_leak*1e6}}
+ N={{k}}
+ RS=0.1
+ BV={{Vh*1.05}}
+ IBV={{I_offset*0.1}}
+ )

.ends bjt_esd_device

* Test circuit
.subckt test_circuit
Vin n1 0 DC 0
X_esd n1 0 bjt_esd_device
.dc Vin 0 20 0.1
.print dc I(Vin)
.ends test_circuit
"""
        
        # Save to file
        with open("bjt_esd_model.ckt", "w") as f:
            f.write(spice_content)
        
        print("✓ SPICE model saved to bjt_esd_model.ckt")
        return True
        
    except Exception as e:
        print(f"✗ Error generating SPICE model: {e}")
        return False

def main():
    """Main function"""
    print("BJT ESD Parameter Extractor - Simple Test")
    print("=" * 50)
    
    # Test data loading
    voltage, current = test_data_loading()
    if voltage is None:
        print("✗ Cannot proceed without data")
        return False
    
    # Extract parameters
    parameters = extract_basic_parameters(voltage, current)
    if parameters is None:
        print("✗ Cannot proceed without parameters")
        return False
    
    # Generate SPICE model
    success = generate_spice_model(parameters)
    
    print("\n" + "=" * 50)
    if success:
        print("✓ Test completed successfully!")
        print("✓ SPICE model generated and ready for HSPICE simulation")
        print("\nTo test with HSPICE:")
        print("  hspice bjt_esd_model.ckt -o test_results.lis")
    else:
        print("✗ Test failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
