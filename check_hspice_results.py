#!/usr/bin/env python3
"""
Check HSPICE Simulation Results
Analyze the HSPICE output and compare with fitted model
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import glob
import tempfile

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def find_hspice_output():
    """Find the most recent HSPICE output file"""
    print("SEARCHING FOR HSPICE OUTPUT")
    print("=" * 40)
    
    # Look in current directory
    lis_files = glob.glob("*.lis")
    
    # Look in temp directories
    temp_dirs = [tempfile.gettempdir()]
    for temp_dir in temp_dirs:
        temp_lis = glob.glob(os.path.join(temp_dir, "**", "*.lis"), recursive=True)
        lis_files.extend(temp_lis)
    
    # Look for specific pattern from the output
    pattern_dirs = glob.glob(os.path.join(tempfile.gettempdir(), "bjt_esd_sim_*"))
    for pattern_dir in pattern_dirs:
        pattern_lis = glob.glob(os.path.join(pattern_dir, "*.lis"))
        lis_files.extend(pattern_lis)
    
    if not lis_files:
        print("❌ No HSPICE .lis files found")
        print("Searched locations:")
        print(f"  • Current directory: {os.getcwd()}")
        print(f"  • Temp directory: {tempfile.gettempdir()}")
        print(f"  • Pattern directories: bjt_esd_sim_*")
        return None
    
    # Find the most recent file
    latest_file = max(lis_files, key=os.path.getmtime)
    print(f"✓ Found HSPICE output: {latest_file}")
    print(f"  File size: {os.path.getsize(latest_file)} bytes")
    print(f"  Modified: {os.path.getmtime(latest_file)}")
    
    return latest_file

def parse_hspice_output(lis_file):
    """Parse HSPICE .lis file to extract I-V data"""
    print(f"\nPARSING HSPICE OUTPUT")
    print("=" * 40)
    
    try:
        with open(lis_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return None
    
    lines = content.split('\n')
    
    # Look for simulation data
    data_section = False
    voltage_data = []
    current_data = []
    
    # Check for errors first
    errors = []
    for line in lines:
        if 'error' in line.lower() or 'failed' in line.lower():
            errors.append(line.strip())
    
    if errors:
        print(f"⚠️  Found {len(errors)} potential errors:")
        for error in errors[:3]:  # Show first 3 errors
            print(f"  • {error}")
        if len(errors) > 3:
            print(f"  • ... and {len(errors)-3} more")
    
    # Look for data table
    for i, line in enumerate(lines):
        line_clean = line.strip()
        
        # Look for DC analysis results
        if 'v(n_anode)' in line.lower() or 'i(vin)' in line.lower():
            print(f"✓ Found data header at line {i+1}")
            data_section = True
            continue
            
        if data_section and line_clean:
            # Try to parse voltage and current
            try:
                parts = line_clean.split()
                if len(parts) >= 2:
                    voltage = float(parts[0])
                    current = abs(float(parts[1]))  # Take absolute value
                    voltage_data.append(voltage)
                    current_data.append(current)
            except (ValueError, IndexError):
                # End of data section or invalid line
                if len(voltage_data) > 10:  # If we have enough data, stop
                    break
                continue
    
    if len(voltage_data) == 0:
        print("❌ No simulation data found in HSPICE output")
        print("Possible issues:")
        print("  • Simulation failed to run")
        print("  • Wrong output format")
        print("  • Syntax errors in netlist")
        return None
    
    print(f"✓ Extracted {len(voltage_data)} data points")
    print(f"  Voltage range: {min(voltage_data):.2f}V to {max(voltage_data):.2f}V")
    print(f"  Current range: {min(current_data):.2e}A to {max(current_data):.2e}A")
    
    return {
        'voltage': np.array(voltage_data),
        'current': np.array(current_data),
        'file': lis_file
    }

def load_fitted_model():
    """Load fitted model for comparison"""
    print(f"\nLOADING FITTED MODEL")
    print("=" * 40)
    
    try:
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        # Generate fitted model curve
        voltage = np.linspace(0, 20, 200)
        fitted_current = extractor.calculate_model_current(voltage, parameters)
        
        print("✓ Fitted model loaded successfully")
        print(f"  Parameters: {len(parameters)} extracted")
        print(f"  Curve points: {len(voltage)}")
        
        return {
            'voltage': voltage,
            'current': fitted_current,
            'parameters': parameters
        }
        
    except Exception as e:
        print(f"❌ Failed to load fitted model: {e}")
        return None

def compare_results(hspice_data, fitted_data):
    """Compare HSPICE results with fitted model"""
    print(f"\nCOMPARING RESULTS")
    print("=" * 40)
    
    if hspice_data is None or fitted_data is None:
        print("❌ Cannot compare - missing data")
        return False
    
    # Interpolate fitted model to HSPICE voltage points
    hspice_v = hspice_data['voltage']
    hspice_i = hspice_data['current']
    
    fitted_v = fitted_data['voltage']
    fitted_i = fitted_data['current']
    
    # Interpolate fitted current at HSPICE voltage points
    fitted_i_interp = np.interp(hspice_v, fitted_v, fitted_i)
    
    # Calculate errors
    abs_error = np.abs(hspice_i - fitted_i_interp)
    rel_error = abs_error / (fitted_i_interp + 1e-15) * 100
    log_error = np.abs(np.log10(hspice_i + 1e-15) - np.log10(fitted_i_interp + 1e-15))
    
    print(f"Comparison statistics:")
    print(f"  Data points: {len(hspice_v)}")
    print(f"  Max absolute error: {np.max(abs_error):.2e} A")
    print(f"  Max relative error: {np.max(rel_error):.1f} %")
    print(f"  Average log error: {np.mean(log_error):.4f}")
    
    # Check specific voltage points
    test_voltages = [1, 5, 10, 15, 20]
    print(f"\nPoint-by-point comparison:")
    print(f"{'V':>3} | {'HSPICE':>12} | {'Fitted':>12} | {'Error':>10} | {'Status':>8}")
    print("-" * 65)
    
    good_matches = 0
    total_tests = 0
    
    for test_v in test_voltages:
        if test_v <= np.max(hspice_v):
            idx = np.argmin(np.abs(hspice_v - test_v))
            v_actual = hspice_v[idx]
            i_hspice = hspice_i[idx]
            i_fitted = np.interp(v_actual, fitted_v, fitted_i)
            
            error = abs(i_hspice - i_fitted)
            rel_err = error / (i_fitted + 1e-15) * 100
            
            if rel_err < 5:  # Less than 5% error
                status = "✅ GOOD"
                good_matches += 1
            elif rel_err < 20:  # Less than 20% error
                status = "⚠️  OK"
            else:
                status = "❌ POOR"
            
            total_tests += 1
            
            print(f"{v_actual:3.0f} | {i_hspice:12.3e} | {i_fitted:12.3e} | {error:10.2e} | {status:>8}")
    
    # Overall assessment
    print(f"\nOVERALL ASSESSMENT:")
    match_percentage = (good_matches / total_tests * 100) if total_tests > 0 else 0
    
    if np.mean(log_error) < 0.1 and match_percentage >= 80:
        print("🎉 EXCELLENT: HSPICE results match fitted model very well!")
        print("✅ The parameter transfer problem is SOLVED")
        result = "excellent"
    elif np.mean(log_error) < 0.5 and match_percentage >= 60:
        print("✅ GOOD: HSPICE results match fitted model reasonably well")
        print("✅ Significant improvement achieved")
        result = "good"
    elif np.mean(log_error) < 1.0:
        print("⚠️  ACCEPTABLE: Some differences remain")
        print("⚠️  Further optimization may be needed")
        result = "acceptable"
    else:
        print("❌ POOR: Significant differences still exist")
        print("❌ Need to investigate further")
        result = "poor"
    
    return result

def create_comparison_plot(hspice_data, fitted_data):
    """Create comparison plot"""
    print(f"\nCREATING COMPARISON PLOT")
    print("=" * 40)
    
    if hspice_data is None or fitted_data is None:
        print("❌ Cannot create plot - missing data")
        return False
    
    try:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Linear plot
        ax1.plot(fitted_data['voltage'], fitted_data['current'], 'r-', linewidth=2, label='Fitted Model', alpha=0.8)
        ax1.plot(hspice_data['voltage'], hspice_data['current'], 'g--', linewidth=2, label='HSPICE Simulation', alpha=0.8)
        ax1.set_xlabel('Voltage (V)')
        ax1.set_ylabel('Current (A)')
        ax1.set_title('Linear Scale Comparison')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Log plot
        ax2.semilogy(fitted_data['voltage'], fitted_data['current'], 'r-', linewidth=2, label='Fitted Model', alpha=0.8)
        ax2.semilogy(hspice_data['voltage'], hspice_data['current'], 'g--', linewidth=2, label='HSPICE Simulation', alpha=0.8)
        ax2.set_xlabel('Voltage (V)')
        ax2.set_ylabel('Current (A)')
        ax2.set_title('Log Scale Comparison')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('hspice_vs_fitted_comparison.png', dpi=150, bbox_inches='tight')
        print("✓ Comparison plot saved: hspice_vs_fitted_comparison.png")
        
        return True
        
    except Exception as e:
        print(f"❌ Plot creation failed: {e}")
        return False

def main():
    """Main analysis function"""
    print("HSPICE SIMULATION RESULTS ANALYSIS")
    print("=" * 60)
    print("Checking if HSPICE simulation matches fitted model")
    
    # Step 1: Find HSPICE output
    lis_file = find_hspice_output()
    if lis_file is None:
        print("\n❌ ANALYSIS FAILED: No HSPICE output found")
        print("Please ensure HSPICE simulation completed successfully")
        return False
    
    # Step 2: Parse HSPICE results
    hspice_data = parse_hspice_output(lis_file)
    
    # Step 3: Load fitted model
    fitted_data = load_fitted_model()
    
    # Step 4: Compare results
    result = compare_results(hspice_data, fitted_data)
    
    # Step 5: Create comparison plot
    plot_ok = create_comparison_plot(hspice_data, fitted_data)
    
    # Final summary
    print("\n" + "=" * 60)
    print("FINAL ANALYSIS SUMMARY")
    print("=" * 60)
    
    if result == "excellent":
        print("🎉 SUCCESS: Parameter transfer problem SOLVED!")
        print("✅ HSPICE simulation matches fitted model excellently")
        print("✅ The green line should now overlap with the red line")
    elif result == "good":
        print("✅ GOOD PROGRESS: Significant improvement achieved")
        print("✅ HSPICE simulation matches fitted model well")
        print("✅ Much better than before")
    elif result == "acceptable":
        print("⚠️  PARTIAL SUCCESS: Some improvement achieved")
        print("⚠️  Still some differences, but much better than before")
    else:
        print("❌ ISSUE REMAINS: Significant differences still exist")
        print("❌ May need to try different model type or debug further")
    
    if plot_ok:
        print(f"\n📊 Visual comparison available: hspice_vs_fitted_comparison.png")
    
    print(f"\n📋 RECOMMENDATIONS:")
    if result in ["excellent", "good"]:
        print("• Continue using PWL model for HSPICE simulations")
        print("• The parameter transfer issue is resolved")
        print("• Use this approach for future ESD modeling")
    else:
        print("• Try different HSPICE model types (table, simple)")
        print("• Check HSPICE version compatibility")
        print("• Verify netlist syntax")
    
    return result in ["excellent", "good", "acceptable"]

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
