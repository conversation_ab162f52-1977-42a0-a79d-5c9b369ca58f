#!/usr/bin/env python3
"""
Test Parameter Transfer from Fitted Model to SPICE Model
Verifies that SPICE simulation results match fitted model exactly
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_parameter_transfer():
    """Test parameter transfer accuracy"""
    print("PARAMETER TRANSFER ACCURACY TEST")
    print("=" * 50)
    
    # Load real data and extract parameters
    try:
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.fixed_spice_generator import FixedSpiceGenerator
        
        # Load data
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        print(f"✓ Loaded data: {len(data['voltage'])} points")
        
        # Extract parameters
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        print("✓ Extracted parameters:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"    {key}: {value:.3e}")
            else:
                print(f"    {key}: {value:.3f}")
        
        # Test different SPICE model types
        generator = FixedSpiceGenerator()
        
        voltage_test = np.linspace(0, 20, 100)
        fitted_current = extractor.calculate_model_current(voltage_test, parameters)
        
        print(f"\nTesting parameter transfer accuracy:")
        print("-" * 40)
        
        # Test behavioral model
        generator.set_model_type("behavioral")
        behavioral_error = generator.compare_models(parameters, [0, 20, 0.2])
        
        # Test hybrid model  
        generator.set_model_type("hybrid")
        hybrid_error = generator.compare_models(parameters, [0, 20, 0.2])
        
        # Test diode model
        generator.set_model_type("diode")
        diode_error = generator.compare_models(parameters, [0, 20, 0.2])
        
        print(f"\nSUMMARY:")
        print(f"Behavioral model error: {behavioral_error:.2e}")
        print(f"Hybrid model error:     {hybrid_error:.2e}")
        print(f"Diode model error:      {diode_error:.2e}")
        
        # Generate test files
        print(f"\nGenerating test SPICE files:")
        
        # Behavioral model (exact)
        generator.set_model_type("behavioral")
        generator.generate_spice_model(parameters, "test_behavioral.ckt")
        
        # Hybrid model (exact + physical)
        generator.set_model_type("hybrid") 
        generator.generate_spice_model(parameters, "test_hybrid.ckt")
        
        # Diode model (approximate)
        generator.set_model_type("diode")
        generator.generate_spice_model(parameters, "test_diode.ckt")
        
        print("✓ Generated test_behavioral.ckt (EXACT)")
        print("✓ Generated test_hybrid.ckt (EXACT)")
        print("✓ Generated test_diode.ckt (APPROXIMATE)")
        
        # Assessment
        print(f"\n" + "="*50)
        print("ASSESSMENT")
        print("="*50)
        
        if behavioral_error < 1e-10:
            print("✅ BEHAVIORAL MODEL: Perfect parameter transfer!")
            print("   Use this for exact matching with fitted model")
        else:
            print("⚠️  BEHAVIORAL MODEL: Some error detected")
            
        if hybrid_error < 1e-10:
            print("✅ HYBRID MODEL: Perfect parameter transfer!")
            print("   Use this for exact matching + physical realism")
        else:
            print("⚠️  HYBRID MODEL: Some error detected")
            
        if diode_error < 0.1:
            print("✅ DIODE MODEL: Good approximation")
            print("   Use this for maximum SPICE compatibility")
        else:
            print("⚠️  DIODE MODEL: Poor approximation")
            print("   Consider using behavioral model instead")
            
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def demonstrate_exact_matching():
    """Demonstrate exact matching between fitted and SPICE models"""
    print("\nEXACT MATCHING DEMONSTRATION")
    print("=" * 50)
    
    try:
        from models.fixed_spice_generator import FixedSpiceGenerator
        
        # Example parameters
        params = {
            'I_leak': 1e-9,
            'Vt1': 12.0,
            'k': 3.0,
            'Ron': 2.0,
            'Vh': 14.0,
            'I_offset': 0.05,
            'Isb': 0.04,
            'Vsb': 14.0
        }
        
        generator = FixedSpiceGenerator()
        voltage = np.linspace(0, 20, 50)
        
        # Calculate fitted model current
        fitted_current = generator.calculate_fitted_current(voltage, params)
        
        # Calculate behavioral SPICE current
        behavioral_current = generator.calculate_behavioral_current(voltage, params)
        
        # Calculate differences
        abs_diff = np.abs(fitted_current - behavioral_current)
        rel_diff = abs_diff / (fitted_current + 1e-15) * 100
        
        print(f"Voltage range: 0V to 20V ({len(voltage)} points)")
        print(f"Maximum absolute difference: {np.max(abs_diff):.2e} A")
        print(f"Maximum relative difference: {np.max(rel_diff):.2e} %")
        print(f"Average relative difference: {np.mean(rel_diff):.2e} %")
        
        if np.max(abs_diff) < 1e-15:
            print("✅ PERFECT MATCH: Fitted and SPICE models are identical!")
        elif np.max(rel_diff) < 1e-10:
            print("✅ EXCELLENT MATCH: Differences are negligible")
        else:
            print("⚠️  Some differences detected")
            
        # Show specific examples
        print(f"\nSpecific examples:")
        test_voltages = [5, 10, 12, 15, 18]
        for v in test_voltages:
            idx = np.argmin(np.abs(voltage - v))
            fitted_i = fitted_current[idx]
            behavioral_i = behavioral_current[idx]
            diff = abs(fitted_i - behavioral_i)
            print(f"  V={v:2d}V: Fitted={fitted_i:.3e}A, SPICE={behavioral_i:.3e}A, Diff={diff:.2e}A")
            
        return True
        
    except Exception as e:
        print(f"✗ Demonstration failed: {e}")
        return False

def create_comparison_plot():
    """Create comparison plot of fitted vs SPICE models"""
    print("\nCREATING COMPARISON PLOT")
    print("=" * 50)
    
    try:
        from models.fixed_spice_generator import FixedSpiceGenerator
        
        # Example parameters
        params = {
            'I_leak': 1e-9,
            'Vt1': 12.0,
            'k': 3.0,
            'Ron': 2.0,
            'Vh': 14.0,
            'I_offset': 0.05,
            'Isb': 0.04,
            'Vsb': 14.0
        }
        
        generator = FixedSpiceGenerator()
        voltage = np.linspace(0, 20, 200)
        
        # Calculate currents
        fitted_current = generator.calculate_fitted_current(voltage, params)
        behavioral_current = generator.calculate_behavioral_current(voltage, params)
        
        # Create plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Linear plot
        ax1.plot(voltage, fitted_current, 'b-', linewidth=2, label='Fitted Model')
        ax1.plot(voltage, behavioral_current, 'r--', linewidth=2, label='SPICE Behavioral')
        ax1.set_xlabel('Voltage (V)')
        ax1.set_ylabel('Current (A)')
        ax1.set_title('Linear Scale Comparison')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Log plot
        ax2.semilogy(voltage, fitted_current, 'b-', linewidth=2, label='Fitted Model')
        ax2.semilogy(voltage, behavioral_current, 'r--', linewidth=2, label='SPICE Behavioral')
        ax2.set_xlabel('Voltage (V)')
        ax2.set_ylabel('Current (A)')
        ax2.set_title('Log Scale Comparison')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('parameter_transfer_verification.png', dpi=150, bbox_inches='tight')
        print("✓ Comparison plot saved: parameter_transfer_verification.png")
        
        return True
        
    except Exception as e:
        print(f"✗ Plot creation failed: {e}")
        return False

def main():
    """Main test function"""
    print("PARAMETER TRANSFER VERIFICATION")
    print("=" * 60)
    print("Testing exact parameter transfer from fitted model to SPICE")
    
    # Run tests
    test1_success = test_parameter_transfer()
    test2_success = demonstrate_exact_matching()
    test3_success = create_comparison_plot()
    
    print("\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    
    if test1_success:
        print("✅ Parameter transfer test: PASSED")
    else:
        print("❌ Parameter transfer test: FAILED")
        
    if test2_success:
        print("✅ Exact matching demonstration: PASSED")
    else:
        print("❌ Exact matching demonstration: FAILED")
        
    if test3_success:
        print("✅ Comparison plot generation: PASSED")
    else:
        print("❌ Comparison plot generation: FAILED")
        
    if test1_success and test2_success:
        print("\n🎉 CONCLUSION: Parameter transfer is working correctly!")
        print("\n📋 RECOMMENDATIONS:")
        print("1. Use 'Save Fixed SPICE Model' in the GUI")
        print("2. Choose 'behavioral' model type for exact matching")
        print("3. Use the generated .ckt file for HSPICE simulation")
        print("4. Results should match fitted model exactly!")
        
        print("\n🔧 USAGE IN GUI:")
        print("• File → Save Fixed SPICE Model...")
        print("• Select 'behavioral' model type")
        print("• Save as .ckt file")
        print("• Run HSPICE simulation")
        print("• Compare with fitted model - should be identical!")
    else:
        print("\n⚠️  ISSUES DETECTED:")
        print("Some tests failed. Check error messages above.")
        print("Consider using the Exact Parameter Conversion methods instead.")
    
    return test1_success and test2_success

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
