* Simple BJT ESD Behavioral Model Test
* Simplified version for HSPICE compatibility

.subckt bjt_esd_simple anode cathode
* Parameters
.param I_leak=1.744541e-07
.param Vt1=3.625
.param k=0.644
.param Ron=1.441
.param Vh=12.720

* Simple behavioral current source
G_esd anode cathode cur='if(V(anode,cathode) < Vt1, I_leak*exp(V(anode,cathode)/1.0), I_leak*exp(k*(V(anode,cathode)-Vt1)/Vt1))'

* Parallel resistance for high voltage
R_parallel anode cathode {Ron}

.ends bjt_esd_simple

* Test circuit
Vin n1 0 DC 0
X_esd n1 0 bjt_esd_simple

* Analysis
.dc Vin 0 20 0.5
.print dc V(n1) I(Vin)

* Options
.option post=2
.option accurate

.end
