#!/usr/bin/env python3
"""
Analyze Parameter Mismatch Between Fitted Model and SPICE Model
Identifies why simulation results don't match fitted model
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_fitted_model_equation():
    """Analyze the fitted model equation"""
    print("FITTED MODEL EQUATION ANALYSIS")
    print("=" * 50)
    
    print("\nFitted Model (from parameter_extractor.py):")
    print("```python")
    print("for i, v in enumerate(voltage):")
    print("    if v < vt1:")
    print("        # Leakage region")
    print("        current[i] = i_leak * np.exp(v / 1.0)  # Thermal voltage = 1V")
    print("    elif v < vh:")
    print("        # Trigger region") 
    print("        current[i] = i_leak * np.exp(k * (v - vt1) / vt1)")
    print("    else:")
    print("        # Snapback region")
    print("        linear_term = (v - vsb) / ron")
    print("        exp_term = isb * np.exp(-(v - vsb))")
    print("        current[i] = i_offset + linear_term + exp_term")
    print("```")

def analyze_spice_model_equation():
    """Analyze the SPICE model equation"""
    print("\nSPICE MODEL EQUATION ANALYSIS")
    print("=" * 50)
    
    print("\nSPICE Model (from spice_generator.py):")
    print("```spice")
    print(".param bjt_esd_current(v) = '")
    print("+ if(v < 0, I_leak*1e-3,")
    print("+ if(v < Vt1, I_leak*exp(v/1.0),")
    print("+ if(v < Vh, I_leak*exp(k*(v-Vt1)/Vt1),")
    print("+ I_offset + (v-Vsb)/Ron + Isb*exp(-(v-Vsb))")
    print("+ )))'")
    print("```")
    
    print("\nAlternative SPICE Implementation:")
    print("```spice")
    print("D_main anode n_main D_ESD")
    print("R_main n_main cathode {Ron}")
    print(".model D_ESD D(")
    print("+ IS={I_leak*1e6}      # ❌ PROBLEM: Scaling factor!")
    print("+ N={k}                # ❌ PROBLEM: Different meaning!")
    print("+ BV={vh*1.05}         # ❌ PROBLEM: Different voltage!")
    print("+ IBV={i_offset*0.1}   # ❌ PROBLEM: Different current!")
    print("+ )")
    print("```")

def identify_key_problems():
    """Identify key problems causing mismatch"""
    print("\nKEY PROBLEMS IDENTIFIED")
    print("=" * 50)
    
    problems = [
        {
            "problem": "Inconsistent Mathematical Models",
            "description": "Fitted model uses one equation, SPICE uses different equations",
            "impact": "Complete mismatch in I-V characteristics"
        },
        {
            "problem": "Parameter Scaling Issues", 
            "description": "SPICE diode IS parameter scaled by 1e6, but no justification",
            "impact": "Current levels completely wrong"
        },
        {
            "problem": "Different Physical Meanings",
            "description": "Parameter 'k' means different things in fitted vs SPICE model",
            "impact": "Exponential behavior doesn't match"
        },
        {
            "problem": "Voltage Reference Mismatch",
            "description": "Trigger voltages and breakdown voltages use different references",
            "impact": "Trigger points occur at wrong voltages"
        },
        {
            "problem": "Missing Continuity",
            "description": "No continuity enforcement between regions",
            "impact": "Current jumps at region boundaries"
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"\n{i}. {problem['problem']}")
        print(f"   Description: {problem['description']}")
        print(f"   Impact: {problem['impact']}")

def demonstrate_mismatch():
    """Demonstrate the mismatch with example parameters"""
    print("\nDEMONSTRATING THE MISMATCH")
    print("=" * 50)
    
    # Example parameters
    params = {
        'I_leak': 1e-9,
        'Vt1': 12.0,
        'k': 3.0,
        'Ron': 2.0,
        'Vh': 14.0,
        'I_offset': 0.05,
        'Isb': 0.04,
        'Vsb': 14.0
    }
    
    voltage = np.linspace(0, 20, 100)
    
    # Calculate fitted model current
    fitted_current = calculate_fitted_current(voltage, params)
    
    # Calculate what SPICE diode model would produce
    spice_diode_current = calculate_spice_diode_current(voltage, params)
    
    # Calculate what behavioral SPICE would produce
    spice_behavioral_current = calculate_spice_behavioral_current(voltage, params)
    
    print(f"\nAt V = 10V:")
    print(f"  Fitted Model:      {fitted_current[50]:.6e} A")
    print(f"  SPICE Diode:       {spice_diode_current[50]:.6e} A")
    print(f"  SPICE Behavioral:  {spice_behavioral_current[50]:.6e} A")
    
    print(f"\nAt V = 15V:")
    print(f"  Fitted Model:      {fitted_current[75]:.6e} A")
    print(f"  SPICE Diode:       {spice_diode_current[75]:.6e} A")
    print(f"  SPICE Behavioral:  {spice_behavioral_current[75]:.6e} A")
    
    # Calculate errors
    fitted_vs_diode_error = np.mean(np.abs(np.log10(fitted_current + 1e-15) - 
                                          np.log10(spice_diode_current + 1e-15)))
    fitted_vs_behavioral_error = np.mean(np.abs(np.log10(fitted_current + 1e-15) - 
                                               np.log10(spice_behavioral_current + 1e-15)))
    
    print(f"\nLog Errors:")
    print(f"  Fitted vs SPICE Diode:      {fitted_vs_diode_error:.4f}")
    print(f"  Fitted vs SPICE Behavioral: {fitted_vs_behavioral_error:.4f}")

def calculate_fitted_current(voltage, params):
    """Calculate current using fitted model equation"""
    i_leak = params['I_leak']
    vt1 = params['Vt1']
    k = params['k']
    ron = params['Ron']
    vh = params['Vh']
    i_offset = params['I_offset']
    isb = params['Isb']
    vsb = params['Vsb']
    
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < vt1:
            # Leakage region
            current[i] = i_leak * np.exp(v / 1.0)
        elif v < vh:
            # Trigger region
            current[i] = i_leak * np.exp(k * (v - vt1) / vt1)
        else:
            # Snapback region
            linear_term = (v - vsb) / ron if ron > 0 else 0
            exp_term = isb * np.exp(-(v - vsb)) if v > vsb else isb
            current[i] = i_offset + linear_term + exp_term
            
    return current

def calculate_spice_diode_current(voltage, params):
    """Calculate current using SPICE diode model approximation"""
    i_leak = params['I_leak']
    k = params['k']
    vh = params['Vh']
    i_offset = params['I_offset']
    ron = params['Ron']
    
    # SPICE diode parameters (as generated by current code)
    IS = i_leak * 1e6  # ❌ This scaling is wrong!
    N = k
    BV = vh * 1.05
    IBV = i_offset * 0.1
    
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < BV:
            # Forward diode equation: I = IS * (exp(V/(N*Vt)) - 1)
            Vt = 0.026  # Thermal voltage at room temperature
            current[i] = IS * (np.exp(v / (N * Vt)) - 1)
        else:
            # Breakdown region
            current[i] = IBV + (v - BV) / ron
            
    return current

def calculate_spice_behavioral_current(voltage, params):
    """Calculate current using SPICE behavioral model"""
    i_leak = params['I_leak']
    vt1 = params['Vt1']
    k = params['k']
    ron = params['Ron']
    vh = params['Vh']
    i_offset = params['I_offset']
    isb = params['Isb']
    vsb = params['Vsb']
    
    current = np.zeros_like(voltage)
    
    for i, v in enumerate(voltage):
        if v < 0:
            current[i] = i_leak * 1e-3
        elif v < vt1:
            current[i] = i_leak * np.exp(v / 1.0)
        elif v < vh:
            current[i] = i_leak * np.exp(k * (v - vt1) / vt1)
        else:
            current[i] = i_offset + (v - vsb) / ron + isb * np.exp(-(v - vsb))
            
    return current

def propose_solutions():
    """Propose solutions to fix the mismatch"""
    print("\nPROPOSED SOLUTIONS")
    print("=" * 50)
    
    solutions = [
        {
            "solution": "Use Behavioral SPICE Model Only",
            "description": "Remove diode model, use only behavioral equations",
            "pros": ["Exact mathematical match", "No parameter conversion needed"],
            "cons": ["May have convergence issues", "Not all SPICE simulators support"]
        },
        {
            "solution": "Correct Parameter Conversion",
            "description": "Fix the parameter mapping from fitted to SPICE diode",
            "pros": ["Better SPICE compatibility", "Physical meaning preserved"],
            "cons": ["Complex conversion", "May not be exact match"]
        },
        {
            "solution": "Hybrid Approach",
            "description": "Use behavioral for accuracy, diode for backup",
            "pros": ["Best of both worlds", "Robust simulation"],
            "cons": ["More complex model", "Larger file size"]
        },
        {
            "solution": "Exact Conversion Methods",
            "description": "Use the exact conversion methods we developed",
            "pros": ["100% accuracy guaranteed", "Multiple method options"],
            "cons": ["More complex implementation", "Requires understanding"]
        }
    ]
    
    for i, sol in enumerate(solutions, 1):
        print(f"\n{i}. {sol['solution']}")
        print(f"   Description: {sol['description']}")
        print(f"   Pros: {', '.join(sol['pros'])}")
        print(f"   Cons: {', '.join(sol['cons'])}")

def main():
    """Main analysis function"""
    print("PARAMETER MISMATCH ANALYSIS")
    print("=" * 60)
    print("Analyzing why SPICE simulation results don't match fitted model")
    
    analyze_fitted_model_equation()
    analyze_spice_model_equation()
    identify_key_problems()
    demonstrate_mismatch()
    propose_solutions()
    
    print("\n" + "=" * 60)
    print("CONCLUSION")
    print("=" * 60)
    
    print("\n🔍 ROOT CAUSE IDENTIFIED:")
    print("The mismatch occurs because:")
    print("1. Fitted model uses one mathematical equation")
    print("2. SPICE model uses completely different equations")
    print("3. Parameter scaling and meanings are inconsistent")
    print("4. No proper parameter conversion is performed")
    
    print("\n✅ RECOMMENDED SOLUTION:")
    print("Use the Exact Parameter Conversion methods we developed:")
    print("• Method 1 (Behavioral): 100% mathematical equivalence")
    print("• Method 2 (PWL): 100% I-V curve equivalence") 
    print("• Method 3 (Multi-diode): Physical circuit equivalence")
    
    print("\n📋 IMMEDIATE ACTIONS:")
    print("1. Use Tools → Exact Parameter Conversion")
    print("2. Select Method 1 (Behavioral) for exact matching")
    print("3. Use the generated .ckt file for HSPICE simulation")
    print("4. Compare results - they should match perfectly!")
    
    print("\n🎯 LONG-TERM SOLUTION:")
    print("Update the standard SPICE generator to use behavioral")
    print("equations that exactly match the fitted model.")
    
    return True

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
