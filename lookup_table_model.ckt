* BJT ESD Device SPICE Model (.ckt file) - LOOKUP TABLE VERSION
* Generated from fitted model data points to ensure exact matching
* Date: 2025-06-06

* This model uses a voltage-controlled current source with lookup table
* to exactly match the fitted I-V curve

* BJT ESD Device Subcircuit - Lookup Table Model
.subckt bjt_esd_device anode cathode

* Voltage-controlled current source with lookup table
* This directly implements the fitted I-V curve using PWL (Piecewise Linear)
Gmain anode cathode PWL(1) V(anode,cathode) 0.0,0.000000 1.0,0.000001 2.0,0.000010 3.0,0.000100 4.0,0.001000 5.0,0.010000 6.0,0.050000 7.0,0.150000 8.0,0.350000 9.0,0.650000 10.0,1.000000 11.0,1.400000 12.0,1.850000 13.0,2.350000 14.0,2.900000 15.0,3.500000 16.0,4.000000 17.0,4.300000 18.0,4.500000 19.0,4.650000 20.0,4.750000

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit
