#!/usr/bin/env python3
"""
Test Fixed PWL Model
Generate and test the corrected PWL model with proper VCCS syntax
"""

import sys
import os
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fixed_pwl_model():
    """Test the fixed PWL model generation"""
    print("TESTING FIXED PWL MODEL")
    print("=" * 40)
    
    try:
        # Load parameters
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.hspice_compatible_generator import HspiceCompatibleGenerator
        
        # Load data and extract parameters
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        print("✓ Parameters loaded:")
        for key, value in parameters.items():
            if 'I_' in key or 'Isb' in key:
                print(f"    {key}: {value:.6e}")
            else:
                print(f"    {key}: {value:.6f}")
        
        # Generate fixed PWL model
        generator = HspiceCompatibleGenerator()
        generator.set_model_type("pwl")
        
        filename = "bjt_esd_fixed_pwl.ckt"
        content = generator.generate_hspice_model(parameters, filename)
        
        print(f"\n✓ Fixed PWL model generated: {filename}")
        print(f"✓ Content length: {len(content)} characters")
        
        # Check the PWL syntax
        print(f"\nChecking PWL syntax:")
        lines = content.split('\n')
        
        has_vccs = False
        has_pwl_syntax = False
        syntax_issues = []
        
        for i, line in enumerate(lines):
            line_clean = line.strip()
            
            if line_clean.startswith('G_esd') and 'PWL(' in line_clean:
                has_vccs = True
                has_pwl_syntax = True
                print(f"  ✓ Found VCCS with PWL at line {i+1}")
                print(f"    {line_clean}")
                
            if 'I_esd' in line_clean and 'PWL(' in line_clean:
                syntax_issues.append(f"Line {i+1}: Old current source syntax found")
                
        print(f"\nSyntax verification:")
        print(f"  VCCS (G_esd): {'✓' if has_vccs else '❌'}")
        print(f"  PWL syntax: {'✓' if has_pwl_syntax else '❌'}")
        
        if syntax_issues:
            print(f"  ⚠️  Issues found:")
            for issue in syntax_issues:
                print(f"    • {issue}")
        else:
            print(f"  ✓ No syntax issues detected")
        
        # Show key parts of the model
        print(f"\nKey model sections:")
        in_subckt = False
        for i, line in enumerate(lines):
            if '.subckt bjt_esd_pwl' in line.lower():
                in_subckt = True
                print(f"  {i+1:2d}: {line}")
            elif in_subckt and line.strip():
                print(f"  {i+1:2d}: {line}")
                if '.ends' in line.lower():
                    break
        
        return True, filename
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False, None

def run_hspice_test(filename):
    """Run HSPICE test with the fixed model"""
    print(f"\nRUNNING HSPICE TEST")
    print("=" * 40)
    
    try:
        from simulation.hspice_runner import HspiceRunner
        
        # Check HSPICE installation
        runner = HspiceRunner()
        if not runner.check_hspice_installation():
            print("❌ HSPICE not available - skipping simulation test")
            return False
        
        print("✓ HSPICE installation detected")
        
        # Run simulation
        import subprocess
        import os
        
        output_file = filename.replace('.ckt', '_results.lis')
        cmd = [runner.hspice_path, filename, "-o", output_file]
        
        print(f"Running command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              cwd=os.getcwd(), timeout=30)
        
        print(f"Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ HSPICE simulation completed successfully!")
            
            # Check if output file exists
            if os.path.exists(output_file):
                print(f"✓ Output file created: {output_file}")
                
                # Try to parse results
                sim_results = runner.parse_hspice_output(output_file)
                if sim_results:
                    voltage = sim_results['voltage']
                    current = sim_results['current']
                    print(f"✓ Parsed {len(voltage)} data points")
                    print(f"  Voltage range: {voltage.min():.2f}V to {voltage.max():.2f}V")
                    print(f"  Current range: {current.min():.2e}A to {current.max():.2e}A")
                    return True
                else:
                    print("⚠️  Could not parse simulation results")
                    return False
            else:
                print(f"❌ Output file not created: {output_file}")
                return False
        else:
            print("❌ HSPICE simulation failed")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            
            # Save error log
            error_file = "hspice_test_error.log"
            with open(error_file, 'w') as f:
                f.write("HSPICE Test Error Log\n")
                f.write("=" * 30 + "\n")
                f.write(f"Command: {' '.join(cmd)}\n")
                f.write(f"Return code: {result.returncode}\n\n")
                f.write("STDOUT:\n")
                f.write(result.stdout)
                f.write("\n\nSTDERR:\n")
                f.write(result.stderr)
            print(f"Error log saved: {error_file}")
            
            return False
            
    except Exception as e:
        print(f"❌ HSPICE test error: {e}")
        return False

def compare_with_fitted_model(filename):
    """Compare HSPICE results with fitted model"""
    print(f"\nCOMPARING WITH FITTED MODEL")
    print("=" * 40)
    
    try:
        # Load fitted model
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        parameters = extractor.extract_parameters(data)
        
        # Calculate fitted model curve
        voltage = np.linspace(0, 20, 200)
        fitted_current = extractor.calculate_model_current(voltage, parameters)
        
        print("✓ Fitted model calculated")
        
        # Try to load HSPICE results
        output_file = filename.replace('.ckt', '_results.lis')
        if os.path.exists(output_file):
            from simulation.hspice_runner import HspiceRunner
            runner = HspiceRunner()
            
            sim_results = runner.parse_hspice_output(output_file)
            if sim_results:
                hspice_v = sim_results['voltage']
                hspice_i = sim_results['current']
                
                # Interpolate fitted model to HSPICE points
                fitted_i_interp = np.interp(hspice_v, voltage, fitted_current)
                
                # Calculate errors
                abs_error = np.abs(hspice_i - fitted_i_interp)
                rel_error = abs_error / (fitted_i_interp + 1e-15) * 100
                
                print(f"✓ Comparison completed")
                print(f"  Data points: {len(hspice_v)}")
                print(f"  Max absolute error: {np.max(abs_error):.2e} A")
                print(f"  Max relative error: {np.max(rel_error):.1f} %")
                print(f"  Average relative error: {np.mean(rel_error):.1f} %")
                
                # Check specific points
                test_voltages = [1, 5, 10, 15, 20]
                print(f"\nPoint comparison:")
                print(f"{'V':>3} | {'HSPICE':>12} | {'Fitted':>12} | {'Error %':>8}")
                print("-" * 45)
                
                for test_v in test_voltages:
                    if test_v <= np.max(hspice_v):
                        idx = np.argmin(np.abs(hspice_v - test_v))
                        v_actual = hspice_v[idx]
                        i_hspice = hspice_i[idx]
                        i_fitted = np.interp(v_actual, voltage, fitted_current)
                        
                        error_pct = abs(i_hspice - i_fitted) / (i_fitted + 1e-15) * 100
                        
                        print(f"{v_actual:3.0f} | {i_hspice:12.3e} | {i_fitted:12.3e} | {error_pct:8.1f}")
                
                # Overall assessment
                if np.mean(rel_error) < 5:
                    print("\n🎉 EXCELLENT: PWL model matches fitted model very well!")
                    return True
                elif np.mean(rel_error) < 20:
                    print("\n✅ GOOD: PWL model matches fitted model reasonably well")
                    return True
                else:
                    print("\n⚠️  ACCEPTABLE: Some differences remain")
                    return False
            else:
                print("❌ Could not parse HSPICE results")
                return False
        else:
            print(f"❌ HSPICE output file not found: {output_file}")
            return False
            
    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        return False

def main():
    """Main test function"""
    print("FIXED PWL MODEL TEST")
    print("=" * 50)
    print("Testing corrected PWL model with VCCS syntax")
    
    # Test 1: Generate fixed PWL model
    success, filename = test_fixed_pwl_model()
    
    if not success:
        print("\n❌ PWL model generation failed")
        return False
    
    # Test 2: Run HSPICE simulation
    hspice_ok = run_hspice_test(filename)
    
    # Test 3: Compare with fitted model
    comparison_ok = compare_with_fitted_model(filename)
    
    # Final summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    if success:
        print("✅ PWL model generation: SUCCESS")
        print(f"✅ Generated file: {filename}")
        print("✅ Fixed VCCS syntax")
    
    if hspice_ok:
        print("✅ HSPICE simulation: SUCCESS")
        print("✅ No syntax errors")
    else:
        print("❌ HSPICE simulation: FAILED")
        print("❌ Check error logs for details")
    
    if comparison_ok:
        print("🎉 Model comparison: EXCELLENT")
        print("🎉 PWL model matches fitted model!")
    elif hspice_ok:
        print("⚠️  Model comparison: NEEDS REVIEW")
        print("⚠️  Check comparison details above")
    
    overall_success = success and hspice_ok and comparison_ok
    
    print(f"\n📋 NEXT STEPS:")
    if overall_success:
        print("• Use this PWL model for HSPICE simulations")
        print("• The parameter transfer issue should be resolved")
        print("• Green line should now match red line in GUI")
    else:
        print("• Review error logs for debugging")
        print("• Try alternative model types if needed")
        print("• Check HSPICE version compatibility")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
