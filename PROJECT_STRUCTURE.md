# BJT ESD Parameter Extractor - Project Structure

## 项目概述

这是一个完整的BJT ESD器件参数提取工具，具有以下特点：

- **PyQt5 GUI界面**：上方绘图区域，下方参数控制面板
- **参数提取算法**：自动从I-V数据提取BJT ESD参数
- **实时参数调整**：滑块和直接输入支持
- **SPICE模型生成**：生成HSPICE兼容的.ckt文件
- **HSPICE仿真集成**：直接运行仿真并比较结果

## 文件结构

```
bjt_esd_extractor/
├── main.py                     # 主程序入口
├── simple_test.py              # 简单功能测试
├── run_app.bat                 # Windows启动脚本
├── requirements.txt            # Python依赖
├── README.md                   # 详细说明文档
├── PROJECT_STRUCTURE.md        # 项目结构说明
├── 1.csv                       # 测试数据文件
│
├── gui/                        # GUI模块
│   ├── __init__.py
│   ├── main_window.py          # 主窗口
│   ├── plot_widget.py          # 绘图组件
│   └── parameter_widget.py     # 参数控制面板
│
├── data/                       # 数据处理模块
│   ├── __init__.py
│   ├── data_loader.py          # 数据加载器
│   └── parameter_extractor.py  # 参数提取算法
│
├── models/                     # 模型模块
│   ├── __init__.py
│   ├── bjt_esd_model.py        # BJT ESD模型类
│   └── spice_generator.py      # SPICE模型生成器
│
└── simulation/                 # 仿真模块
    ├── __init__.py
    └── hspice_runner.py        # HSPICE仿真接口
```

## 核心功能模块

### 1. GUI模块 (gui/)

- **main_window.py**: 主应用窗口，集成所有功能
- **plot_widget.py**: matplotlib绘图组件，支持导航工具栏
- **parameter_widget.py**: 参数控制面板，滑块+直接输入

### 2. 数据处理模块 (data/)

- **data_loader.py**: CSV数据加载和HSPICE输出解析
- **parameter_extractor.py**: BJT ESD参数提取算法

### 3. 模型模块 (models/)

- **bjt_esd_model.py**: BJT ESD器件模型类
- **spice_generator.py**: HSPICE兼容的SPICE模型生成

### 4. 仿真模块 (simulation/)

- **hspice_runner.py**: HSPICE仿真执行和结果解析

## BJT ESD模型

### 三区域模型

1. **漏电区域** (V < Vt1): 指数漏电流
2. **触发区域** (Vt1 ≤ V < Vh): 快速电流增长
3. **回滞区域** (V ≥ Vh): 线性导通+回滞行为

### 模型方程

```
I(V) = {
  I_leak * exp(V/1.0)                           if V < Vt1
  I_leak * exp(k*(V-Vt1)/Vt1)                  if Vt1 ≤ V < Vh  
  I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))   if V ≥ Vh
}
```

### 参数说明

- **I_leak**: 漏电流 (A)
- **Vt1**: 触发电压 (V)
- **k**: 指数因子
- **Ron**: 导通电阻 (Ω)
- **Vh**: 保持电压 (V)
- **I_offset**: 电流偏移 (A)
- **Isb**: 回滞电流 (A)
- **Vsb**: 回滞电压 (V)

## 使用方法

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
python main.py
```

或使用批处理文件：
```bash
run_app.bat
```

### 3. 基本操作流程

1. **加载数据**: 点击"Load Data"加载CSV文件
2. **参数提取**: 自动提取或手动调整参数
3. **实时调整**: 使用滑块或直接输入调整参数
4. **保存模型**: 点击"Save Model"保存.ckt文件
5. **HSPICE仿真**: 点击"HSPICE Simulation"运行验证

### 4. 数据格式

CSV文件格式：
```csv
Voltage,Current
0.0,0.0
0.1,1.23e-09
0.2,2.45e-09
...
```

## HSPICE集成

### 要求

- HSPICE已安装并在系统PATH中
- 环境变量正确配置

### 生成的SPICE模型

- 完整的子电路定义
- HSPICE兼容语法
- 包含测试电路示例
- 支持参数化建模

### 仿真命令

```bash
hspice bjt_esd_model.ckt -o simulation_results.lis
```

## 故障排除

### 常见问题

1. **PyQt5导入错误**: 确保已安装PyQt5
2. **HSPICE未找到**: 检查HSPICE安装和PATH设置
3. **CSV加载失败**: 检查文件格式和列结构
4. **参数提取失败**: 验证数据质量和电压范围

### 调试模式

设置环境变量启用调试输出：
```bash
set DEBUG=1
python main.py
```

## 扩展功能

### 可添加的功能

1. **温度建模**: 温度相关参数
2. **工艺变化**: 蒙特卡洛分析
3. **频域分析**: AC特性建模
4. **批量处理**: 多文件参数提取
5. **数据库集成**: 参数数据库管理

### 自定义模型

可以通过修改`models/bjt_esd_model.py`来实现自定义的ESD器件模型。

## 技术细节

### 参数提取算法

1. **区域识别**: 基于电流导数识别不同区域
2. **非线性拟合**: 使用scipy.optimize进行参数优化
3. **鲁棒性**: 处理噪声和异常数据点

### SPICE模型生成

1. **行为建模**: 使用HSPICE行为语句
2. **等效电路**: 二极管+电阻组合
3. **收敛性**: 优化的模型参数确保仿真收敛

### GUI设计

1. **响应式布局**: 自适应窗口大小
2. **实时更新**: 参数变化立即反映在图形中
3. **用户友好**: 直观的操作界面
