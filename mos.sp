* Body Leakage Check at High VDS - BSIM4 Model
*------------------------------------------------
* Simulation Setup
.option post=2 accurate probe
.option converge=1 ingold=1
.temp 25

* 包含您的BSIM4模型库 - 替换为实际路径
.lib 'D:\code\spice\bc018_v1p14.lib' tt

* 定义电源
vds d 0 dc 0     * 漏源电压（扫描变量）
vg  g 0 dc 0     * 栅极电压（固定值）
vs  s 0 dc 0     * 源极接地
vb  b 0 dc 0     * 体端接地

M1 d g s b n50 W=10u L=1u



* 主仿真控制
* 情况1: 固定VGS=0V (检查GIDL和结击穿)
.dc vds 0 10 0.1 sweep vg 0 0 1   * VGS=0V

* 情况2: 固定VGS=Vth (检查碰撞电离)
* 假设Vth=0.5V，根据实际值调整
.dc vds 0 10 0.1 sweep vg 0.5 0.5 1 * VGS=0.5V

* 情况3: 固定VGS=1V (高于Vth)
.dc vds 0 10 0.1 sweep vg 1.0 1.0 1 * VGS=1.0V

* 监控关键电流
.print i(vb)    * 体电流Ib（主要监控对象）
.print i(vds)   * 漏电流Id
.print i(vg)    * 栅电流Ig（检查GIDL）

* 可选：温度影响分析（如需要）
*.dc temp 25 125 25 * 温度扫描

.end