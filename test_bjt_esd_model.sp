* BJT ESD Device SPICE Model
* Generated by BJT ESD Parameter Extractor
* Date: 2025-06-06 09:37:58

* Model Parameters:
* I_leak = 1.998100e-07 A (Leakage Current)
* Vt1 = 12.645 V (Trigger Voltage)
* k = 3.000 (Exponential Factor)
* Ron = 1.962 Ohm (On Resistance)
* Vh = 13.876 V (Holding Voltage)
* I_offset = 0.050000 A (Current Offset)
* Isb = 0.039970 A (Snapback Current)
* Vsb = 13.820 V (Snapback Voltage)

* BJT ESD Device Subcircuit
.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak=1.998100e-07
.param Vt1=12.645
.param k=3.000
.param Ron=1.962
.param Vh=13.876
.param I_offset=0.050000
.param Isb=0.039970
.param Vsb=13.820

* Simple equivalent circuit for HSPICE compatibility
* Approximates BJT ESD behavior using basic circuit elements
* Current equation with three regions:
* Region 1 (Leakage): V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2 (Trigger): Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)
* Region 3 (Snapback): V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

* Leakage resistance (approximates low voltage behavior)
Rleak anode n1 6.329e+07

* Trigger diode (approximates exponential turn-on)
Dtrig n1 n2 DESD
.model DESD D(IS=1.998100e-07 N=3.0 RS=0.1)

* On-state resistance (snapback region)
Ron n2 cathode 1.962

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit

.end
