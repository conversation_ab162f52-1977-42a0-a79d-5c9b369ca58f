#!/usr/bin/env python3
"""
Debug HSPICE parsing issues with detailed analysis
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def analyze_hspice_output_file(filename):
    """Analyze HSPICE output file in detail"""
    print(f"Analyzing HSPICE output file: {filename}")
    print("=" * 60)
    
    if not os.path.exists(filename):
        print(f"File not found: {filename}")
        return None
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
    
    print(f"Total lines in file: {len(lines)}")
    
    # Find DC section
    dc_section_start = None
    data_section_start = None
    
    for i, line in enumerate(lines):
        line_lower = line.lower()
        if "dc transfer curves" in line_lower:
            dc_section_start = i
            print(f"Found DC section at line {i}: {line.strip()}")
        elif dc_section_start and ("voltage" in line_lower and "current" in line_lower):
            print(f"Found header at line {i}: {line.strip()}")
        elif dc_section_start and line.strip() and not any(x in line_lower for x in ["voltage", "current", "***", "---", "alter", "temp"]):
            if data_section_start is None:
                data_section_start = i
                print(f"Data section starts at line {i}")
            break
    
    if dc_section_start is None:
        print("No DC section found!")
        return None
    
    if data_section_start is None:
        print("No data section found!")
        return None
    
    # Parse data section
    voltage = []
    current = []
    raw_data = []
    
    print(f"\nParsing data from line {data_section_start}...")
    
    for i in range(data_section_start, len(lines)):
        line = lines[i].strip()
        if not line or "***" in line or "job concluded" in line.lower():
            break
        
        try:
            parts = line.split()
            if len(parts) >= 3:
                # Try to parse as: index voltage current
                v_val = float(parts[1])
                i_val = float(parts[2])
                
                if 0 <= v_val <= 50:  # Reasonable voltage range
                    voltage.append(v_val)
                    current.append(abs(i_val))  # Convert to positive
                    raw_data.append((i, v_val, i_val, line))
                    
                    if len(voltage) <= 10:
                        print(f"  Point {len(voltage):3d}: V={v_val:8.6f}V, I={i_val:12.6e}A -> {abs(i_val):12.6e}A")
        except (ValueError, IndexError):
            continue
    
    print(f"\nParsed {len(voltage)} data points")
    
    if len(voltage) == 0:
        print("No valid data points found!")
        return None
    
    voltage = np.array(voltage)
    current = np.array(current)
    
    # Detailed analysis
    print(f"\nDetailed Analysis:")
    print(f"Voltage range: {voltage.min():.6f} to {voltage.max():.6f} V")
    print(f"Current range: {current.min():.6e} to {current.max():.6e} A")
    
    # Check for voltage ordering
    is_sorted = np.all(voltage[:-1] <= voltage[1:])
    print(f"Voltage data is sorted: {is_sorted}")
    
    # Check for duplicates
    unique_voltages = len(np.unique(voltage))
    print(f"Unique voltage points: {unique_voltages}/{len(voltage)}")
    
    # Check for current jumps
    if len(current) > 1:
        current_diff = np.diff(current)
        max_jump = np.max(np.abs(current_diff))
        max_jump_idx = np.argmax(np.abs(current_diff))
        print(f"Maximum current jump: {max_jump:.6e}A at index {max_jump_idx}")
        print(f"  From V={voltage[max_jump_idx]:.6f}V, I={current[max_jump_idx]:.6e}A")
        print(f"  To   V={voltage[max_jump_idx+1]:.6f}V, I={current[max_jump_idx+1]:.6e}A")
        
        # Show context around the jump
        print(f"Context around maximum jump:")
        start_idx = max(0, max_jump_idx - 2)
        end_idx = min(len(voltage), max_jump_idx + 4)
        for j in range(start_idx, end_idx):
            marker = " -> " if j == max_jump_idx else "    "
            print(f"  [{j:3d}]{marker}V={voltage[j]:8.6f}V, I={current[j]:12.6e}A")
    
    # Show first and last points
    print(f"\nFirst 5 data points:")
    for i in range(min(5, len(voltage))):
        print(f"  [{i:3d}] V={voltage[i]:8.6f}V, I={current[i]:12.6e}A")
    
    print(f"\nLast 5 data points:")
    for i in range(max(0, len(voltage)-5), len(voltage)):
        print(f"  [{i:3d}] V={voltage[i]:8.6f}V, I={current[i]:12.6e}A")
    
    return voltage, current, raw_data

def compare_with_fitted_model():
    """Compare HSPICE data with fitted model"""
    try:
        from utils.data_loader import DataLoader
        from models.bjt_esd_model import BJTESDModel
        
        if not os.path.exists('1.csv'):
            print("No experimental data file found")
            return
        
        # Load experimental data
        loader = DataLoader()
        voltage_exp, current_exp, _ = loader.load_data('1.csv')
        
        # Fit model
        model = BJTESDModel()
        fitted_params = model.fit_parameters(voltage_exp, current_exp)
        
        print(f"\nFitted model parameters:")
        for key, value in fitted_params.items():
            if key != 'r_squared':
                print(f"  {key}: {value:.6e}")
        
        # Generate fitted curve
        voltage_fitted = np.linspace(0, 20, 100)
        current_fitted = model.current_equation(voltage_fitted, **fitted_params)
        
        return voltage_fitted, current_fitted, fitted_params
        
    except Exception as e:
        print(f"Error comparing with fitted model: {e}")
        return None

def plot_debug_comparison(hspice_data, fitted_data=None):
    """Create debug plots"""
    if hspice_data is None:
        print("No HSPICE data to plot")
        return
    
    voltage_hspice, current_hspice, raw_data = hspice_data
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # Plot 1: Full comparison
    ax1.plot(voltage_hspice, current_hspice, 'g--', linewidth=2, label='HSPICE Simulation')
    
    if fitted_data:
        voltage_fitted, current_fitted, _ = fitted_data
        ax1.plot(voltage_fitted, current_fitted, 'r-', linewidth=2, label='Fitted Model')
    
    ax1.set_xlabel('Voltage (V)')
    ax1.set_ylabel('Current (A)')
    ax1.set_title('HSPICE vs Fitted Model Comparison')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Plot 2: Current differences (if fitted data available)
    if fitted_data:
        voltage_fitted, current_fitted, _ = fitted_data
        # Interpolate HSPICE data to fitted voltage points
        current_hspice_interp = np.interp(voltage_fitted, voltage_hspice, current_hspice)
        current_diff = current_hspice_interp - current_fitted
        
        ax2.plot(voltage_fitted, current_diff, 'b-', linewidth=2, label='HSPICE - Fitted')
        ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax2.set_xlabel('Voltage (V)')
        ax2.set_ylabel('Current Difference (A)')
        ax2.set_title('Current Difference (HSPICE - Fitted Model)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
    else:
        # Plot current derivative to show jumps
        if len(current_hspice) > 1:
            current_diff = np.diff(current_hspice)
            voltage_mid = (voltage_hspice[:-1] + voltage_hspice[1:]) / 2
            ax2.plot(voltage_mid, current_diff, 'b-', linewidth=2, label='Current Derivative')
            ax2.set_xlabel('Voltage (V)')
            ax2.set_ylabel('dI/dV (A/V)')
            ax2.set_title('Current Derivative (shows jumps)')
            ax2.grid(True, alpha=0.3)
            ax2.legend()
    
    plt.tight_layout()
    plt.savefig('hspice_debug_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("Debug plot saved as: hspice_debug_analysis.png")

def main():
    """Main debug function"""
    print("BJT ESD Parameter Extractor - HSPICE Debug Analysis")
    print("=" * 60)
    
    # Find HSPICE output files
    hspice_files = []
    for filename in os.listdir('.'):
        if filename.endswith('.lis'):
            hspice_files.append(filename)
    
    if not hspice_files:
        print("No HSPICE output files (.lis) found in current directory")
        return
    
    print(f"Found HSPICE output files: {hspice_files}")
    
    # Analyze the most recent or specific file
    target_file = None
    if 'bjt_esd_simulation.lis' in hspice_files:
        target_file = 'bjt_esd_simulation.lis'
    elif 'high_accuracy_bjt_esd_simulation.lis' in hspice_files:
        target_file = 'high_accuracy_bjt_esd_simulation.lis'
    else:
        target_file = hspice_files[0]
    
    print(f"\nAnalyzing: {target_file}")
    
    # Analyze HSPICE data
    hspice_data = analyze_hspice_output_file(target_file)
    
    # Compare with fitted model
    fitted_data = compare_with_fitted_model()
    
    # Create debug plots
    plot_debug_comparison(hspice_data, fitted_data)
    
    # Summary
    if hspice_data:
        voltage_hspice, current_hspice, _ = hspice_data
        print(f"\nSummary:")
        print(f"HSPICE data points: {len(voltage_hspice)}")
        print(f"Voltage range: {voltage_hspice.min():.3f} to {voltage_hspice.max():.3f} V")
        print(f"Current range: {current_hspice.min():.3e} to {current_hspice.max():.3e} A")
        
        if fitted_data:
            voltage_fitted, current_fitted, _ = fitted_data
            # Compare at 20V
            i_20v_hspice = np.interp(20.0, voltage_hspice, current_hspice)
            i_20v_fitted = np.interp(20.0, voltage_fitted, current_fitted)
            error = abs(i_20v_hspice - i_20v_fitted) / i_20v_fitted * 100
            print(f"At 20V: HSPICE={i_20v_hspice:.3f}A, Fitted={i_20v_fitted:.3f}A, Error={error:.1f}%")

if __name__ == "__main__":
    main()
