#!/usr/bin/env python3
"""
Test the final simplified HSPICE model
"""

import sys
import os
import numpy as np

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_final_model():
    """Test the final simplified model"""
    try:
        from utils.hspice_interface import HSPICEInterface
        from models.bjt_esd_model import BJTESDModel
        from utils.data_loader import DataLoader
        
        print("Testing Final Simplified HSPICE Model")
        print("=" * 40)
        
        if not os.path.exists('1.csv'):
            print("No experimental data file found")
            return False
        
        # Load and fit data
        loader = DataLoader()
        voltage_exp, current_exp, _ = loader.load_data('1.csv')
        
        model = BJTESDModel()
        fitted_params = model.fit_parameters(voltage_exp, current_exp)
        
        print(f"Fitted R² = {fitted_params.get('r_squared', 0):.4f}")
        
        # Generate new simplified model
        hspice = HSPICEInterface()
        model_content = hspice.generate_spice_model(fitted_params)
        
        # Save files
        with open('final_simplified_model.ckt', 'w') as f:
            f.write(model_content)
        
        netlist_content = hspice.generate_netlist(fitted_params, (0, 20), 100, 'final_simplified_model.ckt')
        with open('final_simplified_simulation.sp', 'w') as f:
            f.write(netlist_content)
        
        print("✓ Generated final simplified model files")
        
        # Test HSPICE if available
        if hspice.verify_hspice_installation():
            print("Running HSPICE simulation...")
            result = hspice.run_simulation_from_netlist('final_simplified_simulation.sp')
            
            if result:
                voltage_sim, current_sim = result
                print(f"✓ HSPICE simulation successful: {len(voltage_sim)} points")
                
                # Generate fitted curve for comparison
                voltage_fitted = np.linspace(0, 20, 100)
                current_fitted = model.current_equation(voltage_fitted, **fitted_params)
                
                # Compare at key voltages
                print(f"\nComparison at key voltages:")
                test_voltages = [5, 10, 15, 20]
                
                total_error = 0
                valid_points = 0
                
                for v_test in test_voltages:
                    if v_test <= voltage_fitted.max() and v_test <= voltage_sim.max():
                        i_fitted = np.interp(v_test, voltage_fitted, current_fitted)
                        i_sim = np.interp(v_test, voltage_sim, current_sim)
                        
                        if i_fitted > 0:
                            error = abs(i_sim - i_fitted) / i_fitted * 100
                            total_error += error
                            valid_points += 1
                            
                            print(f"  {v_test:2.0f}V: Fitted={i_fitted:.4f}A, HSPICE={i_sim:.4f}A, Error={error:.1f}%")
                
                # Calculate average error
                avg_error = total_error / valid_points if valid_points > 0 else 0
                print(f"\nAverage Error: {avg_error:.1f}%")
                
                if avg_error < 30:
                    print("✓ Acceptable model matching!")
                    return True
                else:
                    print("⚠ Model needs further improvement")
                    return False
            else:
                print("✗ HSPICE simulation failed")
                return False
        else:
            print("! HSPICE not available")
            return True
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_comparison_plot():
    """Create a comparison plot if matplotlib is available"""
    try:
        import matplotlib.pyplot as plt
        from utils.data_loader import DataLoader
        from models.bjt_esd_model import BJTESDModel
        from utils.hspice_interface import HSPICEInterface
        
        if not os.path.exists('1.csv'):
            return
        
        # Load experimental data
        loader = DataLoader()
        voltage_exp, current_exp, _ = loader.load_data('1.csv')
        
        # Fit model
        model = BJTESDModel()
        fitted_params = model.fit_parameters(voltage_exp, current_exp)
        
        # Generate fitted curve
        voltage_fitted = np.linspace(0, 20, 100)
        current_fitted = model.current_equation(voltage_fitted, **fitted_params)
        
        # Load HSPICE results if available
        hspice = HSPICEInterface()
        if os.path.exists('final_simplified_simulation.sp'):
            result = hspice.run_simulation_from_netlist('final_simplified_simulation.sp')
            if result:
                voltage_sim, current_sim = result
                
                # Create plot
                plt.figure(figsize=(12, 8))
                
                plt.plot(voltage_exp, current_exp, 'bo', markersize=4, alpha=0.7, label='Experimental Data')
                plt.plot(voltage_fitted, current_fitted, 'r-', linewidth=2, label='Fitted Model')
                plt.plot(voltage_sim, current_sim, 'g--', linewidth=2, label='HSPICE Simulation (Final)')
                
                plt.xlabel('Voltage (V)')
                plt.ylabel('Current (A)')
                plt.title('BJT ESD Device I-V Characteristics - Final Model Comparison')
                plt.grid(True, alpha=0.3)
                plt.legend()
                
                # Add R² info
                plt.text(0.02, 0.98, f"R² = {fitted_params.get('r_squared', 0):.4f}", 
                        transform=plt.gca().transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
                
                plt.tight_layout()
                plt.savefig('final_model_comparison.png', dpi=300, bbox_inches='tight')
                plt.show()
                print("Comparison plot saved as: final_model_comparison.png")
                
    except ImportError:
        print("Matplotlib not available for plotting")
    except Exception as e:
        print(f"Plotting failed: {e}")

def main():
    """Main test function"""
    print("BJT ESD Parameter Extractor - Final Model Test")
    print("=" * 50)
    
    # Test the final model
    success = test_final_model()
    
    if success:
        print("\n✓ Final simplified model test completed successfully")
        
        # Create comparison plot
        create_comparison_plot()
        
        print(f"\nGenerated files:")
        for filename in ['final_simplified_model.ckt', 'final_simplified_simulation.sp']:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"  {filename} - {size} bytes")
        
        print(f"\nRecommendation:")
        print(f"The simplified model should provide better stability and")
        print(f"more predictable behavior than the previous complex models.")
        
    else:
        print("\n✗ Final model test failed")
        print("Consider further model simplification or parameter adjustment")
    
    return success

if __name__ == "__main__":
    main()
