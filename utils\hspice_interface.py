"""
HSPICE interface for BJT ESD device simulation
"""

import os
import subprocess
import tempfile
from typing import Dict, Optional, Tuple
import numpy as np
import logging

logger = logging.getLogger(__name__)

class HSPICEInterface:
    """
    Interface to HSPICE for BJT ESD device simulation
    """

    def __init__(self, hspice_path: str = "hspice"):
        self.hspice_path = hspice_path
        self.temp_dir = tempfile.mkdtemp()

    def generate_netlist(self, parameters: Dict[str, float],
                        voltage_range: Tuple[float, float] = (0, 20),
                        num_points: int = 100) -> str:
        """
        Generate HSPICE netlist for BJT ESD device

        Args:
            parameters: Model parameters
            voltage_range: Voltage sweep range (start, stop)
            num_points: Number of simulation points

        Returns:
            HSPICE netlist string
        """
        v_start, v_stop = voltage_range

        netlist = f"""* BJT ESD Device Simulation
* Generated by BJT ESD Parameter Extractor

.title BJT ESD Device I-V Characteristics

* Voltage source
Vin n1 0 DC 0

* BJT ESD device model
* Using subcircuit to model pnp BJT ESD behavior
.subckt bjt_esd anode cathode
* Parameters
.param I_leak={parameters.get('I_leak', 1e-7)}
.param Vt1={parameters.get('Vt1', 12.6)}
.param k={parameters.get('k', 3.0)}
.param Ron={parameters.get('Ron', 2.0)}
.param Vh={parameters.get('Vh', 13.9)}
.param I_offset={parameters.get('I_offset', 0.05)}
.param Isb={parameters.get('Isb', 0.04)}
.param Vsb={parameters.get('Vsb', 13.8)}

* Behavioral current source modeling ESD characteristics
Gesd anode cathode cur='bjt_esd_current(V(anode,cathode))'

.func bjt_esd_current(v) {{
+  if(v < Vt1, I_leak * exp(v/Vt1),
+    if(v < Vh, I_leak * exp(k*(v-Vt1)/Vt1),
+      I_offset + (v-Vsb)/Ron + Isb*exp(-(v-Vsb))))
+}}

.ends bjt_esd

* Instantiate ESD device
Xesd n1 0 bjt_esd

* Analysis
.dc Vin {v_start} {v_stop} {(v_stop-v_start)/num_points}

* Output
.print dc I(Vin)
.probe dc I(Vin)

* Options
.option post=2
.option accurate
.option gmin=1e-15

.end
"""
        return netlist

    def run_simulation(self, parameters: Dict[str, float],
                      voltage_range: Tuple[float, float] = (0, 20),
                      num_points: int = 100) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        Run HSPICE simulation

        Args:
            parameters: Model parameters
            voltage_range: Voltage sweep range
            num_points: Number of simulation points

        Returns:
            Tuple of (voltage, current) arrays or None if failed
        """
        try:
            # Generate netlist
            netlist = self.generate_netlist(parameters, voltage_range, num_points)

            # Write netlist to temporary file
            netlist_file = os.path.join(self.temp_dir, "bjt_esd.sp")
            with open(netlist_file, 'w') as f:
                f.write(netlist)

            # Run HSPICE
            cmd = [self.hspice_path, netlist_file]
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  cwd=self.temp_dir, timeout=60)

            if result.returncode != 0:
                logger.error(f"HSPICE simulation failed: {result.stderr}")
                return None

            # Parse output
            return self._parse_hspice_output()

        except Exception as e:
            logger.error(f"HSPICE simulation error: {str(e)}")
            return None

    def _parse_hspice_output(self) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        Parse HSPICE output files

        Returns:
            Tuple of (voltage, current) arrays or None if failed
        """
        try:
            # Look for .lis file
            lis_file = os.path.join(self.temp_dir, "bjt_esd.lis")
            if not os.path.exists(lis_file):
                logger.warning("HSPICE output file not found")
                return None

            voltage = []
            current = []

            with open(lis_file, 'r') as f:
                lines = f.readlines()

            # Parse DC analysis results
            in_dc_section = False
            for line in lines:
                line = line.strip()

                if "dc analysis" in line.lower():
                    in_dc_section = True
                    continue

                if in_dc_section and line:
                    try:
                        parts = line.split()
                        if len(parts) >= 2:
                            v = float(parts[0])
                            i = float(parts[1])
                            voltage.append(v)
                            current.append(i)
                    except (ValueError, IndexError):
                        continue

            if len(voltage) == 0:
                logger.error("No simulation data found in HSPICE output")
                return None

            return np.array(voltage), np.array(current)

        except Exception as e:
            logger.error(f"Failed to parse HSPICE output: {str(e)}")
            return None

    def verify_hspice_installation(self) -> bool:
        """
        Verify HSPICE installation

        Returns:
            True if HSPICE is available
        """
        try:
            result = subprocess.run([self.hspice_path, "-v"],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception:
            return False

    def cleanup(self):
        """
        Clean up temporary files
        """
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            logger.warning(f"Failed to cleanup temporary files: {str(e)}")

    def __del__(self):
        """
        Destructor to cleanup temporary files
        """
        self.cleanup()
