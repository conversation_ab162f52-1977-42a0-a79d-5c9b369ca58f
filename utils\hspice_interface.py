"""
HSPICE interface for BJT ESD device simulation
"""

import os
import subprocess
import tempfile
from typing import Dict, Optional, <PERSON>ple
import numpy as np
import logging

logger = logging.getLogger(__name__)

class HSPICEInterface:
    """
    Interface to HSPICE for BJT ESD device simulation
    """

    def __init__(self, hspice_path: str = "hspice"):
        self.hspice_path = hspice_path
        self.temp_dir = tempfile.mkdtemp()

    def generate_netlist(self, parameters: Dict[str, float],
                        voltage_range: Tuple[float, float] = (0, 20),
                        num_points: int = 100,
                        model_file_path: str = None) -> str:
        """
        Generate HSPICE netlist (.sp) for BJT ESD device simulation

        Args:
            parameters: Model parameters
            voltage_range: Voltage sweep range (start, stop)
            num_points: Number of simulation points
            model_file_path: Path to model file (.ckt) to include

        Returns:
            HSPICE netlist string
        """
        v_start, v_stop = voltage_range
        step_size = (v_stop - v_start) / num_points

        # Include model file if provided
        include_line = ""
        if model_file_path:
            include_line = f".include '{model_file_path}'"

        netlist = f"""* BJT ESD Device Simulation Netlist (.sp file)
* Generated by BJT ESD Parameter Extractor
* Date: {self._get_timestamp()}

.title BJT ESD Device I-V Characteristics

{include_line}

* Voltage source for DC sweep
Vin n1 0 DC 0

* Instantiate ESD device from included model
Xesd n1 0 bjt_esd_device

* DC Analysis
.dc Vin {v_start} {v_stop} {step_size:.6f}

* Output commands
.print dc V(n1) I(Vin)
.probe dc V(n1) I(Vin)

* Simulation options
.option post=2
.option gmin=1e-15
.option accurate
.option runlvl=5

.end
"""
        return netlist

    def run_simulation(self, parameters: Dict[str, float],
                      voltage_range: Tuple[float, float] = (0, 20),
                      num_points: int = 100) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        Run HSPICE simulation (legacy method for backward compatibility)

        Args:
            parameters: Model parameters
            voltage_range: Voltage sweep range
            num_points: Number of simulation points

        Returns:
            Tuple of (voltage, current) arrays or None if failed
        """
        try:
            # Generate netlist
            netlist = self.generate_netlist(parameters, voltage_range, num_points)

            # Write netlist to temporary file
            netlist_file = os.path.join(self.temp_dir, "bjt_esd.sp")
            output_file = os.path.join(self.temp_dir, "bjt_esd.lis")

            with open(netlist_file, 'w') as f:
                f.write(netlist)

            logger.info(f"Generated HSPICE netlist: {netlist_file}")

            # Run HSPICE with correct command format: hspice input.sp -o output.lis
            cmd = [self.hspice_path, netlist_file, "-o", output_file]
            logger.info(f"Running HSPICE command: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  cwd=self.temp_dir, timeout=120)

            logger.info(f"HSPICE return code: {result.returncode}")
            if result.stdout:
                logger.info(f"HSPICE stdout: {result.stdout}")
            if result.stderr:
                logger.info(f"HSPICE stderr: {result.stderr}")

            if result.returncode != 0:
                logger.error(f"HSPICE simulation failed with return code {result.returncode}")
                logger.error(f"STDERR: {result.stderr}")
                return None

            # Parse output
            return self._parse_hspice_output()

        except Exception as e:
            logger.error(f"HSPICE simulation error: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def run_simulation_from_netlist(self, netlist_file_path: str) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        Run HSPICE simulation from user-selected netlist file

        Args:
            netlist_file_path: Path to the netlist file (.sp)

        Returns:
            Tuple of (voltage, current) arrays or None if failed
        """
        try:
            if not os.path.exists(netlist_file_path):
                logger.error(f"Netlist file not found: {netlist_file_path}")
                return None

            # Generate output file path
            base_name = os.path.splitext(os.path.basename(netlist_file_path))[0]
            output_file = os.path.join(os.path.dirname(netlist_file_path), f"{base_name}.lis")

            print(f"Starting HSPICE simulation...")
            print(f"Netlist file: {netlist_file_path}")
            print(f"Output file: {output_file}")

            # Run HSPICE with correct command format: hspice input.sp -o output.lis
            # Use absolute paths to avoid path issues
            abs_netlist = os.path.abspath(netlist_file_path)
            abs_output = os.path.abspath(output_file)

            cmd = [self.hspice_path, abs_netlist, "-o", abs_output]
            print(f"Running command: {' '.join(cmd)}")
            print(f"Working directory: {os.path.dirname(abs_netlist)}")

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  cwd=os.path.dirname(abs_netlist), timeout=120)

            print(f"HSPICE return code: {result.returncode}")

            if result.stdout:
                print("HSPICE stdout:")
                print(result.stdout)

            if result.stderr:
                print("HSPICE stderr:")
                print(result.stderr)

            if result.returncode != 0:
                print(f"HSPICE simulation failed with return code {result.returncode}")
                return None

            print("HSPICE simulation completed successfully!")

            # Parse output
            return self._parse_hspice_output_file(output_file)

        except Exception as e:
            print(f"HSPICE simulation error: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return None

    def _parse_hspice_output(self) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        Parse HSPICE output files

        Returns:
            Tuple of (voltage, current) arrays or None if failed
        """
        try:
            # Look for .lis file
            lis_file = os.path.join(self.temp_dir, "bjt_esd.lis")
            if not os.path.exists(lis_file):
                # Try alternative output file names
                alt_files = ["bjt_esd.out", "bjt_esd.mt0", "bjt_esd.sw0"]
                for alt_file in alt_files:
                    alt_path = os.path.join(self.temp_dir, alt_file)
                    if os.path.exists(alt_path):
                        lis_file = alt_path
                        break
                else:
                    # List all files in temp directory for debugging
                    files = os.listdir(self.temp_dir)
                    logger.warning(f"HSPICE output file not found. Available files: {files}")
                    return None

            logger.info(f"Parsing HSPICE output file: {lis_file}")

            voltage = []
            current = []

            with open(lis_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            # Parse DC analysis results - look for different possible formats
            in_dc_section = False
            data_started = False

            for i, line in enumerate(lines):
                line = line.strip()

                # Look for DC analysis section markers
                if any(marker in line.lower() for marker in [
                    "dc transfer curves", "dc analysis", "operating point",
                    "vin", "i(vin)", "sweep", "dc sweep"]):
                    in_dc_section = True
                    logger.info(f"Found DC section at line {i}: {line}")
                    continue

                # Look for data lines in DC section
                if in_dc_section and line:
                    # Skip header lines and separators
                    if any(header in line.lower() for header in [
                        "voltage", "current", "element", "node", "---", "***",
                        "alter", "temp", "x"]):
                        continue

                    # Skip empty lines and lines with only spaces/tabs
                    if not line.strip():
                        continue

                    try:
                        # Parse HSPICE output format: index voltage current
                        parts = line.split()
                        if len(parts) >= 3:
                            try:
                                # HSPICE format: column 1=index, column 2=voltage, column 3=current
                                v_val = float(parts[1])  # Second column is voltage
                                i_val = float(parts[2])  # Third column is current

                                # Sanity check: voltage should be reasonable
                                if 0 <= v_val <= 50:
                                    voltage.append(v_val)
                                    current.append(abs(i_val))  # Convert to positive value
                                    data_started = True
                                    continue  # Skip alternative parsing if successful

                            except (ValueError, IndexError):
                                pass  # Fall through to alternative parsing

                            # Try alternative parsing only if standard format fails
                            for j in range(len(parts)-1):
                                try:
                                    v_candidate = float(parts[j])
                                    i_candidate = float(parts[j+1])

                                    # Sanity check: voltage should be reasonable
                                    if 0 <= v_candidate <= 50:
                                        voltage.append(v_candidate)
                                        current.append(abs(i_candidate))  # Convert to positive value
                                        data_started = True
                                        break
                                except ValueError:
                                    continue

                    except (ValueError, IndexError):
                        # If we've started collecting data and hit an error, we might be done
                        if data_started and len(voltage) > 5:
                            break
                        continue

            if len(voltage) == 0:
                logger.error("No simulation data found in HSPICE output")
                # Log first 50 lines for debugging
                logger.error("First 50 lines of output file:")
                for i, line in enumerate(lines[:50]):
                    logger.error(f"{i+1}: {line.strip()}")
                return None

            logger.info(f"Successfully parsed {len(voltage)} data points from HSPICE output")
            return np.array(voltage), np.array(current)

        except Exception as e:
            logger.error(f"Failed to parse HSPICE output: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def _parse_hspice_output_file(self, output_file_path: str) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        Parse HSPICE output file at specified path

        Args:
            output_file_path: Path to HSPICE output file

        Returns:
            Tuple of (voltage, current) arrays or None if failed
        """
        try:
            if not os.path.exists(output_file_path):
                print(f"Output file not found: {output_file_path}")
                return None

            print(f"Parsing HSPICE output file: {output_file_path}")

            voltage = []
            current = []

            with open(output_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            # Parse DC analysis results - look for different possible formats
            in_dc_section = False
            data_started = False

            for i, line in enumerate(lines):
                line = line.strip()

                # Look for DC analysis section markers
                if any(marker in line.lower() for marker in [
                    "dc transfer curves", "dc analysis", "operating point",
                    "vin", "i(vin)", "sweep", "dc sweep"]):
                    in_dc_section = True
                    print(f"DEBUG: Found DC section at line {i}: {line}")
                    continue

                # Look for data lines in DC section
                if in_dc_section and line:
                    # Skip header lines and separators
                    if any(header in line.lower() for header in [
                        "voltage", "current", "element", "node", "---", "***",
                        "alter", "temp", "x"]):
                        continue

                    # Skip empty lines and lines with only spaces/tabs
                    if not line.strip():
                        continue

                    try:
                        # Parse HSPICE output format: index voltage current
                        parts = line.split()
                        if len(parts) >= 3:
                            try:
                                # HSPICE format: column 1=index, column 2=voltage, column 3=current
                                v_val = float(parts[1])  # Second column is voltage
                                i_val = float(parts[2])  # Third column is current

                                # Sanity check: voltage should be reasonable
                                if 0 <= v_val <= 50:
                                    voltage.append(v_val)
                                    current.append(abs(i_val))  # Convert to positive value
                                    data_started = True
                                    if len(voltage) <= 10 or len(voltage) % 20 == 0:  # Debug first 10 and every 20th point
                                        print(f"DEBUG: Parsed point {len(voltage)}: V={v_val:.6f}V, I={abs(i_val):.6e}A (from line: {line[:50]}...)")
                                    continue  # Skip alternative parsing if successful

                            except (ValueError, IndexError):
                                pass  # Fall through to alternative parsing

                            # Try alternative parsing only if standard format fails
                            for j in range(len(parts)-1):
                                try:
                                    v_candidate = float(parts[j])
                                    i_candidate = float(parts[j+1])

                                    # Sanity check: voltage should be reasonable
                                    if 0 <= v_candidate <= 50:
                                        voltage.append(v_candidate)
                                        current.append(abs(i_candidate))  # Convert to positive value
                                        data_started = True
                                        break
                                except ValueError:
                                    continue

                    except (ValueError, IndexError):
                        # If we've started collecting data and hit an error, we might be done
                        if data_started and len(voltage) > 5:
                            break
                        continue

            if len(voltage) == 0:
                print("No simulation data found in HSPICE output")
                # Print first 50 lines for debugging
                print("First 50 lines of output file:")
                for i, line in enumerate(lines[:50]):
                    print(f"{i+1}: {line.strip()}")
                return None

            print(f"Successfully parsed {len(voltage)} data points from HSPICE output")

            # Debug information
            if len(voltage) > 0:
                print(f"DEBUG: Voltage range: {np.min(voltage):.3f} to {np.max(voltage):.3f} V")
                print(f"DEBUG: Current range: {np.min(current):.3e} to {np.max(current):.3e} A")
                print(f"DEBUG: First 5 data points:")
                for i in range(min(5, len(voltage))):
                    print(f"  [{i}] V={voltage[i]:.6f}V, I={current[i]:.6e}A")
                print(f"DEBUG: Last 5 data points:")
                for i in range(max(0, len(voltage)-5), len(voltage)):
                    print(f"  [{i}] V={voltage[i]:.6f}V, I={current[i]:.6e}A")

                # Check for data anomalies
                voltage_array = np.array(voltage)
                current_array = np.array(current)

                # Check for voltage ordering
                is_sorted = np.all(voltage_array[:-1] <= voltage_array[1:])
                print(f"DEBUG: Voltage data is sorted: {is_sorted}")

                # Check for current jumps
                if len(current_array) > 1:
                    current_diff = np.diff(current_array)
                    max_jump = np.max(np.abs(current_diff))
                    max_jump_idx = np.argmax(np.abs(current_diff))
                    print(f"DEBUG: Maximum current jump: {max_jump:.3e}A at index {max_jump_idx}")
                    print(f"  From V={voltage_array[max_jump_idx]:.3f}V, I={current_array[max_jump_idx]:.3e}A")
                    print(f"  To   V={voltage_array[max_jump_idx+1]:.3f}V, I={current_array[max_jump_idx+1]:.3e}A")

                # Check for duplicates
                unique_voltages = len(np.unique(voltage_array))
                print(f"DEBUG: Unique voltage points: {unique_voltages}/{len(voltage_array)}")

            # Post-process data for better plotting quality
            voltage_processed, current_processed = self._post_process_hspice_data(voltage, current)
            return voltage_processed, current_processed

        except Exception as e:
            print(f"Failed to parse HSPICE output: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return None

    def _post_process_hspice_data(self, voltage, current):
        """
        Post-process HSPICE data for better plotting quality

        Args:
            voltage: Raw voltage data
            current: Raw current data

        Returns:
            Tuple of processed (voltage, current) arrays
        """
        if len(voltage) == 0:
            return np.array(voltage), np.array(current)

        # Convert to numpy arrays
        voltage = np.array(voltage)
        current = np.array(current)

        print(f"DEBUG: Post-processing {len(voltage)} data points...")

        # Remove duplicate voltage points (keep first occurrence)
        unique_indices = []
        seen_voltages = set()

        for i, v in enumerate(voltage):
            v_rounded = round(v, 6)  # 6 decimal places precision
            if v_rounded not in seen_voltages:
                seen_voltages.add(v_rounded)
                unique_indices.append(i)

        if len(unique_indices) < len(voltage):
            print(f"DEBUG: Removed {len(voltage) - len(unique_indices)} duplicate voltage points")
            voltage = voltage[unique_indices]
            current = current[unique_indices]

        # Ensure data is sorted by voltage
        sorted_indices = np.argsort(voltage)
        voltage = voltage[sorted_indices]
        current = current[sorted_indices]

        # Optional: Resample to uniform spacing if too many points
        if len(voltage) > 150:
            print(f"DEBUG: Resampling from {len(voltage)} to 100 points for smoother plotting")
            voltage_uniform = np.linspace(voltage.min(), voltage.max(), 100)
            current_uniform = np.interp(voltage_uniform, voltage, current)
            voltage = voltage_uniform
            current = current_uniform

        # Final validation
        print(f"DEBUG: Final processed data: {len(voltage)} points")
        print(f"DEBUG: Voltage range: {voltage.min():.6f} to {voltage.max():.6f} V")
        print(f"DEBUG: Current range: {current.min():.6e} to {current.max():.6e} A")

        return voltage, current

    def verify_hspice_installation(self) -> bool:
        """
        Verify HSPICE installation

        Returns:
            True if HSPICE is available
        """
        try:
            result = subprocess.run([self.hspice_path, "-v"],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception:
            return False

    def cleanup(self):
        """
        Clean up temporary files
        """
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            logger.warning(f"Failed to cleanup temporary files: {str(e)}")

    def save_spice_model(self, parameters: Dict[str, float], output_path: str) -> bool:
        """
        Save SPICE model file (.ckt) for BJT ESD device

        Args:
            parameters: Model parameters
            output_path: Output file path for the model (.ckt)

        Returns:
            True if successful
        """
        try:
            model_content = self.generate_spice_model(parameters)

            with open(output_path, 'w') as f:
                f.write(model_content)

            logger.info(f"SPICE model (.ckt) saved to: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to save SPICE model: {str(e)}")
            return False

    def generate_spice_model(self, parameters: Dict[str, float]) -> str:
        """
        Generate SPICE model file content (.ckt) for BJT ESD device

        Args:
            parameters: Model parameters

        Returns:
            SPICE model file content
        """
        model_content = f"""* BJT ESD Device SPICE Model (.ckt file)
* Generated by BJT ESD Parameter Extractor
* Date: {self._get_timestamp()}

* Model Parameters:
* I_leak = {parameters.get('I_leak', 1e-7):.6e} A (Leakage Current)
* Vt1 = {parameters.get('Vt1', 12.6):.3f} V (Trigger Voltage)
* k = {parameters.get('k', 3.0):.3f} (Exponential Factor)
* Ron = {parameters.get('Ron', 2.0):.3f} Ohm (On Resistance)
* Vh = {parameters.get('Vh', 13.9):.3f} V (Holding Voltage)
* I_offset = {parameters.get('I_offset', 0.05):.6f} A (Current Offset)
* Isb = {parameters.get('Isb', 0.04):.6f} A (Snapback Current)
* Vsb = {parameters.get('Vsb', 13.8):.3f} V (Snapback Voltage)

* BJT ESD Device Subcircuit with HSPICE if statements
.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak={parameters.get('I_leak', 1e-7):.6e}
.param Vt1={parameters.get('Vt1', 12.6):.3f}
.param k={parameters.get('k', 3.0):.3f}
.param Ron={parameters.get('Ron', 2.0):.3f}
.param Vh={parameters.get('Vh', 13.9):.3f}
.param I_offset={parameters.get('I_offset', 0.05):.6f}
.param Isb={parameters.get('Isb', 0.04):.6f}
.param Vsb={parameters.get('Vsb', 13.8):.3f}

* BJT ESD behavior using behavioral current source for better accuracy
* Current equation with three regions:
* Region 1 (Leakage): V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2 (Trigger): Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)
* Region 3 (Snapback): V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

* Working BJT ESD model using proven simple components
* This model provides stable and predictable I-V characteristics

* Path 1: Low voltage exponential behavior
R1 anode n1 {max(parameters.get('Ron', 2.0)*10, 10.0):.1f}
D1 n1 cathode D1
.model D1 D(IS={parameters.get('I_leak', 1e-7)*1e8:.3e} N={min(parameters.get('k', 3.0)*0.8, 3.0):.1f} RS=0.1)

* Path 2: High voltage linear behavior
R2 anode cathode {20.0/max(parameters.get('I_offset', 0.05)*20, 1.0):.1f}

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit
"""
        return model_content

    def _generate_bjt_esd_table(self, parameters: Dict[str, float]) -> str:
        """
        Generate accurate I-V table for BJT ESD device using the mathematical model

        Args:
            parameters: Model parameters

        Returns:
            PWL table string for HSPICE
        """
        import numpy as np
        from models.bjt_esd_model import BJTESDModel

        # Create voltage points with higher density around critical regions
        model = BJTESDModel()

        # Extract parameters
        I_leak = parameters.get('I_leak', 1e-7)
        Vt1 = parameters.get('Vt1', 12.6)
        Vh = parameters.get('Vh', 13.9)

        # Generate voltage points with adaptive spacing
        v_points = []

        # Dense points in leakage region (0 to Vt1)
        v_points.extend(np.linspace(0, Vt1*0.9, 20))

        # Very dense points around trigger voltage
        v_points.extend(np.linspace(Vt1*0.9, Vt1*1.1, 10))

        # Dense points in trigger region (Vt1 to Vh)
        v_points.extend(np.linspace(Vt1*1.1, Vh*0.95, 15))

        # Very dense points around holding voltage
        v_points.extend(np.linspace(Vh*0.95, Vh*1.05, 10))

        # Points in snapback region (Vh to 20V)
        v_points.extend(np.linspace(Vh*1.05, 20.0, 20))

        # Remove duplicates and sort
        v_points = sorted(list(set(v_points)))
        v_array = np.array(v_points)

        # Calculate corresponding currents using the exact BJT ESD equation
        i_array = model.current_equation(v_array, **parameters)

        # Format as PWL table for HSPICE
        table_entries = []
        for v, i in zip(v_array, i_array):
            table_entries.append(f"{v:.6f}, {i:.6e}")

        return " + \n+ ".join(table_entries)

    def _generate_hspice_current_source(self, parameters: Dict[str, float]) -> str:
        """
        Generate HSPICE current source with PWL table for BJT ESD device

        Args:
            parameters: Model parameters

        Returns:
            HSPICE current source definition
        """
        import numpy as np
        from models.bjt_esd_model import BJTESDModel

        # Create voltage points with higher density around critical regions
        model = BJTESDModel()

        # Extract parameters for critical voltage points
        Vt1 = parameters.get('Vt1', 12.6)
        Vh = parameters.get('Vh', 13.9)

        # Generate voltage points with adaptive spacing
        v_points = []

        # Dense points in leakage region (0 to Vt1)
        v_points.extend(np.linspace(0, Vt1*0.9, 15))

        # Very dense points around trigger voltage
        v_points.extend(np.linspace(Vt1*0.9, Vt1*1.1, 8))

        # Dense points in trigger region (Vt1 to Vh)
        v_points.extend(np.linspace(Vt1*1.1, Vh*0.95, 12))

        # Very dense points around holding voltage
        v_points.extend(np.linspace(Vh*0.95, Vh*1.05, 8))

        # Points in snapback region (Vh to 20V)
        v_points.extend(np.linspace(Vh*1.05, 20.0, 15))

        # Remove duplicates and sort
        v_points = sorted(list(set(v_points)))
        v_array = np.array(v_points)

        # Calculate corresponding currents using the exact BJT ESD equation
        i_array = model.current_equation(v_array, **parameters)

        # Generate HSPICE PWL current source
        pwl_pairs = []
        for v, i in zip(v_array, i_array):
            pwl_pairs.append(f"({v:.6f},{i:.6e})")

        # Create current source with PWL table
        current_source = f"Gesd anode cathode PWL(1) V(anode,cathode) "
        current_source += " ".join(pwl_pairs)

        return current_source

    def save_netlist(self, parameters: Dict[str, float], output_path: str,
                    model_file_path: str = None,
                    voltage_range: Tuple[float, float] = (0, 20),
                    num_points: int = 100) -> bool:
        """
        Save complete HSPICE netlist (.sp) for simulation

        Args:
            parameters: Model parameters
            output_path: Output file path for the netlist (.sp)
            model_file_path: Path to the model file (.ckt) to include
            voltage_range: Voltage sweep range
            num_points: Number of simulation points

        Returns:
            True if successful
        """
        try:
            netlist = self.generate_netlist(parameters, voltage_range, num_points, model_file_path)

            with open(output_path, 'w') as f:
                f.write(netlist)

            logger.info(f"HSPICE netlist (.sp) saved to: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to save HSPICE netlist: {str(e)}")
            return False

    def _get_timestamp(self) -> str:
        """
        Get current timestamp string

        Returns:
            Formatted timestamp string
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def get_temp_files(self) -> Dict[str, str]:
        """
        Get paths to temporary files created during simulation

        Returns:
            Dictionary of file types and their paths
        """
        files = {}
        if os.path.exists(self.temp_dir):
            for filename in os.listdir(self.temp_dir):
                filepath = os.path.join(self.temp_dir, filename)
                if filename.endswith('.sp'):
                    files['netlist'] = filepath
                elif filename.endswith('.lis'):
                    files['output'] = filepath
                elif filename.endswith('.out'):
                    files['output_alt'] = filepath
                elif filename.endswith('.mt0'):
                    files['measure'] = filepath
        return files

    def __del__(self):
        """
        Destructor to cleanup temporary files
        """
        self.cleanup()
