****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******
Input File: D:\code\esd\bjt_esd_simulation.sp
lic:
lic: FLEXlm: SDK_12.3
lic: USER:   e02727               HOSTNAME: ascend27
lic: HOSTID: "982cbcdcead7"       PID:      32968
lic: Using FLEXlm license file:
lic: 27000@ascend27
lic: Checkout 1 hspice
lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12
lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@ascend27
lic:

 init: begin read circuit files, cpu clock= 2.40E-02
       option post =     2.00
       option gmin =    1.000E-15
       option accurate
       option runlvl =     5.00
 init: end read circuit files, cpu clock= 2.60E-02 peak memory=      61 mb
 init: begin check errors, cpu clock= 2.60E-02
 init: end check errors, cpu clock= 2.70E-02 peak memory=      61 mb
 init: begin setup matrix, pivot=     0 cpu clock= 2.70E-02
       establish matrix -- done, cpu clock= 2.70E-02 peak memory=      61 mb
       re-order matrix -- done, cpu clock= 2.70E-02 peak memory=      61 mb
 init: end setup matrix, cpu clock= 2.80E-02 peak memory=      61 mb
 output: D:\code\esd\bjt_esd_simulation.sw0
 sweep: dc dc0    begin, #sweeps= 101 cpu clock= 2.80E-02
 sweep: dc dc0    end, cpu clock= 3.50E-02 peak memory=      61 mb
>info:         ***** hspice job concluded
 lic: Release hspice token(s)
