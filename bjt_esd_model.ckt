* BJT ESD Device SPICE Model (.ckt file)
* Generated by BJT ESD Parameter Extractor
* Date: 2025-06-06 11:14:15

* Model Parameters:
* I_leak = 9.161209e-11 A (Leakage Current)
* Vt1 = 3.841 V (Trigger Voltage)
* k = 7.812 (Exponential Factor)
* Ron = 1.213 Ohm (On Resistance)
* Vh = 14.011 V (Holding Voltage)
* I_offset = 0.152575 A (Current Offset)
* Isb = 0.292588 A (Snapback Current)
* Vsb = 14.661 V (Snapback Voltage)

* BJT ESD Device Subcircuit with HSPICE if statements
.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak=9.161209e-11
.param Vt1=3.841
.param k=7.812
.param Ron=1.213
.param Vh=14.011
.param I_offset=0.152575
.param Isb=0.292588
.param Vsb=14.661

* BJT ESD behavior using behavioral current source for better accuracy
* Current equation with three regions:
* Region 1 (Leakage): V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2 (Trigger): Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)
* Region 3 (Snapback): V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

* High-accuracy equivalent circuit optimized for BJT ESD characteristics
* This model uses carefully tuned parameters to match experimental data

* Primary current path for snapback region (main contributor)
* This path provides the majority of current in high voltage region
Rmain anode n_main 5.0
Dmain n_main cathode DESD_MAIN
.model DESD_MAIN D(IS=5e-3 N=1.0 RS=0.005 BV=13.3 IBV=3.1)

* Secondary trigger path for exponential behavior
* This path models the trigger region behavior
Rtrig anode n_trig 50.0
Dtrig n_trig n_mid DESD_TRIGGER
.model DESD_TRIGGER D(IS=1e-6 N=1.2 RS=0.01 BV=12.6 IBV=0.5)

* Final stage resistance (very low for high current)
Rfinal n_mid cathode 0.121

* Leakage path for low voltage behavior
Rleak anode n_leak 5e+5
Dleak n_leak cathode DESD_LEAK
.model DESD_LEAK D(IS=1e-8 N=2.0 RS=1.0 BV=16.8 IBV=0.01)

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit
