* BJT ESD Device SPICE Model (.ckt file)
* Generated by BJT ESD Parameter Extractor
* Date: 2025-06-06 10:43:40

* Model Parameters:
* I_leak = 1.000015e-12 A (Leakage Current)
* Vt1 = 3.503 V (Trigger Voltage)
* k = 8.455 (Exponential Factor)
* Ron = 1.211 Ohm (On Resistance)
* Vh = 14.058 V (Holding Voltage)
* I_offset = 0.154913 A (Current Offset)
* Isb = 0.299319 A (Snapback Current)
* Vsb = 14.670 V (Snapback Voltage)

* BJT ESD Device Subcircuit with HSPICE if statements
.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak=1.000015e-12
.param Vt1=3.503
.param k=8.455
.param Ron=1.211
.param Vh=14.058
.param I_offset=0.154913
.param Isb=0.299319
.param Vsb=14.670

* BJT ESD behavior using behavioral current source for better accuracy
* Current equation with three regions:
* Region 1 (Leakage): V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2 (Trigger): Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)
* Region 3 (Snapback): V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

* Improved equivalent circuit for better accuracy
* This model uses multiple stages to approximate the three regions

* Region 1: Leakage region (V < Vt1)
* Very high resistance for low current
Rleak anode n1 3.503e+10

* Region 2: Trigger region (Vt1 �� V < Vh)
* Exponential turn-on using optimized diode parameters
Dtrig n1 n2 DESD_TRIGGER
.model DESD_TRIGGER D(IS=1.000015e-12 N=2.8 RS=0.01 BV=14.8 IBV=1e-6)

* Region 3: Snapback region (V �� Vh)
* Low resistance for high current
Dsnap n2 n3 DESD_SNAPBACK
.model DESD_SNAPBACK D(IS=1e-12 N=1.0 RS=0.001 BV=14.058 IBV=0.154913)

* Final on-state resistance
Ron n3 cathode 1.211

* Additional current path for better accuracy in high voltage region
Rhigh anode n4 100
Dhigh n4 cathode DESD_HIGH
.model DESD_HIGH D(IS=1e-15 N=1.0 RS=0.1 BV=15.5 IBV=0.5)

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit
