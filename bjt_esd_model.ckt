* BJT ESD Device SPICE Model (.ckt file)
* Generated by BJT ESD Parameter Extractor
* Date: 2025-06-06 14:29:16

* Model Parameters:
* I_leak = 1.998100e-07 A (Leakage Current)
* Vt1 = 12.645 V (Trigger Voltage)
* k = 3.000 (Exponential Factor)
* Ron = 1.962 Ohm (On Resistance)
* Vh = 13.876 V (Holding Voltage)
* I_offset = 0.050000 A (Current Offset)
* Isb = 0.039970 A (Snapback Current)
* Vsb = 13.820 V (Snapback Voltage)

* BJT ESD Device Subcircuit with HSPICE if statements
.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak=1.998100e-07
.param Vt1=12.645
.param k=3.000
.param Ron=1.962
.param Vh=13.876
.param I_offset=0.050000
.param Isb=0.039970
.param Vsb=13.820

* BJT ESD behavior using behavioral current source for better accuracy
* Current equation with three regions:
* Region 1 (Leakage): V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2 (Trigger): Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)
* Region 3 (Snapback): V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

* PWL current source for exact curve matching
* Uses lookup table generated from fitted mathematical model
Gesd anode cathode PWL(1) V(anode,cathode) (0.000000,1.998100e-07) (0.812893,2.130768e-07) (1.625786,2.272245e-07) (2.438679,2.423115e-07) (3.251571,2.584003e-07) (4.064464,2.755573e-07) (4.877357,2.938535e-07) (5.690250,3.133645e-07) (6.503143,3.341709e-07) (7.316036,3.563589e-07) (8.128929,3.800201e-07) (8.941821,4.052523e-07) (9.754714,4.321598e-07) (10.567607,4.608540e-07) (11.380500,4.914533e-07) (11.741786,5.056973e-07) (12.103071,5.203542e-07) (12.464357,5.354359e-07) (12.825643,2.085594e-07) (13.182200,2.269697e-07) (13.186929,2.272245e-07) (13.248318,2.305581e-07) (13.314436,2.342032e-07) (13.380429,2.378989e-07) (13.380555,2.379060e-07) (13.446673,2.416673e-07) (13.512791,2.454881e-07) (13.548214,2.475599e-07) (13.578657,2.493544e-07) (13.578909,2.493693e-07) (13.645027,2.533118e-07) (13.711145,2.573167e-07) (13.776886,2.613615e-07) (13.777264,2.613849e-07) (13.843382,2.655174e-07) (13.909500,1.321648e-01) (13.975114,1.632863e-01) (14.173343,2.581656e-01) (14.371571,3.541516e-01) (14.569800,4.510453e-01) (14.957671,6.426659e-01) (15.345543,8.362384e-01) (15.733414,1.031135e+00) (16.121286,1.226931e+00) (16.509157,1.423336e+00) (16.897029,1.620155e+00) (17.284900,1.817254e+00) (17.672771,2.014544e+00) (18.060643,2.211963e+00) (18.448514,2.409470e+00) (18.836386,2.607036e+00) (19.224257,2.804643e+00) (19.612129,3.002277e+00) (20.000000,3.199930e+00)

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit
