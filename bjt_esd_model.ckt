* BJT ESD Device SPICE Model (.ckt file)
* Generated by BJT ESD Parameter Extractor
* Date: 2025-06-06 17:19:48

* Model Parameters:
* I_leak = 1.000000e-09 A (Leakage Current)
* Vt1 = 0.068 V (Trigger Voltage)
* k = 1.000 (Exponential Factor)
* Ron = 1.125 Ohm (On Resistance)
* Vh = 12.720 V (Holding Voltage)
* I_offset = 4.920000 A (Current Offset)
* Isb = 0.498000 A (Snapback Current)
* Vsb = 20.448 V (Snapback Voltage)

* BJT ESD Device Subcircuit
.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak=1.000000e-09
.param Vt1=0.068
.param k=1.000
.param Ron=1.125
.param Vh=12.720
.param I_offset=4.920000
.param Isb=0.498000
.param Vsb=20.448

* BJT ESD behavior using HSPICE behavioral modeling
* Three-region model implementation

* Voltage sensing
V_sense anode n_sense 0

* Current source with behavioral model
G_esd n_sense cathode cur='bjt_esd_current(V(anode,cathode))'

* BJT ESD current function
.param bjt_esd_current(v) = '
+ if(v < 0, I_leak*1e-3,
+ if(v < Vt1, I_leak*exp(v/1.0),
+ if(v < Vh, I_leak*exp(k*(v-Vt1)/Vt1),
+ I_offset + (v-Vsb)/Ron + Isb*exp(-(v-Vsb))
+ )))'

* Alternative implementation using diode + resistor for better convergence
D_main anode n_main D_ESD
R_main n_main cathode {Ron}

* Diode model parameters calculated from BJT ESD parameters
.model D_ESD D(
+ IS={I_leak*1e6}
+ N={k}
+ RS=0.1
+ BV={vh*1.05}
+ IBV={i_offset*0.1}
+ CJO=1e-12
+ TT=1e-12
+ )

* Parallel resistance for high voltage region
R_parallel anode cathode {Ron*5}

.ends bjt_esd_device

* Usage Example:
* XBJT_ESD_DEVICE node_anode node_cathode bjt_esd_device

* Test Circuit Example:
.subckt test_circuit
* Voltage source for DC sweep
Vin n1 0 DC 0
* ESD device under test
X_esd n1 0 bjt_esd_device
* DC analysis
.dc Vin 0 20 0.1
* Print current through voltage source
.print dc I(Vin)
.ends test_circuit

* Simulation commands for HSPICE
* .option post
* .option accurate
* .option gmin=1e-15
* .option abstol=1e-15
* .option reltol=1e-6
