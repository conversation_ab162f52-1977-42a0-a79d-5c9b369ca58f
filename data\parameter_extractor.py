"""
Parameter Extractor for BJT ESD Device
"""

import numpy as np
from scipy.optimize import curve_fit, minimize
import warnings

class ParameterExtractor:
    """Class for extracting BJT ESD device parameters from I-V data"""
    
    def __init__(self):
        pass
        
    def extract_parameters(self, data):
        """
        Extract BJT ESD parameters from measurement data
        
        Args:
            data (dict): Dictionary containing voltage and current arrays
            
        Returns:
            dict: Dictionary containing extracted parameters
        """
        voltage = data['voltage']
        current = data['current']
        
        # Initialize parameters
        parameters = {}
        
        try:
            # Find different regions in the I-V curve
            regions = self.identify_regions(voltage, current)
            
            # Extract leakage region parameters
            leakage_params = self.extract_leakage_parameters(
                voltage, current, regions['leakage_end']
            )
            parameters.update(leakage_params)
            
            # Extract trigger region parameters
            trigger_params = self.extract_trigger_parameters(
                voltage, current, regions['leakage_end'], regions['trigger_end']
            )
            parameters.update(trigger_params)
            
            # Extract snapback region parameters
            snapback_params = self.extract_snapback_parameters(
                voltage, current, regions['trigger_end']
            )
            parameters.update(snapback_params)
            
            return parameters
            
        except Exception as e:
            # Return default parameters if extraction fails
            print(f"Parameter extraction failed: {e}")
            return self.get_default_parameters()
            
    def identify_regions(self, voltage, current):
        """
        Identify different regions in the I-V curve
        
        Args:
            voltage (array): Voltage values
            current (array): Current values
            
        Returns:
            dict: Dictionary with region boundaries
        """
        # Calculate derivative to find transition points
        log_current = np.log10(current + 1e-15)  # Add small value to avoid log(0)
        d_log_i_dv = np.gradient(log_current, voltage)
        
        # Find leakage region end (where derivative starts increasing rapidly)
        leakage_end_idx = 0
        for i in range(1, len(d_log_i_dv) - 1):
            if d_log_i_dv[i] > 0.5:  # Threshold for rapid increase
                leakage_end_idx = i
                break
        
        # Find trigger region end (where current reaches significant level)
        trigger_end_idx = len(voltage) - 1
        for i in range(leakage_end_idx, len(current)):
            if current[i] > 1e-3:  # 1mA threshold
                trigger_end_idx = i
                break
                
        return {
            'leakage_end': leakage_end_idx,
            'trigger_end': trigger_end_idx
        }
        
    def extract_leakage_parameters(self, voltage, current, end_idx):
        """Extract leakage region parameters"""
        if end_idx < 5:  # Need at least 5 points
            end_idx = min(20, len(voltage) - 1)
            
        v_leak = voltage[:end_idx]
        i_leak = current[:end_idx]
        
        # Fit exponential model: I = I_leak * exp(V/Vt)
        try:
            def leakage_model(v, i_leak, vt):
                return i_leak * np.exp(v / vt)
                
            # Initial guess
            p0 = [1e-9, 1.0]
            
            # Fit with bounds
            bounds = ([1e-15, 0.1], [1e-3, 10.0])
            popt, _ = curve_fit(leakage_model, v_leak, i_leak, p0=p0, bounds=bounds)
            
            return {
                'I_leak': popt[0],
                'Vt_leak': popt[1]
            }
            
        except:
            # Return default values if fitting fails
            return {
                'I_leak': 1e-9,
                'Vt_leak': 1.0
            }
            
    def extract_trigger_parameters(self, voltage, current, start_idx, end_idx):
        """Extract trigger region parameters"""
        if end_idx <= start_idx:
            end_idx = min(start_idx + 20, len(voltage) - 1)
            
        v_trig = voltage[start_idx:end_idx]
        i_trig = current[start_idx:end_idx]
        
        if len(v_trig) < 3:
            return {'Vt1': 12.0, 'k': 3.0}
            
        try:
            # Find trigger voltage (where current starts increasing rapidly)
            log_i = np.log10(i_trig + 1e-15)
            d_log_i = np.gradient(log_i, v_trig)
            
            # Trigger voltage is where derivative is maximum
            max_deriv_idx = np.argmax(d_log_i)
            vt1 = v_trig[max_deriv_idx]
            
            # Exponential factor from slope
            k = np.mean(d_log_i[max_deriv_idx:]) * np.log(10)
            k = max(1.0, min(10.0, k))  # Clamp to reasonable range
            
            return {
                'Vt1': vt1,
                'k': k
            }
            
        except:
            return {'Vt1': 12.0, 'k': 3.0}
            
    def extract_snapback_parameters(self, voltage, current, start_idx):
        """Extract snapback region parameters"""
        if start_idx >= len(voltage) - 3:
            return self.get_default_snapback_parameters()
            
        v_snap = voltage[start_idx:]
        i_snap = current[start_idx:]
        
        try:
            # Find holding voltage (minimum voltage in snapback region)
            min_v_idx = np.argmin(v_snap)
            vh = v_snap[min_v_idx]
            
            # Find snapback voltage (voltage at maximum current before snapback)
            max_i_idx = np.argmax(i_snap)
            vsb = v_snap[max_i_idx] if max_i_idx > 0 else vh
            
            # On resistance from linear region slope
            if len(v_snap) > 5:
                # Use last few points for linear fit
                v_linear = v_snap[-5:]
                i_linear = i_snap[-5:]
                
                # Linear fit: I = I_offset + (V - Vsb) / Ron
                slope = np.polyfit(v_linear, i_linear, 1)[0]
                ron = 1.0 / slope if slope > 0 else 2.0
                ron = max(0.1, min(10.0, ron))  # Clamp to reasonable range
            else:
                ron = 2.0
                
            # Current offset and snapback current
            i_offset = np.mean(i_snap[-3:]) if len(i_snap) > 3 else 0.05
            isb = np.max(i_snap) * 0.1  # 10% of maximum current
            
            return {
                'Ron': ron,
                'Vh': vh,
                'I_offset': i_offset,
                'Isb': isb,
                'Vsb': vsb
            }
            
        except:
            return self.get_default_snapback_parameters()
            
    def get_default_snapback_parameters(self):
        """Get default snapback parameters"""
        return {
            'Ron': 2.0,
            'Vh': 14.0,
            'I_offset': 0.05,
            'Isb': 0.04,
            'Vsb': 14.0
        }
        
    def get_default_parameters(self):
        """Get default parameter set"""
        return {
            'I_leak': 1e-9,
            'Vt1': 12.0,
            'k': 3.0,
            'Ron': 2.0,
            'Vh': 14.0,
            'I_offset': 0.05,
            'Isb': 0.04,
            'Vsb': 14.0
        }
        
    def calculate_model_current(self, voltage, parameters):
        """
        Calculate model current using BJT ESD model
        
        Args:
            voltage (array): Voltage values
            parameters (dict): Model parameters
            
        Returns:
            array: Calculated current values
        """
        # Extract parameters
        i_leak = parameters.get('I_leak', 1e-9)
        vt1 = parameters.get('Vt1', 12.0)
        k = parameters.get('k', 3.0)
        ron = parameters.get('Ron', 2.0)
        vh = parameters.get('Vh', 14.0)
        i_offset = parameters.get('I_offset', 0.05)
        isb = parameters.get('Isb', 0.04)
        vsb = parameters.get('Vsb', 14.0)
        
        current = np.zeros_like(voltage)
        
        for i, v in enumerate(voltage):
            if v < vt1:
                # Leakage region
                current[i] = i_leak * np.exp(v / 1.0)  # Simple exponential
            elif v < vh:
                # Trigger region
                current[i] = i_leak * np.exp(k * (v - vt1) / vt1)
            else:
                # Snapback region
                linear_term = (v - vsb) / ron if ron > 0 else 0
                exp_term = isb * np.exp(-(v - vsb)) if v > vsb else isb
                current[i] = i_offset + linear_term + exp_term
                
        return current
        
    def optimize_parameters(self, data, initial_params):
        """
        Optimize parameters using least squares fitting
        
        Args:
            data (dict): Measurement data
            initial_params (dict): Initial parameter guess
            
        Returns:
            dict: Optimized parameters
        """
        voltage = data['voltage']
        current = data['current']
        
        # Define parameter bounds
        bounds = {
            'I_leak': (1e-15, 1e-3),
            'Vt1': (1.0, 20.0),
            'k': (1.0, 10.0),
            'Ron': (0.1, 10.0),
            'Vh': (10.0, 20.0),
            'I_offset': (0.001, 1.0),
            'Isb': (0.001, 1.0),
            'Vsb': (10.0, 20.0)
        }
        
        # Convert to optimization format
        param_names = list(initial_params.keys())
        x0 = [initial_params[name] for name in param_names]
        lower_bounds = [bounds[name][0] for name in param_names]
        upper_bounds = [bounds[name][1] for name in param_names]
        
        def objective(x):
            params = dict(zip(param_names, x))
            model_current = self.calculate_model_current(voltage, params)
            
            # Use log scale for better fitting
            log_measured = np.log10(current + 1e-15)
            log_model = np.log10(model_current + 1e-15)
            
            return np.sum((log_measured - log_model) ** 2)
            
        try:
            result = minimize(objective, x0, bounds=list(zip(lower_bounds, upper_bounds)),
                            method='L-BFGS-B')
            
            if result.success:
                optimized_params = dict(zip(param_names, result.x))
                return optimized_params
            else:
                return initial_params
                
        except:
            return initial_params
