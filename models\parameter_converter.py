"""
Parameter Converter for BJT ESD Model
Converts fitted mathematical model parameters to SPICE model parameters
with validation and optimization for exact matching
"""

import numpy as np
from scipy.optimize import minimize, differential_evolution
import warnings

class ParameterConverter:
    """
    Converts fitted BJT ESD model parameters to SPICE-compatible parameters
    with validation and optimization for exact matching
    """

    def __init__(self):
        self.fitted_params = None
        self.spice_params = None
        self.voltage_range = None
        self.target_current = None

    def set_fitted_model(self, parameters, voltage_range, target_current):
        """
        Set the fitted model parameters and target I-V curve

        Args:
            parameters (dict): Fitted model parameters
            voltage_range (array): Voltage points
            target_current (array): Target current values
        """
        self.fitted_params = parameters.copy()
        self.voltage_range = voltage_range.copy()
        self.target_current = target_current.copy()

    def calculate_fitted_current(self, voltage, parameters):
        """
        Calculate current using fitted mathematical model

        Args:
            voltage (array): Voltage values
            parameters (dict): Fitted model parameters

        Returns:
            array: Current values from fitted model
        """
        i_leak = parameters['I_leak']
        vt1 = parameters['Vt1']
        k = parameters['k']
        ron = parameters['Ron']
        vh = parameters['Vh']
        i_offset = parameters['I_offset']
        isb = parameters['Isb']
        vsb = parameters['Vsb']

        current = np.zeros_like(voltage)

        for i, v in enumerate(voltage):
            if v < 0:
                # Negative voltage - reverse bias
                current[i] = i_leak * 1e-3
            elif v < vt1:
                # Leakage region
                current[i] = i_leak * np.exp(v / 1.0)
            elif v < vh:
                # Trigger region
                current[i] = i_leak * np.exp(k * (v - vt1) / vt1)
            else:
                # Snapback region
                linear_term = (v - vsb) / ron if ron > 0 else 0
                exp_term = isb * np.exp(-(v - vsb)) if v > vsb else isb
                current[i] = i_offset + linear_term + exp_term

        return current

    def calculate_spice_current(self, voltage, spice_params):
        """
        Calculate current using SPICE diode model

        Args:
            voltage (array): Voltage values
            spice_params (dict): SPICE model parameters

        Returns:
            array: Current values from SPICE model
        """
        # SPICE diode equation: I = IS * (exp(V/(N*Vt)) - 1)
        # For ESD: I = IS * exp(V/(N*Vt)) + V/R_parallel

        IS = spice_params['IS']
        N = spice_params['N']
        RS = spice_params.get('RS', 0.1)
        BV = spice_params['BV']
        IBV = spice_params.get('IBV', 1e-6)
        R_parallel = spice_params.get('R_parallel', 1e6)

        Vt = 0.026  # Thermal voltage at room temperature
        current = np.zeros_like(voltage)

        for i, v in enumerate(voltage):
            if v < 0:
                # Reverse bias
                if abs(v) < BV:
                    current[i] = -IS * (np.exp(-v/(N*Vt)) - 1)
                else:
                    # Breakdown
                    current[i] = IBV * np.exp(-(abs(v) - BV)/Vt)
            else:
                # Forward bias
                v_diode = v

                # Simple diode equation with overflow protection
                exp_arg = min(v_diode/(N*Vt), 50)  # Limit to prevent overflow
                i_diode = IS * (np.exp(exp_arg) - 1)
                i_parallel = v / R_parallel if R_parallel > 0 else 0
                i_total = i_diode + i_parallel

                current[i] = max(i_total, 1e-15)  # Ensure positive current

        return current

    def convert_to_spice_parameters(self, method='optimization'):
        """
        Convert fitted parameters to SPICE parameters

        Args:
            method (str): Conversion method ('direct', 'optimization', 'hybrid')

        Returns:
            dict: SPICE model parameters
        """
        if self.fitted_params is None:
            raise ValueError("Fitted parameters not set. Call set_fitted_model first.")

        if method == 'direct':
            return self._direct_conversion()
        elif method == 'optimization':
            return self._optimization_conversion()
        elif method == 'hybrid':
            # Start with direct conversion, then optimize
            initial_params = self._direct_conversion()
            return self._optimization_conversion(initial_params)
        else:
            raise ValueError("Unknown conversion method: {}".format(method))

    def _direct_conversion(self):
        """
        Direct parameter conversion using empirical relationships

        Returns:
            dict: SPICE parameters
        """
        fitted = self.fitted_params

        # Direct mapping with physical considerations
        spice_params = {
            # Saturation current - related to leakage current
            'IS': fitted['I_leak'] * 1e6,  # Scale up for SPICE

            # Ideality factor - related to exponential slope
            'N': fitted['k'],

            # Series resistance
            'RS': 0.1,  # Small series resistance

            # Breakdown voltage - related to holding voltage
            'BV': fitted['Vh'] * 1.05,  # Slightly higher than holding voltage

            # Breakdown current
            'IBV': fitted['I_offset'] * 0.1,

            # Parallel resistance for high voltage region
            'R_parallel': fitted['Ron'],

            # Junction capacitance (small for ESD)
            'CJO': 1e-12,

            # Transit time (small for ESD)
            'TT': 1e-12
        }

        return spice_params

    def _optimization_conversion(self, initial_params=None):
        """
        Optimization-based parameter conversion for exact matching

        Args:
            initial_params (dict): Initial guess for SPICE parameters

        Returns:
            dict: Optimized SPICE parameters
        """
        if initial_params is None:
            initial_params = self._direct_conversion()

        # Define parameter bounds for optimization
        bounds = [
            (1e-15, 1e-3),    # IS
            (0.5, 10.0),      # N
            (0.01, 1.0),      # RS
            (self.fitted_params['Vh']*0.8, self.fitted_params['Vh']*1.5),  # BV
            (1e-9, 1e-3),     # IBV
            (0.1, 100.0),     # R_parallel
        ]

        # Initial parameter vector
        x0 = [
            initial_params['IS'],
            initial_params['N'],
            initial_params['RS'],
            initial_params['BV'],
            initial_params['IBV'],
            initial_params['R_parallel']
        ]

        def objective(x):
            """Objective function for optimization"""
            spice_params = {
                'IS': x[0],
                'N': x[1],
                'RS': x[2],
                'BV': x[3],
                'IBV': x[4],
                'R_parallel': x[5],
                'CJO': 1e-12,
                'TT': 1e-12
            }

            try:
                spice_current = self.calculate_spice_current(self.voltage_range, spice_params)

                # Use log-scale error for better fitting across decades
                log_target = np.log10(np.maximum(self.target_current, 1e-15))
                log_spice = np.log10(np.maximum(spice_current, 1e-15))

                # Calculate weighted error (more weight on important regions)
                weights = np.ones_like(log_target)

                # Higher weight for trigger region
                trigger_mask = (self.voltage_range >= self.fitted_params['Vt1']*0.8) & \
                              (self.voltage_range <= self.fitted_params['Vh']*1.2)
                weights[trigger_mask] *= 3.0

                # Higher weight for snapback region
                snapback_mask = self.voltage_range >= self.fitted_params['Vh']
                weights[snapback_mask] *= 2.0

                error = np.sum(weights * (log_target - log_spice)**2)
                return error

            except Exception:
                return 1e10  # Large penalty for invalid parameters

        try:
            # Use differential evolution for global optimization
            result = differential_evolution(objective, bounds, seed=42, maxiter=100)

            if result.success:
                optimized_params = {
                    'IS': result.x[0],
                    'N': result.x[1],
                    'RS': result.x[2],
                    'BV': result.x[3],
                    'IBV': result.x[4],
                    'R_parallel': result.x[5],
                    'CJO': 1e-12,
                    'TT': 1e-12
                }

                # Store optimization results
                self.optimization_error = result.fun

                return optimized_params
            else:
                print("Warning: Optimization failed, using direct conversion")
                return initial_params

        except Exception as e:
            print("Warning: Optimization error ({}), using direct conversion".format(e))
            return initial_params

    def validate_conversion(self, spice_params, tolerance=0.1):
        """
        Validate the conversion by comparing fitted and SPICE model outputs

        Args:
            spice_params (dict): SPICE model parameters
            tolerance (float): Acceptable relative error (0.1 = 10%)

        Returns:
            dict: Validation results
        """
        if self.fitted_params is None or self.voltage_range is None:
            raise ValueError("Fitted model not set. Call set_fitted_model first.")

        # Calculate currents from both models
        fitted_current = self.calculate_fitted_current(self.voltage_range, self.fitted_params)
        spice_current = self.calculate_spice_current(self.voltage_range, spice_params)

        # Calculate errors with NaN protection
        relative_error = np.abs(fitted_current - spice_current) / (fitted_current + 1e-15)

        # Protect against log of zero or negative values
        fitted_log = np.log10(np.maximum(fitted_current, 1e-15))
        spice_log = np.log10(np.maximum(spice_current, 1e-15))
        log_error = np.abs(fitted_log - spice_log)

        # Remove any NaN or inf values
        valid_mask = np.isfinite(log_error) & np.isfinite(relative_error)
        if not np.any(valid_mask):
            # If all values are invalid, return default error values
            log_error = np.full_like(log_error, 1.0)
            relative_error = np.full_like(relative_error, 1.0)
            valid_mask = np.ones_like(log_error, dtype=bool)
        else:
            log_error = np.where(valid_mask, log_error, 1.0)
            relative_error = np.where(valid_mask, relative_error, 1.0)

        # Calculate statistics
        max_relative_error = np.max(relative_error)
        mean_relative_error = np.mean(relative_error)
        max_log_error = np.max(log_error)
        mean_log_error = np.mean(log_error)

        # Check if within tolerance
        within_tolerance = max_log_error < tolerance

        # Find problematic regions
        problem_mask = log_error > tolerance
        problem_voltages = self.voltage_range[problem_mask] if np.any(problem_mask) else []

        validation_results = {
            'within_tolerance': within_tolerance,
            'max_relative_error': max_relative_error,
            'mean_relative_error': mean_relative_error,
            'max_log_error': max_log_error,
            'mean_log_error': mean_log_error,
            'problem_voltages': problem_voltages,
            'fitted_current': fitted_current,
            'spice_current': spice_current,
            'relative_error': relative_error,
            'log_error': log_error
        }

        return validation_results

    def generate_optimized_spice_model(self, model_name="bjt_esd_optimized"):
        """
        Generate SPICE model with optimized parameters for exact matching

        Args:
            model_name (str): Name of the SPICE model

        Returns:
            str: SPICE model content
        """
        if self.fitted_params is None:
            raise ValueError("Fitted parameters not set. Call set_fitted_model first.")

        # Convert parameters using optimization
        spice_params = self.convert_to_spice_parameters(method='optimization')

        # Validate conversion
        validation = self.validate_conversion(spice_params)

        # Generate SPICE model content
        spice_content = """* Optimized BJT ESD Device SPICE Model
* Generated by BJT ESD Parameter Extractor with Parameter Optimization
* Fitted Model to SPICE Model Conversion with Validation

* Validation Results:
* Max Log Error: {:.4f}
* Mean Log Error: {:.4f}
* Within Tolerance: {}

* Original Fitted Parameters:
* I_leak = {:.6e} A
* Vt1 = {:.3f} V
* k = {:.3f}
* Ron = {:.3f} Ohm
* Vh = {:.3f} V
* I_offset = {:.6f} A
* Isb = {:.6f} A
* Vsb = {:.3f} V

* Optimized SPICE Parameters:
* IS = {:.6e} A
* N = {:.3f}
* RS = {:.6f} Ohm
* BV = {:.3f} V
* IBV = {:.6e} A
* R_parallel = {:.3f} Ohm

.subckt {} anode cathode
* Main ESD diode with optimized parameters
D_main anode n_main D_ESD_OPT
R_main n_main cathode {:.6f}

* Parallel resistance for high voltage region
R_parallel anode cathode {:.3f}

* Optimized diode model
.model D_ESD_OPT D(
+ IS={:.6e}
+ N={:.3f}
+ RS={:.6f}
+ BV={:.3f}
+ IBV={:.6e}
+ CJO=1e-12
+ TT=1e-12
+ )

.ends {}

* Test circuit for validation
.subckt test_validation
Vin n1 0 DC 0
X_esd n1 0 {}
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.ends test_validation

.end
""".format(
            validation['max_log_error'],
            validation['mean_log_error'],
            validation['within_tolerance'],
            self.fitted_params['I_leak'],
            self.fitted_params['Vt1'],
            self.fitted_params['k'],
            self.fitted_params['Ron'],
            self.fitted_params['Vh'],
            self.fitted_params['I_offset'],
            self.fitted_params['Isb'],
            self.fitted_params['Vsb'],
            spice_params['IS'],
            spice_params['N'],
            spice_params['RS'],
            spice_params['BV'],
            spice_params['IBV'],
            spice_params['R_parallel'],
            model_name,
            spice_params['RS'],
            spice_params['R_parallel'],
            spice_params['IS'],
            spice_params['N'],
            spice_params['RS'],
            spice_params['BV'],
            spice_params['IBV'],
            model_name,
            model_name
        )

        return spice_content, validation
