"""
HSPICE Model Comparison Dialog for BJT ESD Parameter Extractor
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QPushButton, QGroupBox, QCheckBox, 
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QProgressBar, QTextEdit, QTabWidget, QWidget,
                             QComboBox, QSpinBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

class ComparisonWorker(QThread):
    """Worker thread for model comparison"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    model_completed = pyqtSignal(str, dict)
    comparison_completed = pyqtSignal(list)
    
    def __init__(self, file_paths, options):
        super().__init__()
        self.file_paths = file_paths
        self.options = options
        self.results = []
        
    def run(self):
        """Run comparison simulations"""
        try:
            from simulation.hspice_runner import HspiceRunner
            
            hspice_runner = HspiceRunner()
            
            if not hspice_runner.check_hspice_installation():
                self.status_updated.emit("Error: HSPICE not found")
                return
            
            total_files = len(self.file_paths)
            
            for i, file_path in enumerate(self.file_paths):
                try:
                    self.status_updated.emit("Simulating: {}".format(os.path.basename(file_path)))
                    
                    # Run simulation
                    base_name = os.path.splitext(os.path.basename(file_path))[0]
                    output_file = "{}_comparison.lis".format(base_name)
                    
                    # Custom simulation call would go here
                    # For now, simulate with dummy data
                    import numpy as np
                    voltage = np.linspace(0, 20, 100)
                    current = np.abs(np.random.random(100) * 1e-6 * (i + 1))
                    
                    result = {
                        'voltage': voltage,
                        'current': current,
                        'file_name': os.path.basename(file_path),
                        'model_name': base_name,
                        'output_file': output_file
                    }
                    
                    self.results.append(result)
                    self.model_completed.emit(file_path, result)
                    
                    # Update progress
                    progress = int((i + 1) / total_files * 100)
                    self.progress_updated.emit(progress)
                    
                except Exception as e:
                    self.status_updated.emit("Error simulating {}: {}".format(file_path, str(e)))
                    
            self.status_updated.emit("Comparison completed!")
            self.comparison_completed.emit(self.results)
            
        except Exception as e:
            self.status_updated.emit("Comparison failed: {}".format(str(e)))

class HspiceComparisonDialog(QDialog):
    """Dialog for comparing multiple HSPICE models"""
    
    def __init__(self, file_paths, parent=None):
        super().__init__(parent)
        self.file_paths = file_paths
        self.comparison_results = []
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("HSPICE Model Comparison")
        self.setGeometry(200, 200, 900, 700)
        
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_files_tab()
        self.create_options_tab()
        self.create_results_tab()
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready to start comparison")
        layout.addWidget(self.status_label)
        
        # Buttons
        self.create_buttons(layout)
        
    def create_files_tab(self):
        """Create files selection tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Title
        title = QLabel("Model Files for Comparison")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title)
        
        # Files table
        self.files_table = QTableWidget()
        self.files_table.setColumnCount(4)
        self.files_table.setHorizontalHeaderLabels(["Include", "File Name", "Model Name", "Size"])
        self.files_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        # Populate files table
        self.populate_files_table()
        layout.addWidget(self.files_table)
        
        # File operations
        file_ops_layout = QHBoxLayout()
        
        select_all_button = QPushButton("Select All")
        select_all_button.clicked.connect(self.select_all_files)
        file_ops_layout.addWidget(select_all_button)
        
        select_none_button = QPushButton("Select None")
        select_none_button.clicked.connect(self.select_no_files)
        file_ops_layout.addWidget(select_none_button)
        
        file_ops_layout.addStretch()
        layout.addLayout(file_ops_layout)
        
        self.tab_widget.addTab(tab, "Files")
        
    def create_options_tab(self):
        """Create comparison options tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Simulation options
        sim_group = QGroupBox("Simulation Options")
        sim_layout = QGridLayout(sim_group)
        
        sim_layout.addWidget(QLabel("Voltage Start (V):"), 0, 0)
        self.voltage_start_spin = QDoubleSpinBox()
        self.voltage_start_spin.setRange(-100, 100)
        self.voltage_start_spin.setValue(0)
        sim_layout.addWidget(self.voltage_start_spin, 0, 1)
        
        sim_layout.addWidget(QLabel("Voltage Stop (V):"), 1, 0)
        self.voltage_stop_spin = QDoubleSpinBox()
        self.voltage_stop_spin.setRange(-100, 100)
        self.voltage_stop_spin.setValue(20)
        sim_layout.addWidget(self.voltage_stop_spin, 1, 1)
        
        sim_layout.addWidget(QLabel("Voltage Step (V):"), 2, 0)
        self.voltage_step_spin = QDoubleSpinBox()
        self.voltage_step_spin.setRange(0.001, 10)
        self.voltage_step_spin.setValue(0.1)
        sim_layout.addWidget(self.voltage_step_spin, 2, 1)
        
        layout.addWidget(sim_group)
        
        # Comparison options
        comp_group = QGroupBox("Comparison Options")
        comp_layout = QVBoxLayout(comp_group)
        
        self.plot_results_check = QCheckBox("Plot comparison results")
        self.plot_results_check.setChecked(True)
        comp_layout.addWidget(self.plot_results_check)
        
        self.save_results_check = QCheckBox("Save comparison data")
        self.save_results_check.setChecked(True)
        comp_layout.addWidget(self.save_results_check)
        
        self.generate_report_check = QCheckBox("Generate comparison report")
        self.generate_report_check.setChecked(True)
        comp_layout.addWidget(self.generate_report_check)
        
        layout.addWidget(comp_group)
        
        # Analysis options
        analysis_group = QGroupBox("Analysis Options")
        analysis_layout = QGridLayout(analysis_group)
        
        analysis_layout.addWidget(QLabel("Error Metric:"), 0, 0)
        self.error_metric_combo = QComboBox()
        self.error_metric_combo.addItems(["Log Error", "Relative Error", "Absolute Error"])
        analysis_layout.addWidget(self.error_metric_combo, 0, 1)
        
        analysis_layout.addWidget(QLabel("Reference Model:"), 1, 0)
        self.reference_combo = QComboBox()
        self.reference_combo.addItem("First Model")
        for file_path in self.file_paths:
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            self.reference_combo.addItem(base_name)
        analysis_layout.addWidget(self.reference_combo, 1, 1)
        
        layout.addWidget(analysis_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Options")
        
    def create_results_tab(self):
        """Create results display tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            "Model", "Status", "Data Points", "Max Current", "Notes"
        ])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)
        
        # Results summary
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setMaximumHeight(200)
        layout.addWidget(self.results_text)
        
        self.tab_widget.addTab(tab, "Results")
        
    def create_buttons(self, parent_layout):
        """Create dialog buttons"""
        button_layout = QHBoxLayout()
        
        # Start comparison button
        self.start_button = QPushButton("Start Comparison")
        self.start_button.clicked.connect(self.start_comparison)
        button_layout.addWidget(self.start_button)
        
        button_layout.addStretch()
        
        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)
        
        parent_layout.addLayout(button_layout)
        
    def populate_files_table(self):
        """Populate the files table"""
        self.files_table.setRowCount(len(self.file_paths))
        
        for row, file_path in enumerate(self.file_paths):
            # Include checkbox
            include_check = QCheckBox()
            include_check.setChecked(True)
            self.files_table.setCellWidget(row, 0, include_check)
            
            # File name
            file_name = os.path.basename(file_path)
            self.files_table.setItem(row, 1, QTableWidgetItem(file_name))
            
            # Model name
            model_name = os.path.splitext(file_name)[0]
            self.files_table.setItem(row, 2, QTableWidgetItem(model_name))
            
            # File size
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                size_str = "{} bytes".format(size)
            else:
                size_str = "File not found"
            self.files_table.setItem(row, 3, QTableWidgetItem(size_str))
            
    def select_all_files(self):
        """Select all files for comparison"""
        for row in range(self.files_table.rowCount()):
            checkbox = self.files_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)
                
    def select_no_files(self):
        """Deselect all files"""
        for row in range(self.files_table.rowCount()):
            checkbox = self.files_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)
                
    def get_selected_files(self):
        """Get list of selected files"""
        selected_files = []
        for row in range(self.files_table.rowCount()):
            checkbox = self.files_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_files.append(self.file_paths[row])
        return selected_files
        
    def start_comparison(self):
        """Start the comparison process"""
        selected_files = self.get_selected_files()
        
        if len(selected_files) < 2:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                "Selection Required",
                "Please select at least 2 files for comparison."
            )
            return
            
        # Get options
        options = {
            'voltage_range': (self.voltage_start_spin.value(), self.voltage_stop_spin.value()),
            'voltage_step': self.voltage_step_spin.value(),
            'plot_results': self.plot_results_check.isChecked(),
            'save_results': self.save_results_check.isChecked(),
            'generate_report': self.generate_report_check.isChecked(),
            'error_metric': self.error_metric_combo.currentText(),
            'reference_model': self.reference_combo.currentText()
        }
        
        # Disable start button and show progress
        self.start_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Start worker thread
        self.worker = ComparisonWorker(selected_files, options)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.model_completed.connect(self.on_model_completed)
        self.worker.comparison_completed.connect(self.on_comparison_completed)
        self.worker.start()
        
    def on_model_completed(self, file_path, result):
        """Handle individual model completion"""
        # Update results table
        row = self.results_table.rowCount()
        self.results_table.setRowCount(row + 1)
        
        self.results_table.setItem(row, 0, QTableWidgetItem(result['model_name']))
        self.results_table.setItem(row, 1, QTableWidgetItem("✓ Complete"))
        self.results_table.setItem(row, 2, QTableWidgetItem(str(len(result['voltage']))))
        self.results_table.setItem(row, 3, QTableWidgetItem("{:.3e}".format(max(result['current']))))
        self.results_table.setItem(row, 4, QTableWidgetItem("Simulation successful"))
        
    def on_comparison_completed(self, results):
        """Handle comparison completion"""
        self.comparison_results = results
        
        # Update results summary
        summary = "HSPICE Model Comparison Results\n"
        summary += "=" * 40 + "\n\n"
        summary += "Successfully compared {} models:\n\n".format(len(results))
        
        for result in results:
            summary += "• {}: {} data points\n".format(
                result['model_name'], len(result['voltage'])
            )
            
        summary += "\nComparison completed successfully!\n"
        summary += "Results are available in the main application."
        
        self.results_text.setText(summary)
        
        # Re-enable controls
        self.start_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        # Switch to results tab
        self.tab_widget.setCurrentIndex(2)
        
    def get_comparison_results(self):
        """Get comparison results"""
        return self.comparison_results
