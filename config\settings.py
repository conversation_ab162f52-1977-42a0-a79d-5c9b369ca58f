"""
Application configuration settings
"""

import os

# Application settings
APP_NAME = "BJT ESD Parameter Extractor"
APP_VERSION = "1.0.0"

# File paths
DEFAULT_DATA_PATH = "data"
DEFAULT_OUTPUT_PATH = "output"
HSPICE_PATH = "hspice"  # Adjust based on your HSPICE installation

# Plot settings
PLOT_DPI = 100
PLOT_FIGSIZE = (10, 6)
PLOT_STYLE = 'seaborn-v0_8'

# Default parameter ranges
PARAMETER_RANGES = {
    'I_leak': {'min': 1e-12, 'max': 1e-6, 'default': 1.9981e-07, 'unit': 'A'},
    'Vt1': {'min': 1.0, 'max': 50.0, 'default': 12.645, 'unit': 'V'},
    'k': {'min': 0.1, 'max': 10.0, 'default': 3.0, 'unit': ''},
    'Ron': {'min': 0.1, 'max': 100.0, 'default': 1.962, 'unit': 'Ω'},
    'Vh': {'min': 1.0, 'max': 50.0, 'default': 13.876, 'unit': 'V'},
    'I_offset': {'min': 0.001, 'max': 1.0, 'default': 0.05, 'unit': 'A'},
    'Isb': {'min': 0.001, 'max': 1.0, 'default': 0.03997, 'unit': 'A'},
    'Vsb': {'min': 1.0, 'max': 50.0, 'default': 13.82, 'unit': 'V'}
}

# GUI settings
WINDOW_SIZE = (1200, 800)
PLOT_HEIGHT = 400
PARAMETER_PANEL_HEIGHT = 350

# Data processing settings
SMOOTHING_WINDOW = 5
DERIVATIVE_THRESHOLD = 0.1
