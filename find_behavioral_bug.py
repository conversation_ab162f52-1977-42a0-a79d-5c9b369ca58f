#!/usr/bin/env python3
"""
Find the Bug in Behavioral Model
Deep dive into why behavioral model differs from fitted model
"""

import sys
import os
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def compare_equations_step_by_step():
    """Compare equations step by step"""
    print("STEP-BY-STEP EQUATION COMPARISON")
    print("=" * 60)
    
    # Load real parameters
    try:
        from data.data_loader import DataLoader
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        extractor = ImprovedParameterExtractor()
        extractor.debug = False
        params = extractor.extract_parameters(data)
        
    except:
        params = {
            'I_leak': 9.317230e-07,
            'Vt1': 9.083848,
            'k': 2.000000,
            'Ron': 1.140351,
            'Vh': 14.752984,
            'I_offset': 1.000000e-06,
            'Isb': 5.693839e-01,
            'Vsb': 14.831887
        }
    
    print("Parameters:")
    for key, value in params.items():
        if 'I_' in key or 'Isb' in key:
            print(f"  {key}: {value:.6e}")
        else:
            print(f"  {key}: {value:.6f}")
    
    # Test specific voltages in each region
    test_cases = [
        {"V": 5.0, "region": "leakage", "expected_region": "< Vt1"},
        {"V": 10.0, "region": "trigger", "expected_region": "Vt1 to Vh"},
        {"V": 16.0, "region": "snapback", "expected_region": "> Vh"}
    ]
    
    print(f"\nRegion boundaries:")
    print(f"  Vt1 = {params['Vt1']:.3f}V")
    print(f"  Vh  = {params['Vh']:.3f}V")
    print(f"  Vsb = {params['Vsb']:.3f}V")
    
    for test in test_cases:
        v = test["V"]
        print(f"\n" + "-"*50)
        print(f"Testing V = {v}V ({test['region']} region)")
        print(f"Expected: {test['expected_region']}")
        
        # Fitted model calculation
        fitted_current = calculate_fitted_detailed(v, params)
        
        # Original behavioral calculation  
        orig_behavioral_current = calculate_original_behavioral_detailed(v, params)
        
        # Fixed behavioral calculation
        fixed_behavioral_current = calculate_fixed_behavioral_detailed(v, params)
        
        print(f"\nResults:")
        print(f"  Fitted:           {fitted_current:.6e}")
        print(f"  Orig Behavioral:  {orig_behavioral_current:.6e}")
        print(f"  Fixed Behavioral: {fixed_behavioral_current:.6e}")
        
        # Check differences
        diff1 = abs(fitted_current - orig_behavioral_current)
        diff2 = abs(fitted_current - fixed_behavioral_current)
        
        print(f"\nDifferences:")
        print(f"  |Fitted - Orig|:  {diff1:.6e}")
        print(f"  |Fitted - Fixed|: {diff2:.6e}")
        
        if diff1 > 1e-15:
            print(f"  ❌ Original behavioral differs!")
        else:
            print(f"  ✅ Original behavioral matches")
            
        if diff2 > 1e-15:
            print(f"  ❌ Fixed behavioral differs!")
        else:
            print(f"  ✅ Fixed behavioral matches")

def calculate_fitted_detailed(v, params):
    """Calculate fitted model with detailed logging"""
    print(f"\nFITTED MODEL for V={v}:")
    
    if v < params['Vt1']:
        print(f"  Region: Leakage (V < Vt1={params['Vt1']:.3f})")
        print(f"  Formula: I_leak * exp(V/1.0)")
        print(f"  Calculation: {params['I_leak']:.6e} * exp({v}/1.0)")
        result = params['I_leak'] * np.exp(v / 1.0)
        print(f"  Result: {result:.6e}")
        return result
        
    elif v < params['Vh']:
        print(f"  Region: Trigger (Vt1 < V < Vh={params['Vh']:.3f})")
        print(f"  Formula: I_leak * exp(k * (V - Vt1) / Vt1)")
        exp_arg = params['k'] * (v - params['Vt1']) / params['Vt1']
        print(f"  Exp argument: {params['k']:.3f} * ({v} - {params['Vt1']:.3f}) / {params['Vt1']:.3f} = {exp_arg:.6f}")
        result = params['I_leak'] * np.exp(exp_arg)
        print(f"  Calculation: {params['I_leak']:.6e} * exp({exp_arg:.6f}) = {result:.6e}")
        return result
        
    else:
        print(f"  Region: Snapback (V > Vh={params['Vh']:.3f})")
        print(f"  Formula: I_offset + (V - Vsb)/Ron + Isb*exp(-(V - Vsb))")
        
        linear_term = (v - params['Vsb']) / params['Ron'] if params['Ron'] > 0 else 0
        exp_arg = -(v - params['Vsb'])
        exp_term = params['Isb'] * np.exp(exp_arg) if v > params['Vsb'] else params['Isb']
        
        print(f"  Linear term: ({v} - {params['Vsb']:.3f}) / {params['Ron']:.3f} = {linear_term:.6f}")
        print(f"  Exp argument: -({v} - {params['Vsb']:.3f}) = {exp_arg:.6f}")
        print(f"  Exp term: {params['Isb']:.6e} * exp({exp_arg:.6f}) = {exp_term:.6e}")
        
        result = params['I_offset'] + linear_term + exp_term
        print(f"  Total: {params['I_offset']:.6e} + {linear_term:.6f} + {exp_term:.6e} = {result:.6e}")
        return result

def calculate_original_behavioral_detailed(v, params):
    """Calculate original SPICE behavioral with detailed logging"""
    print(f"\nORIGINAL SPICE BEHAVIORAL for V={v}:")
    
    if v < 0:
        print(f"  Region: Negative voltage")
        result = params['I_leak'] * 1e-3
        print(f"  Result: {result:.6e}")
        return result
        
    elif v < params['Vt1']:
        print(f"  Region: Leakage (V < Vt1={params['Vt1']:.3f})")
        print(f"  Formula: I_leak * exp(V/1.0)")
        result = params['I_leak'] * np.exp(v / 1.0)
        print(f"  Result: {result:.6e}")
        return result
        
    elif v < params['Vh']:
        print(f"  Region: Trigger (Vt1 < V < Vh={params['Vh']:.3f})")
        print(f"  Formula: I_leak * exp(k * (V - Vt1) / Vt1)")
        exp_arg = params['k'] * (v - params['Vt1']) / params['Vt1']
        result = params['I_leak'] * np.exp(exp_arg)
        print(f"  Result: {result:.6e}")
        return result
        
    else:
        print(f"  Region: Snapback (V > Vh={params['Vh']:.3f})")
        print(f"  Formula: I_offset + (V - Vsb)/Ron + Isb*exp(-(V - Vsb))")
        
        linear_term = (v - params['Vsb']) / params['Ron']
        exp_term = params['Isb'] * np.exp(-(v - params['Vsb']))
        
        result = params['I_offset'] + linear_term + exp_term
        print(f"  Result: {result:.6e}")
        return result

def calculate_fixed_behavioral_detailed(v, params):
    """Calculate fixed SPICE behavioral with detailed logging"""
    print(f"\nFIXED SPICE BEHAVIORAL for V={v}:")
    
    if v < 0:
        print(f"  Region: Negative voltage")
        result = params['I_leak'] * 1e-3
        print(f"  Result: {result:.6e}")
        return result
        
    elif v < params['Vt1']:
        print(f"  Region: Leakage (V < Vt1={params['Vt1']:.3f})")
        print(f"  Formula: I_leak * exp(V/1.0)")
        result = params['I_leak'] * np.exp(v / 1.0)
        print(f"  Result: {result:.6e}")
        return result
        
    elif v < params['Vh']:
        print(f"  Region: Trigger (Vt1 < V < Vh={params['Vh']:.3f})")
        print(f"  Formula: I_leak * exp(k * (V - Vt1) / Vt1)")
        exp_arg = params['k'] * (v - params['Vt1']) / params['Vt1']
        result = params['I_leak'] * np.exp(exp_arg)
        print(f"  Result: {result:.6e}")
        return result
        
    else:
        print(f"  Region: Snapback (V > Vh={params['Vh']:.3f})")
        print(f"  Formula: I_offset + (V - Vsb)/Ron + Isb*exp(-(V - Vsb))")
        
        linear_term = (v - params['Vsb']) / params['Ron']
        exp_term = params['Isb'] * np.exp(-(v - params['Vsb']))
        
        result = params['I_offset'] + linear_term + exp_term
        print(f"  Result: {result:.6e}")
        return result

def check_source_code_differences():
    """Check actual source code for differences"""
    print("\n" + "="*60)
    print("SOURCE CODE COMPARISON")
    print("="*60)
    
    print("\nChecking parameter_extractor.py calculate_model_current:")
    try:
        with open("data/parameter_extractor.py", "r") as f:
            lines = f.readlines()
            
        # Find the calculate_model_current method
        start_line = None
        for i, line in enumerate(lines):
            if "def calculate_model_current" in line:
                start_line = i
                break
                
        if start_line:
            print("Found calculate_model_current method:")
            for i in range(start_line, min(start_line + 30, len(lines))):
                print(f"  {i+1:3d}: {lines[i].rstrip()}")
        else:
            print("Method not found!")
            
    except Exception as e:
        print(f"Error reading file: {e}")
    
    print("\nChecking improved_parameter_extractor.py calculate_model_current:")
    try:
        with open("data/improved_parameter_extractor.py", "r") as f:
            lines = f.readlines()
            
        # Find the calculate_model_current method
        start_line = None
        for i, line in enumerate(lines):
            if "def calculate_model_current" in line:
                start_line = i
                break
                
        if start_line:
            print("Found calculate_model_current method:")
            for i in range(start_line, min(start_line + 30, len(lines))):
                print(f"  {i+1:3d}: {lines[i].rstrip()}")
        else:
            print("Method not found!")
            
    except Exception as e:
        print(f"Error reading file: {e}")

def find_the_actual_bug():
    """Try to find the actual source of the error"""
    print("\n" + "="*60)
    print("FINDING THE ACTUAL BUG")
    print("="*60)
    
    # Test with the actual extractor objects
    try:
        from data.parameter_extractor import ParameterExtractor
        from data.improved_parameter_extractor import ImprovedParameterExtractor
        from models.fixed_spice_generator import FixedSpiceGenerator
        
        # Load real data
        from data.data_loader import DataLoader
        loader = DataLoader()
        data = loader.load_csv("1.csv")
        
        # Extract with improved extractor
        improved = ImprovedParameterExtractor()
        improved.debug = False
        params = improved.extract_parameters(data)
        
        print("Using actual extractor objects:")
        
        # Test voltage
        test_v = np.array([10.0])
        
        # Calculate with improved extractor
        improved_current = improved.calculate_model_current(test_v, params)
        print(f"Improved extractor result: {improved_current[0]:.6e}")
        
        # Calculate with original extractor
        original = ParameterExtractor()
        original_current = original.calculate_model_current(test_v, params)
        print(f"Original extractor result: {original_current[0]:.6e}")
        
        # Calculate with fixed generator
        generator = FixedSpiceGenerator()
        fixed_current = generator.calculate_fitted_current(test_v, params)
        print(f"Fixed generator result: {fixed_current[0]:.6e}")
        
        # Check differences
        diff1 = abs(improved_current[0] - original_current[0])
        diff2 = abs(improved_current[0] - fixed_current[0])
        
        print(f"\nDifferences:")
        print(f"  |Improved - Original|: {diff1:.6e}")
        print(f"  |Improved - Fixed|:    {diff2:.6e}")
        
        if diff1 > 1e-15:
            print("❌ Original extractor differs from improved!")
        if diff2 > 1e-15:
            print("❌ Fixed generator differs from improved!")
            
        # The issue might be in which extractor is being used
        print(f"\n🔍 HYPOTHESIS:")
        print("The error might come from using different extractors")
        print("or different equation implementations.")
        
    except Exception as e:
        print(f"Error in actual object test: {e}")

def main():
    """Main debugging function"""
    print("BEHAVIORAL MODEL BUG HUNT")
    print("=" * 60)
    print("Finding why behavioral models differ from fitted model")
    
    # Step by step comparison
    compare_equations_step_by_step()
    
    # Check source code
    check_source_code_differences()
    
    # Find actual bug
    find_the_actual_bug()
    
    print("\n" + "="*60)
    print("CONCLUSIONS")
    print("="*60)
    
    print("\n🔍 FINDINGS:")
    print("1. Behavioral models should be mathematically identical")
    print("2. Small differences (1e-4) suggest implementation variations")
    print("3. Need to check which extractor is actually being used")
    print("4. May be floating point precision or region boundary issues")
    
    print("\n🔧 DEBUGGING ACTIONS:")
    print("1. Check if improved vs original extractor is being used")
    print("2. Verify region boundary calculations")
    print("3. Check for floating point precision issues")
    print("4. Test with exact same parameter values")
    
    return True

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
