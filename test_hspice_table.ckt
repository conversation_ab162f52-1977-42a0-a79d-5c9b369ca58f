* BJT ESD Device HSPICE Model
* Generated: 2025-06-09 08:49:17
* Model type: table
* HSPICE Compatible - Fixed syntax issues
*
* FITTED MODEL PARAMETERS:
* I_leak = 9.317230e-07 A
* Vt1    = 9.083848 V  
* k      = 2.000000
* Ron    = 1.140351 Ohm
* Vh     = 14.752984 V
* I_offset = 0.000001 A
* Isb    = 0.569384 A
* Vsb    = 14.831887 V
*
* USAGE: This model uses HSPICE-compatible syntax
* No behavioral expressions that might cause syntax errors
*


* BJT ESD Table Model - HSPICE Compatible
* Uses table lookup for exact I-V matching

.subckt bjt_esd_table anode cathode

* Table-based current source
G_table anode cathode TABLE {V(anode,cathode)} = (
+ (0.0, 9.317230e-07)
+ (1.0, 2.532686e-06)
+ (2.0, 6.884553e-06)
+ (3.0, 1.871416e-05)
+ (4.0, 5.087035e-05)
+ (5.0, 1.382799e-04)
+ (6.0, 3.758839e-04)
+ (7.0, 1.021758e-03)
+ (8.0, 2.777427e-03)
+ (9.0, 7.549829e-03)
+ (10.0, 1.139957e-06)
+ (11.0, 1.420717e-06)
+ (12.0, 1.770625e-06)
+ (13.0, 2.206712e-06)
+ (14.0, 2.750202e-06)
+ (15.0, 6.286997e-01)
+ (16.0, 1.201398e+00)
+ (17.0, 1.966403e+00)
+ (18.0, 2.802153e+00)
+ (19.0, 3.663930e+00)
+ (20.0, 4.535281e+00)
+ )

.ends bjt_esd_table


* Test circuit
Vin n_anode 0 DC 0
X_esd n_anode 0 bjt_esd_table

* Analysis
.dc Vin 0 20 0.1
.print dc V(n_anode) I(Vin)
.probe dc V(n_anode) I(Vin)

* HSPICE options for better convergence
.option post=2
.option accurate
.option gmin=1e-15
.option abstol=1e-15
.option reltol=1e-6
.option itl1=500
.option itl2=200

.end
