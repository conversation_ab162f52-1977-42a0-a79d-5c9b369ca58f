#!/usr/bin/env python3
"""
Generate accurate HSPICE model manually
"""

import numpy as np
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def bjt_esd_current_equation(voltage, I_leak=9.161209e-11, Vt1=3.841, k=7.812, 
                           Ron=1.213, Vh=14.011, I_offset=0.152575, 
                           Isb=0.292588, Vsb=14.661):
    """
    BJT ESD current equation
    """
    V = np.asarray(voltage)
    I = np.zeros_like(V)
    
    # Region 1: Leakage region (V < Vt1)
    mask1 = V < Vt1
    I[mask1] = I_leak * np.exp(V[mask1] / Vt1)
    
    # Region 2: Trigger region (Vt1 <= V < Vh)
    mask2 = (V >= Vt1) & (V < Vh)
    I[mask2] = I_leak * np.exp(k * (V[mask2] - Vt1) / Vt1)
    
    # Region 3: Snapback region (V >= Vh)
    mask3 = V >= Vh
    I[mask3] = I_offset + (V[mask3] - Vsb) / Ron + Isb * np.exp(-(V[mask3] - Vsb))
    
    return I

def generate_accurate_hspice_model():
    """Generate accurate HSPICE model"""
    
    # Use fitted parameters from the experimental data
    params = {
        'I_leak': 9.161209e-11,
        'Vt1': 3.841,
        'k': 7.812,
        'Ron': 1.213,
        'Vh': 14.011,
        'I_offset': 0.152575,
        'Isb': 0.292588,
        'Vsb': 14.661
    }
    
    print("Generating accurate HSPICE model with fitted parameters:")
    for key, value in params.items():
        print(f"  {key}: {value:.6e}")
    
    # Generate voltage points with adaptive spacing
    Vt1 = params['Vt1']
    Vh = params['Vh']
    
    v_points = []
    
    # Dense points in leakage region (0 to Vt1)
    v_points.extend(np.linspace(0, Vt1*0.9, 15))
    
    # Very dense points around trigger voltage
    v_points.extend(np.linspace(Vt1*0.9, Vt1*1.1, 8))
    
    # Dense points in trigger region (Vt1 to Vh)
    v_points.extend(np.linspace(Vt1*1.1, Vh*0.95, 12))
    
    # Very dense points around holding voltage
    v_points.extend(np.linspace(Vh*0.95, Vh*1.05, 8))
    
    # Points in snapback region (Vh to 20V)
    v_points.extend(np.linspace(Vh*1.05, 20.0, 15))
    
    # Remove duplicates and sort
    v_points = sorted(list(set(v_points)))
    v_array = np.array(v_points)
    
    # Calculate corresponding currents
    i_array = bjt_esd_current_equation(v_array, **params)
    
    print(f"\nGenerated {len(v_array)} voltage-current pairs")
    print("Sample points:")
    for i in range(0, len(v_array), len(v_array)//10):
        print(f"  V={v_array[i]:.3f}V, I={i_array[i]:.3e}A")
    
    # Generate HSPICE PWL current source
    pwl_pairs = []
    for v, i in zip(v_array, i_array):
        pwl_pairs.append(f"({v:.6f},{i:.6e})")
    
    # Create the complete model
    model_content = f"""* BJT ESD Device SPICE Model (.ckt file) - ACCURATE VERSION
* Generated by BJT ESD Parameter Extractor
* Date: 2025-06-06

* Model Parameters (Fitted from experimental data):
* I_leak = {params['I_leak']:.6e} A (Leakage Current)
* Vt1 = {params['Vt1']:.3f} V (Trigger Voltage)
* k = {params['k']:.3f} (Exponential Factor)
* Ron = {params['Ron']:.3f} Ohm (On Resistance)
* Vh = {params['Vh']:.3f} V (Holding Voltage)
* I_offset = {params['I_offset']:.6f} A (Current Offset)
* Isb = {params['Isb']:.6f} A (Snapback Current)
* Vsb = {params['Vsb']:.3f} V (Snapback Voltage)

* BJT ESD Device Subcircuit - Accurate PWL Model
.subckt bjt_esd_device anode cathode

* Accurate BJT ESD model using piecewise linear current source
* This model closely approximates the mathematical BJT ESD equation
* Current equation with three regions:
* Region 1 (Leakage): V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2 (Trigger): Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)
* Region 3 (Snapback): V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

* Voltage-controlled current source with detailed PWL table ({len(v_array)} points)
Gesd anode cathode PWL(1) V(anode,cathode) {' '.join(pwl_pairs)}

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit
"""
    
    # Save to file
    with open('accurate_bjt_esd_model.ckt', 'w') as f:
        f.write(model_content)
    
    print(f"\n✓ Accurate HSPICE model saved to: accurate_bjt_esd_model.ckt")
    print(f"Model file size: {len(model_content)} characters")
    
    # Generate corresponding netlist
    netlist_content = f"""* BJT ESD Device Simulation Netlist (.sp file) - ACCURATE VERSION
* Generated by BJT ESD Parameter Extractor
* Date: 2025-06-06

.title BJT ESD Device I-V Characteristics - Accurate Model

.include 'accurate_bjt_esd_model.ckt'

* Voltage source for DC sweep
Vin n1 0 DC 0

* Instantiate ESD device from included model
Xesd n1 0 bjt_esd_device

* DC Analysis (100 points from 0 to 20V)
.dc Vin 0 20 0.2

* Output commands
.print dc V(n1) I(Vin)
.probe dc V(n1) I(Vin)

* Simulation options
.option post=2
.option gmin=1e-15
.option accurate
.option runlvl=5

.end
"""
    
    with open('accurate_bjt_esd_simulation.sp', 'w') as f:
        f.write(netlist_content)
    
    print(f"✓ Accurate HSPICE netlist saved to: accurate_bjt_esd_simulation.sp")
    
    return True

if __name__ == "__main__":
    print("BJT ESD Parameter Extractor - Accurate Model Generator")
    print("=" * 60)
    
    try:
        success = generate_accurate_hspice_model()
        if success:
            print("\n" + "=" * 60)
            print("✓ Accurate HSPICE model generation completed successfully!")
            print("\nGenerated files:")
            for filename in ["accurate_bjt_esd_model.ckt", "accurate_bjt_esd_simulation.sp"]:
                if os.path.exists(filename):
                    print(f"  {filename} - {os.path.getsize(filename)} bytes")
            
            print("\nTo test the model:")
            print("  hspice accurate_bjt_esd_simulation.sp -o accurate_bjt_esd_simulation.lis")
        else:
            print("✗ Model generation failed")
            sys.exit(1)
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
