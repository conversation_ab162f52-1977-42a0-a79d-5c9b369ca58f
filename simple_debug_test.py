#!/usr/bin/env python3
"""
Simple debug test for HSPICE parsing
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_hspice_parsing():
    """Test HSPICE parsing with debug output"""
    try:
        from utils.hspice_interface import HSPICEInterface
        
        print("Testing HSPICE parsing with debug...")
        
        hspice = HSPICEInterface()
        
        if os.path.exists('bjt_esd_simulation.lis'):
            print("Parsing bjt_esd_simulation.lis...")
            result = hspice._parse_hspice_output_file('bjt_esd_simulation.lis')
            
            if result:
                voltage, current = result
                print(f"\nParsed {len(voltage)} data points")
                
                # Manual analysis
                print(f"Voltage range: {voltage.min():.6f} to {voltage.max():.6f} V")
                print(f"Current range: {current.min():.6e} to {current.max():.6e} A")
                
                # Check voltage steps
                if len(voltage) > 1:
                    voltage_steps = np.diff(voltage)
                    print(f"Voltage steps - min: {voltage_steps.min():.6f}, max: {voltage_steps.max():.6f}, avg: {voltage_steps.mean():.6f}")
                
                # Show sample points
                print(f"\nSample data points:")
                indices = [0, len(voltage)//4, len(voltage)//2, 3*len(voltage)//4, -1]
                for i in indices:
                    if 0 <= i < len(voltage):
                        print(f"  [{i:3d}] V={voltage[i]:8.6f}V, I={current[i]:12.6e}A")
                
                # Check for anomalies
                if len(current) > 1:
                    current_diff = np.diff(current)
                    max_jump_idx = np.argmax(np.abs(current_diff))
                    max_jump = current_diff[max_jump_idx]
                    print(f"\nLargest current change: {max_jump:.6e}A at index {max_jump_idx}")
                    print(f"  From V={voltage[max_jump_idx]:.6f}V, I={current[max_jump_idx]:.6e}A")
                    print(f"  To   V={voltage[max_jump_idx+1]:.6f}V, I={current[max_jump_idx+1]:.6e}A")
                
                return voltage, current
            else:
                print("Failed to parse HSPICE output")
                return None
        else:
            print("No bjt_esd_simulation.lis file found")
            return None
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_fitted_model():
    """Test fitted model for comparison"""
    try:
        from utils.data_loader import DataLoader
        from models.bjt_esd_model import BJTESDModel
        
        if not os.path.exists('1.csv'):
            print("No experimental data file found")
            return None
        
        # Load and fit data
        loader = DataLoader()
        voltage_exp, current_exp, _ = loader.load_data('1.csv')
        
        model = BJTESDModel()
        fitted_params = model.fit_parameters(voltage_exp, current_exp)
        
        # Generate fitted curve
        voltage_fitted = np.linspace(0, 20, 100)
        current_fitted = model.current_equation(voltage_fitted, **fitted_params)
        
        print(f"\nFitted model:")
        print(f"R² = {fitted_params.get('r_squared', 0):.6f}")
        print(f"Sample fitted points:")
        for i in [0, 25, 50, 75, 99]:
            print(f"  V={voltage_fitted[i]:6.3f}V, I={current_fitted[i]:8.6e}A")
        
        return voltage_fitted, current_fitted
        
    except Exception as e:
        print(f"Error in fitted model: {e}")
        return None

def create_debug_plot(hspice_data, fitted_data):
    """Create debug plot"""
    if hspice_data is None:
        print("No HSPICE data to plot")
        return
    
    voltage_hspice, current_hspice = hspice_data
    
    plt.figure(figsize=(12, 8))
    
    # Plot HSPICE data
    plt.plot(voltage_hspice, current_hspice, 'g--', linewidth=2, marker='o', markersize=3, 
             label=f'HSPICE Simulation ({len(voltage_hspice)} points)')
    
    # Plot fitted data if available
    if fitted_data:
        voltage_fitted, current_fitted = fitted_data
        plt.plot(voltage_fitted, current_fitted, 'r-', linewidth=2, 
                 label=f'Fitted Model ({len(voltage_fitted)} points)')
    
    plt.xlabel('Voltage (V)')
    plt.ylabel('Current (A)')
    plt.title('HSPICE Debug Analysis')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # Add annotations for key points
    if len(voltage_hspice) > 0:
        # Annotate some key points
        key_voltages = [5, 10, 15, 20]
        for v_key in key_voltages:
            if v_key <= voltage_hspice.max():
                i_key = np.interp(v_key, voltage_hspice, current_hspice)
                plt.annotate(f'({v_key}V, {i_key:.2f}A)', 
                           xy=(v_key, i_key), xytext=(v_key+0.5, i_key+0.2),
                           arrowprops=dict(arrowstyle='->', color='blue', alpha=0.7),
                           fontsize=8, color='blue')
    
    plt.tight_layout()
    plt.savefig('hspice_debug_simple.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("Debug plot saved as: hspice_debug_simple.png")

def main():
    """Main debug function"""
    print("Simple HSPICE Debug Test")
    print("=" * 40)
    
    # Test HSPICE parsing
    hspice_data = test_hspice_parsing()
    
    # Test fitted model
    fitted_data = test_fitted_model()
    
    # Create debug plot
    create_debug_plot(hspice_data, fitted_data)
    
    # Summary comparison
    if hspice_data and fitted_data:
        voltage_hspice, current_hspice = hspice_data
        voltage_fitted, current_fitted = fitted_data
        
        print(f"\nComparison at key voltages:")
        for v_test in [5, 10, 15, 20]:
            if v_test <= voltage_hspice.max():
                i_hspice = np.interp(v_test, voltage_hspice, current_hspice)
                i_fitted = np.interp(v_test, voltage_fitted, current_fitted)
                error = abs(i_hspice - i_fitted) / i_fitted * 100 if i_fitted > 0 else 0
                print(f"  {v_test:2.0f}V: HSPICE={i_hspice:.3f}A, Fitted={i_fitted:.3f}A, Error={error:.1f}%")

if __name__ == "__main__":
    main()
