#!/usr/bin/env python3
"""
Demo: HSPICE File Selection and Comparison
Demonstrates the flexible HSPICE file selection functionality
"""

import sys
import os
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_sample_ckt_files():
    """Create sample .ckt files for demonstration"""
    print("Creating sample .ckt files for demonstration...")
    
    # Sample 1: Simple diode model
    sample1_content = """* Simple Diode ESD Model
* Sample file for demonstration

.subckt simple_esd anode cathode
D_main anode cathode D_SIMPLE
.model D_SIMPLE D(
+ IS=1e-9
+ N=2.0
+ RS=0.1
+ BV=15.0
+ IBV=1e-6
+ )
.ends simple_esd

* Test circuit
Vin n1 0 DC 0
X_esd n1 0 simple_esd
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.option post=2
.end
"""
    
    # Sample 2: Behavioral model
    sample2_content = """* Behavioral ESD Model
* Sample file for demonstration

.subckt behavioral_esd anode cathode
.param I_leak=1e-9
.param Vt1=12.0
.param k=3.0

G_esd anode cathode cur='if(V(anode,cathode) < Vt1, I_leak*exp(V(anode,cathode)/1.0), I_leak*exp(k*(V(anode,cathode)-Vt1)/Vt1))'
R_parallel anode cathode 2.0

.ends behavioral_esd

* Test circuit
Vin n1 0 DC 0
X_esd n1 0 behavioral_esd
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.option post=2
.end
"""
    
    # Sample 3: Multi-diode model
    sample3_content = """* Multi-Diode ESD Model
* Sample file for demonstration

.subckt multidiode_esd anode cathode
* Leakage diode
D_leak anode n_leak D_LEAK
R_leak n_leak cathode 1e6

* Trigger diode
D_trig anode n_trig D_TRIG
R_trig n_trig cathode 100

* Snapback resistance
R_snap anode cathode 1.5

.model D_LEAK D(
+ IS=1e-12
+ N=1.0
+ RS=0.1
+ )

.model D_TRIG D(
+ IS=1e-6
+ N=3.0
+ RS=0.01
+ BV=12.0
+ IBV=1e-6
+ )

.ends multidiode_esd

* Test circuit
Vin n1 0 DC 0
X_esd n1 0 multidiode_esd
.dc Vin 0 20 0.1
.print dc V(n1) I(Vin)
.option post=2
.end
"""
    
    # Write sample files
    samples = [
        ("sample_simple_esd.ckt", sample1_content),
        ("sample_behavioral_esd.ckt", sample2_content),
        ("sample_multidiode_esd.ckt", sample3_content)
    ]
    
    created_files = []
    for filename, content in samples:
        try:
            with open(filename, 'w') as f:
                f.write(content)
            created_files.append(filename)
            print("✓ Created: {}".format(filename))
        except Exception as e:
            print("✗ Failed to create {}: {}".format(filename, e))
    
    return created_files

def demonstrate_file_selection_features():
    """Demonstrate the file selection features"""
    print("\n" + "="*60)
    print("HSPICE FILE SELECTION FEATURES")
    print("="*60)
    
    print("\n1. FLEXIBLE FILE SELECTION")
    print("-" * 30)
    print("✓ Select any .ckt, .sp, or .cir file")
    print("✓ Browse with file dialog")
    print("✓ Support for custom file formats")
    print("✓ Automatic file validation")
    
    print("\n2. SIMULATION OPTIONS")
    print("-" * 30)
    print("✓ Configurable voltage range and step")
    print("✓ Temperature settings")
    print("✓ Convergence parameters (GMIN, RELTOL, ABSTOL)")
    print("✓ Iteration limits (ITL1, ITL2)")
    print("✓ Output file selection")
    print("✓ Command preview")
    
    print("\n3. MODEL COMPARISON")
    print("-" * 30)
    print("✓ Select multiple .ckt files")
    print("✓ Simultaneous simulation")
    print("✓ Side-by-side comparison")
    print("✓ Error analysis")
    print("✓ Visual comparison plots")
    
    print("\n4. GUI INTEGRATION")
    print("-" * 30)
    print("✓ Menu: Tools → Run HSPICE with Custom File...")
    print("✓ Menu: Tools → Compare HSPICE Models...")
    print("✓ Toolbar: HSPICE Custom button")
    print("✓ Toolbar: Compare Models button")
    print("✓ Keyboard shortcuts: Ctrl+F6, Shift+F6")

def demonstrate_usage_scenarios():
    """Demonstrate different usage scenarios"""
    print("\n" + "="*60)
    print("USAGE SCENARIOS")
    print("="*60)
    
    print("\n📋 SCENARIO 1: Testing Different Model Approaches")
    print("-" * 50)
    print("1. Generate exact conversion models (Method 1, 2, 3)")
    print("2. Tools → Compare HSPICE Models...")
    print("3. Select all three .ckt files:")
    print("   • bjt_esd_method1_behavioral.ckt")
    print("   • bjt_esd_method2_pwl.ckt")
    print("   • bjt_esd_method3_multidiode.ckt")
    print("4. Configure simulation options")
    print("5. Run comparison and analyze results")
    
    print("\n📋 SCENARIO 2: Custom Model Validation")
    print("-" * 50)
    print("1. Create your own .ckt file with custom model")
    print("2. Tools → Run HSPICE with Custom File...")
    print("3. Browse and select your .ckt file")
    print("4. Configure simulation parameters")
    print("5. Run simulation and compare with measurement data")
    
    print("\n📋 SCENARIO 3: Parameter Sensitivity Analysis")
    print("-" * 50)
    print("1. Create multiple .ckt files with different parameters")
    print("2. Tools → Compare HSPICE Models...")
    print("3. Select all parameter variation files")
    print("4. Run comparison to see parameter effects")
    print("5. Analyze sensitivity and choose optimal parameters")
    
    print("\n📋 SCENARIO 4: Model Verification Workflow")
    print("-" * 50)
    print("1. Load measurement data")
    print("2. Extract parameters and generate exact models")
    print("3. Use custom HSPICE simulation to verify each model")
    print("4. Compare all models with measurement data")
    print("5. Select best model for circuit design")

def demonstrate_advanced_features():
    """Demonstrate advanced features"""
    print("\n" + "="*60)
    print("ADVANCED FEATURES")
    print("="*60)
    
    print("\n🔧 SIMULATION CUSTOMIZATION")
    print("-" * 30)
    print("• Voltage Range: Configure start, stop, and step")
    print("• Temperature: Set operating temperature")
    print("• Convergence: Fine-tune GMIN, RELTOL, ABSTOL")
    print("• Iterations: Control ITL1, ITL2 limits")
    print("• Output: Choose output file location")
    print("• Options: Enable accurate mode, POST output")
    
    print("\n📊 COMPARISON ANALYSIS")
    print("-" * 30)
    print("• Error Metrics: Log error, relative error, absolute error")
    print("• Reference Model: Choose reference for comparison")
    print("• Visual Plots: Automatic comparison plotting")
    print("• Data Export: Save comparison results")
    print("• Report Generation: Detailed comparison reports")
    
    print("\n🎯 INTEGRATION BENEFITS")
    print("-" * 30)
    print("• Seamless Workflow: From parameter extraction to validation")
    print("• Visual Feedback: Immediate plotting of results")
    print("• Quality Control: Built-in validation and error checking")
    print("• Flexibility: Support for any SPICE-compatible model")
    print("• Efficiency: Batch comparison of multiple models")

def show_gui_instructions():
    """Show GUI usage instructions"""
    print("\n" + "="*60)
    print("GUI USAGE INSTRUCTIONS")
    print("="*60)
    
    print("\n🖱️  ACCESSING HSPICE FILE SELECTION")
    print("-" * 40)
    print("Method 1: Menu Bar")
    print("  Tools → Run HSPICE with Custom File... (Ctrl+F6)")
    print("  Tools → Compare HSPICE Models... (Shift+F6)")
    
    print("\nMethod 2: Toolbar")
    print("  Click 'HSPICE Custom' button")
    print("  Click 'Compare Models' button")
    
    print("\n📁 FILE SELECTION PROCESS")
    print("-" * 40)
    print("1. Click on HSPICE file selection option")
    print("2. Browse and select .ckt/.sp/.cir file(s)")
    print("3. Configure simulation options in dialog")
    print("4. Preview HSPICE command")
    print("5. Run simulation")
    print("6. View results in main plot area")
    
    print("\n⚙️  SIMULATION OPTIONS DIALOG")
    print("-" * 40)
    print("• Input File: Shows selected file information")
    print("• Simulation: Configure voltage range, temperature")
    print("• Output: Choose output file and options")
    print("• Advanced: Set convergence and iteration parameters")
    print("• Preview: See generated HSPICE command")
    
    print("\n📊 COMPARISON DIALOG")
    print("-" * 40)
    print("• Files Tab: Select models to compare")
    print("• Options Tab: Configure comparison parameters")
    print("• Results Tab: View comparison results")
    print("• Progress: Real-time simulation progress")

def main():
    """Main demonstration function"""
    print("HSPICE File Selection Demo")
    print("=" * 50)
    print("This demo shows the flexible HSPICE file selection")
    print("and comparison functionality in the BJT ESD GUI.")
    print()
    
    # Create sample files
    created_files = create_sample_ckt_files()
    
    # Demonstrate features
    demonstrate_file_selection_features()
    demonstrate_usage_scenarios()
    demonstrate_advanced_features()
    show_gui_instructions()
    
    print("\n" + "="*60)
    print("SUMMARY: FLEXIBLE HSPICE INTEGRATION")
    print("="*60)
    
    print("\n✅ KEY CAPABILITIES:")
    print("  • Select any .ckt/.sp/.cir file for simulation")
    print("  • Configure all simulation parameters")
    print("  • Compare multiple models simultaneously")
    print("  • Integrate with parameter extraction workflow")
    print("  • Visual comparison and error analysis")
    
    print("\n✅ CREATED SAMPLE FILES:")
    for filename in created_files:
        print("  • {}".format(filename))
    
    print("\n✅ NEXT STEPS:")
    print("  1. Run: python main.py")
    print("  2. Try: Tools → Run HSPICE with Custom File...")
    print("  3. Select one of the sample .ckt files")
    print("  4. Configure options and run simulation")
    print("  5. Try: Tools → Compare HSPICE Models...")
    print("  6. Select multiple sample files for comparison")
    
    print("\n🎯 BENEFITS:")
    print("  • Complete flexibility in model selection")
    print("  • Seamless integration with GUI workflow")
    print("  • Professional simulation configuration")
    print("  • Comprehensive model comparison capabilities")
    
    return True

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
