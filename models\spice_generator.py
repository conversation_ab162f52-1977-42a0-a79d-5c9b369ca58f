"""
SPICE Model Generator for BJT ESD Device
"""

from datetime import datetime
import os

class SpiceGenerator:
    """Class for generating SPICE models for BJT ESD devices"""
    
    def __init__(self):
        self.model_name = "bjt_esd_device"
        
    def generate_bjt_esd_model(self, parameters, model_name=None):
        """
        Generate SPICE model for BJT ESD device
        
        Args:
            parameters (dict): Model parameters
            model_name (str): Optional model name
            
        Returns:
            str: SPICE model content
        """
        if model_name:
            self.model_name = model_name
            
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Extract parameters
        i_leak = parameters.get('I_leak', 1e-9)
        vt1 = parameters.get('Vt1', 12.0)
        k = parameters.get('k', 3.0)
        ron = parameters.get('Ron', 2.0)
        vh = parameters.get('Vh', 14.0)
        i_offset = parameters.get('I_offset', 0.05)
        isb = parameters.get('Isb', 0.04)
        vsb = parameters.get('Vsb', 14.0)
        
        # Generate SPICE content
        spice_content = f"""* BJT ESD Device SPICE Model (.ckt file)
* Generated by BJT ESD Parameter Extractor
* Date: {timestamp}

* Model Parameters:
* I_leak = {i_leak:.6e} A (Leakage Current)
* Vt1 = {vt1:.3f} V (Trigger Voltage)
* k = {k:.3f} (Exponential Factor)
* Ron = {ron:.3f} Ohm (On Resistance)
* Vh = {vh:.3f} V (Holding Voltage)
* I_offset = {i_offset:.6f} A (Current Offset)
* Isb = {isb:.6f} A (Snapback Current)
* Vsb = {vsb:.3f} V (Snapback Voltage)

* BJT ESD Device Subcircuit
.subckt {self.model_name} anode cathode
* Parameters
.param I_leak={i_leak:.6e}
.param Vt1={vt1:.3f}
.param k={k:.3f}
.param Ron={ron:.3f}
.param Vh={vh:.3f}
.param I_offset={i_offset:.6f}
.param Isb={isb:.6f}
.param Vsb={vsb:.3f}

* BJT ESD behavior using HSPICE behavioral modeling
* Three-region model implementation

* Voltage sensing
V_sense anode n_sense 0

* Current source with behavioral model
G_esd n_sense cathode cur='bjt_esd_current(V(anode,cathode))'

* BJT ESD current function
.param bjt_esd_current(v) = '
+ if(v < 0, I_leak*1e-3,
+ if(v < Vt1, I_leak*exp(v/1.0),
+ if(v < Vh, I_leak*exp(k*(v-Vt1)/Vt1),
+ I_offset + (v-Vsb)/Ron + Isb*exp(-(v-Vsb))
+ )))'

* Alternative implementation using diode + resistor for better convergence
D_main anode n_main D_ESD
R_main n_main cathode {{Ron}}

* Diode model parameters calculated from BJT ESD parameters
.model D_ESD D(
+ IS={{I_leak*1e6}}
+ N={{k}}
+ RS=0.1
+ BV={{vh*1.05}}
+ IBV={{i_offset*0.1}}
+ CJO=1e-12
+ TT=1e-12
+ )

* Parallel resistance for high voltage region
R_parallel anode cathode {{Ron*5}}

.ends {self.model_name}

* Usage Example:
* X{self.model_name.upper()} node_anode node_cathode {self.model_name}

* Test Circuit Example:
.subckt test_circuit
* Voltage source for DC sweep
Vin n1 0 DC 0
* ESD device under test
X_esd n1 0 {self.model_name}
* DC analysis
.dc Vin 0 20 0.1
* Print current through voltage source
.print dc I(Vin)
.ends test_circuit

* Simulation commands for HSPICE
* .option post
* .option accurate
* .option gmin=1e-15
* .option abstol=1e-15
* .option reltol=1e-6
"""
        
        return spice_content
        
    def generate_test_netlist(self, parameters, voltage_range=(0, 20), step=0.1):
        """
        Generate test netlist for HSPICE simulation
        
        Args:
            parameters (dict): Model parameters
            voltage_range (tuple): Voltage sweep range (min, max)
            step (float): Voltage step size
            
        Returns:
            str: Complete HSPICE netlist
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        v_min, v_max = voltage_range
        
        # Generate model
        model_content = self.generate_bjt_esd_model(parameters)
        
        # Generate test netlist
        netlist = f"""* BJT ESD Device Test Netlist
* Generated: {timestamp}
* HSPICE Simulation

{model_content}

* Main test circuit
.global 0

* Voltage source
Vin n_anode 0 DC 0

* Device under test
X_dut n_anode 0 {self.model_name}

* Analysis
.dc Vin {v_min} {v_max} {step}

* Output
.print dc V(n_anode) I(Vin)
.probe dc V(n_anode) I(Vin)

* Options for better convergence
.option post=2
.option accurate
.option gmin=1e-15
.option abstol=1e-15
.option reltol=1e-6
.option itl1=500
.option itl2=200

.end
"""
        
        return netlist
        
    def save_spice_model(self, parameters, file_path, include_test=True):
        """
        Save SPICE model to file
        
        Args:
            parameters (dict): Model parameters
            file_path (str): Output file path
            include_test (bool): Include test circuit
        """
        if include_test:
            content = self.generate_test_netlist(parameters)
        else:
            content = self.generate_bjt_esd_model(parameters)
            
        with open(file_path, 'w') as f:
            f.write(content)
            
    def generate_hspice_script(self, netlist_file, output_dir="."):
        """
        Generate HSPICE simulation script
        
        Args:
            netlist_file (str): Path to netlist file
            output_dir (str): Output directory for results
            
        Returns:
            str: HSPICE command script
        """
        base_name = os.path.splitext(os.path.basename(netlist_file))[0]
        
        script = f"""@echo off
REM HSPICE Simulation Script
REM Generated by BJT ESD Parameter Extractor

echo Running HSPICE simulation...
hspice {netlist_file} -o {output_dir}\\{base_name}.lis

echo Simulation completed.
echo Output file: {output_dir}\\{base_name}.lis

pause
"""
        
        return script
        
    def generate_advanced_model(self, parameters):
        """
        Generate advanced SPICE model with temperature and process variations
        
        Args:
            parameters (dict): Base model parameters
            
        Returns:
            str: Advanced SPICE model
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Extract parameters
        i_leak = parameters.get('I_leak', 1e-9)
        vt1 = parameters.get('Vt1', 12.0)
        k = parameters.get('k', 3.0)
        ron = parameters.get('Ron', 2.0)
        vh = parameters.get('Vh', 14.0)
        i_offset = parameters.get('I_offset', 0.05)
        isb = parameters.get('Isb', 0.04)
        vsb = parameters.get('Vsb', 14.0)
        
        advanced_content = f"""* Advanced BJT ESD Device SPICE Model
* Generated: {timestamp}
* Includes temperature and process variations

.subckt {self.model_name}_advanced anode cathode PARAMS:
+ TEMP_COEFF=0.002
+ PROCESS_VAR=1.0

* Temperature-dependent parameters
.param I_leak_temp='I_leak_nom * exp(TEMP_COEFF*(TEMP-27))'
.param Vt1_temp='Vt1_nom * (1 - TEMP_COEFF*(TEMP-27)/100)'
.param Ron_temp='Ron_nom * (1 + TEMP_COEFF*(TEMP-27)/2)'

* Process variation parameters
.param I_leak_nom={{i_leak:.6e}*PROCESS_VAR}}
.param Vt1_nom={{vt1:.3f}*PROCESS_VAR}}
.param k_nom={{k:.3f}}
.param Ron_nom={{ron:.3f}*PROCESS_VAR}}
.param Vh_nom={{vh:.3f}*PROCESS_VAR}}
.param I_offset_nom={{i_offset:.6f}*PROCESS_VAR}}
.param Isb_nom={{isb:.6f}*PROCESS_VAR}}
.param Vsb_nom={{vsb:.3f}*PROCESS_VAR}}

* Main ESD structure
D1 anode n1 D_ESD_MAIN
R1 n1 cathode {{Ron_temp}}

* Parallel leakage path
R_leak anode cathode {{1/I_leak_temp*Vt1_temp}}

* Trigger enhancement
D2 anode n2 D_ESD_TRIGGER
R2 n2 cathode {{Ron_temp*10}}

* Model definitions
.model D_ESD_MAIN D(
+ IS={{I_leak_temp}}
+ N={{k_nom}}
+ RS=0.05
+ BV={{Vh_nom}}
+ IBV={{I_offset_nom}}
+ CJO=1e-12
+ TT=1e-12
+ )

.model D_ESD_TRIGGER D(
+ IS={{I_leak_temp*1e3}}
+ N={{k_nom*0.8}}
+ RS=0.1
+ BV={{Vt1_temp}}
+ IBV={{I_leak_temp*1e6}}
+ CJO=1e-12
+ TT=1e-12
+ )

.ends {self.model_name}_advanced

* Monte Carlo analysis example
* .param PROCESS_VAR=agauss(1.0, 0.1, 3)
* .mc 100 dc Vin 0 20 0.1
"""
        
        return advanced_content
