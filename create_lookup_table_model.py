#!/usr/bin/env python3
"""
Create a lookup table based HSPICE model
"""

import sys
import os
import numpy as np

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_lookup_table_model():
    """Create HSPICE model based on fitted data points"""
    try:
        from utils.data_loader import DataLoader
        from models.bjt_esd_model import BJTESDModel
        
        print("Creating lookup table based HSPICE model...")
        
        if not os.path.exists('1.csv'):
            print("No experimental data file found")
            return False
        
        # Load and fit data
        loader = DataLoader()
        voltage_exp, current_exp, _ = loader.load_data('1.csv')
        
        model = BJTESDModel()
        fitted_params = model.fit_parameters(voltage_exp, current_exp)
        
        print(f"Fitted R² = {fitted_params.get('r_squared', 0):.4f}")
        
        # Generate fitted curve data points
        voltage_points = np.linspace(0, 20, 21)  # 21 points from 0 to 20V
        current_points = model.current_equation(voltage_points, **fitted_params)
        
        print("Generated lookup table points:")
        for i, (v, i) in enumerate(zip(voltage_points, current_points)):
            print(f"  {v:4.1f}V: {i:.6f}A")
        
        # Create HSPICE model with lookup table
        model_content = f"""* BJT ESD Device SPICE Model (.ckt file) - LOOKUP TABLE VERSION
* Generated from fitted model data points
* Date: 2025-06-06

* Model Parameters (Fitted from experimental data):
* R² = {fitted_params.get('r_squared', 0):.6f}

* BJT ESD Device Subcircuit - Lookup Table Model
.subckt bjt_esd_device anode cathode

* Voltage-controlled current source with lookup table
* This directly implements the fitted I-V curve
Gmain anode cathode VCCS PWL(1)"""
        
        # Add voltage-current pairs
        for v, i in zip(voltage_points, current_points):
            model_content += f"\n+ {v:.1f}V {i:.6f}A"
        
        model_content += """

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit"""
        
        # Save the model
        with open('lookup_table_model.ckt', 'w') as f:
            f.write(model_content)
        
        print(f"\n✓ Lookup table model saved to: lookup_table_model.ckt")
        
        # Create corresponding netlist
        netlist_content = f"""* BJT ESD Device Simulation Netlist (.sp file) - LOOKUP TABLE VERSION
* Generated from fitted model data points
* Date: 2025-06-06

.title BJT ESD Device I-V Characteristics - Lookup Table Model

.include 'lookup_table_model.ckt'

* Voltage source for DC sweep
Vin n1 0 DC 0

* Instantiate ESD device from included model
Xesd n1 0 bjt_esd_device

* DC Analysis (100 points from 0 to 20V)
.dc Vin 0 20 0.2

* Output commands
.print dc V(n1) I(Vin)
.probe dc V(n1) I(Vin)

* Simulation options for better convergence
.option post=2
.option gmin=1e-15
.option accurate
.option runlvl=5
.option reltol=1e-4
.option abstol=1e-12

.end"""
        
        with open('lookup_table_simulation.sp', 'w') as f:
            f.write(netlist_content)
        
        print(f"✓ Lookup table netlist saved to: lookup_table_simulation.sp")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_lookup_table_model():
    """Test the lookup table model with HSPICE"""
    try:
        from utils.hspice_interface import HSPICEInterface
        
        hspice = HSPICEInterface()
        
        if not hspice.verify_hspice_installation():
            print("HSPICE not available for testing")
            return False
        
        print("\nTesting lookup table model with HSPICE...")
        
        # Run HSPICE simulation
        result = hspice.run_simulation_from_netlist('lookup_table_simulation.sp')
        
        if result:
            voltage_sim, current_sim = result
            print(f"✓ HSPICE simulation successful: {len(voltage_sim)} points")
            
            # Show key results
            print(f"\nKey simulation results:")
            test_voltages = [1, 5, 10, 15, 20]
            for v_test in test_voltages:
                if v_test <= voltage_sim.max():
                    i_sim = np.interp(v_test, voltage_sim, current_sim)
                    print(f"  {v_test:2.0f}V: {i_sim:.6f}A")
            
            return True
        else:
            print("✗ HSPICE simulation failed")
            return False
            
    except Exception as e:
        print(f"Error testing model: {e}")
        return False

def main():
    """Main function"""
    print("BJT ESD Parameter Extractor - Lookup Table Model Creator")
    print("=" * 60)
    
    # Create lookup table model
    if create_lookup_table_model():
        print("\n✓ Lookup table model created successfully")
        
        # Test with HSPICE
        if test_lookup_table_model():
            print("\n✓ Lookup table model tested successfully")
            print("\nThe lookup table model should provide exact matching")
            print("with the fitted curve since it uses the same data points.")
        else:
            print("\n⚠ HSPICE testing failed, but model file is created")
    else:
        print("\n✗ Failed to create lookup table model")
    
    print(f"\nGenerated files:")
    for filename in ['lookup_table_model.ckt', 'lookup_table_simulation.sp']:
        if os.path.exists(filename):
            print(f"  {filename} - {os.path.getsize(filename)} bytes")

if __name__ == "__main__":
    main()
