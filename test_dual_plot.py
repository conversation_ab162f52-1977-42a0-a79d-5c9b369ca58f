#!/usr/bin/env python3
"""
Test Dual Plot Functionality
Tests the new linear + log dual plot layout
"""

import sys
import os
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dual_plot_creation():
    """Test dual plot widget creation"""
    print("Testing dual plot creation...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.plot_widget import PlotWidget
        
        # Create QApplication
        app = QApplication([])
        
        # Create plot widget
        plot_widget = PlotWidget()
        print("✓ Dual plot widget created successfully")
        
        # Test if both axes exist
        if hasattr(plot_widget, 'ax_linear'):
            print("✓ Linear axis exists")
        else:
            print("✗ Linear axis missing")
            
        if hasattr(plot_widget, 'ax_log'):
            print("✓ Log axis exists")
        else:
            print("✗ Log axis missing")
            
        # Test control panel
        if hasattr(plot_widget, 'show_linear_check'):
            print("✓ Linear plot checkbox exists")
        else:
            print("✗ Linear plot checkbox missing")
            
        if hasattr(plot_widget, 'show_log_check'):
            print("✓ Log plot checkbox exists")
        else:
            print("✗ Log plot checkbox missing")
            
        if hasattr(plot_widget, 'sync_zoom_check'):
            print("✓ Sync zoom checkbox exists")
        else:
            print("✗ Sync zoom checkbox missing")
            
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print("✗ Dual plot creation error: {}".format(e))
        return False

def test_dual_plot_data():
    """Test plotting data on dual plots"""
    print("\nTesting dual plot data display...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.plot_widget import PlotWidget
        
        # Create QApplication
        app = QApplication([])
        
        # Create plot widget
        plot_widget = PlotWidget()
        
        # Create test data
        voltage = np.linspace(0, 20, 100)
        # Create ESD-like I-V curve with both positive and negative currents
        current = np.where(voltage < 12, 
                          1e-9 * np.exp(voltage/2),  # Leakage region
                          1e-3 * (voltage - 12)**2)  # Trigger region
        
        # Add some negative current for testing linear plot
        current[:10] = -current[:10] * 0.1
        
        data = {'voltage': voltage, 'current': current}
        
        # Test plotting measurement data
        plot_widget.plot_measurement_data(data)
        print("✓ Measurement data plotted on both axes")
        
        # Test plotting model curve
        model_current = current * 1.1  # Slightly different for testing
        plot_widget.plot_model_curve(voltage, model_current)
        print("✓ Model curve plotted on both axes")
        
        # Test simulation results
        sim_data = {'voltage': voltage, 'current': current * 0.95}
        plot_widget.plot_simulation_results(sim_data)
        print("✓ Simulation results plotted on both axes")
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print("✗ Dual plot data error: {}".format(e))
        return False

def test_plot_controls():
    """Test plot control functionality"""
    print("\nTesting plot controls...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.plot_widget import PlotWidget
        
        # Create QApplication
        app = QApplication([])
        
        # Create plot widget
        plot_widget = PlotWidget()
        
        # Test visibility toggles
        plot_widget.toggle_linear_plot(False)
        print("✓ Linear plot toggle works")
        
        plot_widget.toggle_log_plot(False)
        print("✓ Log plot toggle works")
        
        # Restore visibility
        plot_widget.toggle_linear_plot(True)
        plot_widget.toggle_log_plot(True)
        print("✓ Plot visibility restored")
        
        # Test clear function
        plot_widget.clear_plot()
        print("✓ Clear plot function works")
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print("✗ Plot controls error: {}".format(e))
        return False

def test_exact_conversion_comparison():
    """Test exact conversion comparison on dual plots"""
    print("\nTesting exact conversion comparison...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.plot_widget import PlotWidget
        
        # Create QApplication
        app = QApplication([])
        
        # Create plot widget
        plot_widget = PlotWidget()
        
        # Create test data
        voltage = np.linspace(0, 20, 100)
        measurement_data = np.where(voltage < 12, 
                                   1e-9 * np.exp(voltage/2),
                                   1e-3 * (voltage - 12)**2)
        
        fitted_current = measurement_data * 1.02
        behavioral_current = measurement_data * 1.01
        pwl_current = measurement_data * 0.99
        multidiode_current = measurement_data * 1.03
        
        # Test exact conversion comparison
        plot_widget.plot_exact_conversion_comparison(
            voltage, measurement_data, fitted_current,
            behavioral_current, pwl_current, multidiode_current
        )
        print("✓ Exact conversion comparison plotted on both axes")
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print("✗ Exact conversion comparison error: {}".format(e))
        return False

def demonstrate_dual_plot_features():
    """Demonstrate dual plot features"""
    print("\n" + "="*60)
    print("DUAL PLOT FEATURES")
    print("="*60)
    
    print("\n📊 LAYOUT DESIGN")
    print("-" * 30)
    print("✓ 左侧: 线性刻度图 (Linear Scale)")
    print("  • 显示完整的电流范围，包括负值")
    print("  • 适合观察低电压区域的细节")
    print("  • 清晰显示电流的线性变化")
    
    print("\n✓ 右侧: 对数刻度图 (Log Scale)")
    print("  • 显示宽动态范围的电流")
    print("  • 适合观察ESD器件的多个区域")
    print("  • 传统的ESD特性分析视图")
    
    print("\n🎛️ CONTROL PANEL")
    print("-" * 30)
    print("✓ 显示控制:")
    print("  • 线性图 复选框 - 切换线性图显示")
    print("  • 对数图 复选框 - 切换对数图显示")
    print("  • 同步缩放 复选框 - X轴缩放同步")
    
    print("\n🔄 SYNCHRONIZATION")
    print("-" * 30)
    print("✓ X轴同步缩放:")
    print("  • 在一个图上缩放，另一个图自动跟随")
    print("  • 可通过复选框开启/关闭同步")
    print("  • 便于对比分析相同电压范围")
    
    print("\n📈 DATA DISPLAY")
    print("-" * 30)
    print("✓ 智能数据处理:")
    print("  • 线性图: 显示所有数据点（包括负值）")
    print("  • 对数图: 只显示正值数据点")
    print("  • 自动过滤零值和负值（对数图）")
    print("  • 保持数据完整性")

def show_usage_benefits():
    """Show usage benefits of dual plot"""
    print("\n" + "="*60)
    print("DUAL PLOT USAGE BENEFITS")
    print("="*60)
    
    print("\n🎯 ANALYSIS ADVANTAGES")
    print("-" * 30)
    print("1. 全面视角:")
    print("   • 线性图显示低电流区域细节")
    print("   • 对数图显示整体动态范围")
    print("   • 同时观察不同尺度的特性")
    
    print("\n2. 精确分析:")
    print("   • 线性图便于测量小电流差异")
    print("   • 对数图便于识别指数变化")
    print("   • 双重验证提高分析可靠性")
    
    print("\n3. 灵活显示:")
    print("   • 可根据需要隐藏任一图表")
    print("   • 节省屏幕空间")
    print("   • 专注于特定分析需求")
    
    print("\n📊 SPECIFIC USE CASES")
    print("-" * 30)
    print("🔍 漏电流分析:")
    print("   • 线性图: 观察漏电流的线性变化")
    print("   • 对数图: 确认指数关系")
    
    print("\n⚡ 触发特性:")
    print("   • 线性图: 精确测量触发电压")
    print("   • 对数图: 观察触发前后的电流跳变")
    
    print("\n🔄 回滞行为:")
    print("   • 线性图: 清晰显示回滞环路")
    print("   • 对数图: 确认保持电流水平")
    
    print("\n🎛️ 模型验证:")
    print("   • 线性图: 验证低电流区域匹配")
    print("   • 对数图: 验证整体拟合质量")

def main():
    """Main test function"""
    print("BJT ESD Dual Plot Test")
    print("=" * 50)
    print("Testing the new linear + log dual plot functionality")
    print()
    
    tests = [
        test_dual_plot_creation,
        test_dual_plot_data,
        test_plot_controls,
        test_exact_conversion_comparison
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print("Test failed with exception: {}".format(e))
    
    # Show features and benefits
    demonstrate_dual_plot_features()
    show_usage_benefits()
    
    print("\n" + "="*60)
    print("DUAL PLOT TEST RESULTS")
    print("="*60)
    print("Tests passed: {}/{}".format(passed, total))
    
    if passed == total:
        print("✅ All dual plot tests passed!")
        print("\n🎉 NEW FEATURES READY:")
        print("  • 双图表布局 (线性 + 对数)")
        print("  • 智能数据显示")
        print("  • 可视化控制面板")
        print("  • X轴同步缩放")
        print("  • 完整向后兼容")
    else:
        print("❌ Some dual plot tests failed")
        print("Check the errors above for details")
    
    print("\n📋 NEXT STEPS:")
    print("  1. Run: python main.py")
    print("  2. Load data and observe dual plot layout")
    print("  3. Use control panel to toggle plot visibility")
    print("  4. Test zoom synchronization")
    print("  5. Compare linear vs log scale analysis")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print("\nPress Enter to exit...")
    input()
    sys.exit(0 if success else 1)
