* BJT ESD Device SPICE Model - EXACT MATCH BEHAVIORAL VERSION
* Uses HSPICE B-element to implement the exact fitted equation
* This should provide perfect matching with the fitted curve
* Date: 2025-06-06

* Fitted Parameters (example values - will be updated by main program):
* I_leak = 9.161209e-11 A
* Vt1 = 3.841 V  
* k = 7.812
* Vh = 14.011 V
* Ron = 1.213 Ohm
* I_offset = 0.152575 A

.subckt bjt_esd_device anode cathode

* Model parameters - these will be substituted by the main program
.param I_leak=9.161209e-11
.param Vt1=3.841
.param k=7.812
.param Vh=14.011
.param Ron=1.213
.param I_offset=0.152575
.param Vsb={Vh*0.9}
.param Isb={I_offset*0.5}

* Behavioral current source implementing the exact fitted equation
* This directly implements the three-region BJT ESD model:
* Region 1: V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2: Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)  
* Region 3: V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

Besd anode cathode I='
+ (V(anode,cathode) < Vt1) ? I_leak * exp(V(anode,cathode)/Vt1) :
+ (V(anode,cathode) < Vh) ? I_leak * exp(k*(V(anode,cathode)-Vt1)/Vt1) :
+ I_offset + (V(anode,cathode)-Vsb)/Ron + Isb*exp(-(V(anode,cathode)-Vsb))'

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device
