* BJT ESD Device SPICE Model (.ckt file)
* Generated by BJT ESD Parameter Extractor
* Date: 2025-06-06 10:31:37

* Model Parameters:
* I_leak = 9.161209e-11 A (Leakage Current)
* Vt1 = 3.841 V (Trigger Voltage)
* k = 7.812 (Exponential Factor)
* Ron = 1.213 Ohm (On Resistance)
* Vh = 14.011 V (Holding Voltage)
* I_offset = 0.152575 A (Current Offset)
* Isb = 0.292588 A (Snapback Current)
* Vsb = 14.661 V (Snapback Voltage)

* BJT ESD Device Subcircuit with HSPICE if statements
.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak=9.161209e-11
.param Vt1=3.841
.param k=7.812
.param Ron=1.213
.param Vh=14.011
.param I_offset=0.152575
.param Isb=0.292588
.param Vsb=14.661

* BJT ESD behavior using behavioral current source for better accuracy
* Current equation with three regions:
* Region 1 (Leakage): V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2 (Trigger): Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)
* Region 3 (Snapback): V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

* Accurate BJT ESD model using piecewise linear current source
* This model closely approximates the mathematical BJT ESD equation

* Voltage-controlled current source with detailed PWL table
Gesd anode cathode PWL(1) V(anode,cathode) (0.000000,9.161209e-11) (0.246909,9.769486e-11) (0.493819,1.041815e-10) (0.740728,1.110989e-10) (0.987637,1.184755e-10) (1.234546,1.263419e-10) (1.481456,1.347306e-10) (1.728365,1.436764e-10) (1.975274,1.532160e-10) (2.222183,1.633891e-10) (2.469093,1.742377e-10) (2.716002,1.858066e-10) (2.962911,1.981436e-10) (3.209820,2.112997e-10) (3.456730,2.253294e-10) (3.566467,2.318602e-10) (3.676204,2.385803e-10) (3.785942,2.454952e-10) (3.895679,1.024282e-10) (4.005417,1.280424e-10) (4.115154,1.600618e-10) (4.224892,2.000882e-10) (5.050847,1.073516e-09) (5.876801,5.759647e-09) (6.702756,3.090175e-08) (7.528711,1.657945e-07) (8.354666,8.895235e-07) (9.180621,4.772485e-06) (10.006576,2.560541e-05) (10.832531,1.373786e-04) (11.658485,7.370656e-04) (12.484440,3.954516e-03) (13.310395,2.121683e-02) (13.510552,3.187746e-02) (13.710708,4.789465e-02) (13.910864,7.195984e-02) (14.111021,2.064681e-01) (14.311177,2.795145e-01) (14.511333,3.692403e-01) (14.711489,4.726197e-01) (15.089240,6.965354e-01) (15.466991,9.479713e-01) (15.844742,1.218269e+00) (16.222492,1.501496e+00) (16.600243,1.793584e+00) (16.977994,2.091744e+00) (17.355745,2.394068e+00) (17.733495,2.699245e+00) (18.111246,3.006377e+00) (18.488997,3.314850e+00) (18.866748,3.624241e+00) (19.244498,3.934262e+00) (19.622249,4.244714e+00) (20.000000,4.555463e+00)

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit
