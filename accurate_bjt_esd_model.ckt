* BJT ESD Device SPICE Model (.ckt file)
* Generated by BJT ESD Parameter Extractor
* Date: 2025-06-06 10:31:37

* Model Parameters:
* I_leak = 9.161209e-11 A (Leakage Current)
* Vt1 = 3.841 V (Trigger Voltage)
* k = 7.812 (Exponential Factor)
* Ron = 1.213 Ohm (On Resistance)
* Vh = 14.011 V (Holding Voltage)
* I_offset = 0.152575 A (Current Offset)
* Isb = 0.292588 A (Snapback Current)
* Vsb = 14.661 V (Snapback Voltage)

* BJT ESD Device Subcircuit with HSPICE if statements
.subckt bjt_esd_device anode cathode
* Parameters
.param I_leak=9.161209e-11
.param Vt1=3.841
.param k=7.812
.param Ron=1.213
.param Vh=14.011
.param I_offset=0.152575
.param Isb=0.292588
.param Vsb=14.661

* BJT ESD behavior using behavioral current source for better accuracy
* Current equation with three regions:
* Region 1 (Leakage): V < Vt1 -> I = I_leak * exp(V/Vt1)
* Region 2 (Trigger): Vt1 <= V < Vh -> I = I_leak * exp(k*(V-Vt1)/Vt1)
* Region 3 (Snapback): V >= Vh -> I = I_offset + (V-Vsb)/Ron + Isb*exp(-(V-Vsb))

* Accurate BJT ESD model using piecewise linear current source
* This model closely approximates the mathematical BJT ESD equation

* Simplified equivalent circuit for better HSPICE compatibility
* Using basic circuit elements to approximate BJT ESD behavior

* Leakage resistance (high value for low current at low voltage)
Rleak anode n1 1.092e+10

* Trigger diode (approximates exponential turn-on)
Dtrig n1 n2 DESD
.model DESD D(IS=9.161209e-11 N=7.8 RS=0.1 BV=16.8)

* On-state resistance (snapback region)
Ron n2 cathode 1.213

.ends bjt_esd_device

* Usage Example:
* Xesd node_anode node_cathode bjt_esd_device

* Test Circuit Example:
* .subckt test_circuit
* Vin n1 0 DC 0
* Xesd n1 0 bjt_esd_device
* .dc Vin 0 20 0.1
* .print dc I(Vin)
* .ends test_circuit
