"""
Plot Widget for BJT ESD Parameter Extractor
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import Navigation<PERSON>oolbar2QT as NavigationToolbar
from matplotlib.figure import Figure
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QCheckBox, QLabel

class PlotWidget(QWidget):
    """Widget for plotting measurement data and model curves"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize user interface"""
        layout = QVBoxLayout(self)

        # Create control panel
        self.create_control_panel(layout)

        # Create matplotlib figure and canvas with dual plots
        self.figure = Figure(figsize=(16, 6))
        self.canvas = FigureCanvas(self.figure)

        # Create navigation toolbar
        self.toolbar = NavigationToolbar(self.canvas, self)

        # Add widgets to layout
        layout.addWidget(self.toolbar)
        layout.addWidget(self.canvas)

        # Create dual subplots (left: linear, right: log)
        self.ax_linear = self.figure.add_subplot(121)  # Left subplot - linear scale
        self.ax_log = self.figure.add_subplot(122)     # Right subplot - log scale

        # For backward compatibility, keep ax as reference to log plot
        self.ax = self.ax_log

        self.setup_plots()

    def create_control_panel(self, parent_layout):
        """Create control panel for plot options"""
        control_layout = QHBoxLayout()

        # Plot visibility controls
        control_layout.addWidget(QLabel("Display:"))

        self.show_linear_check = QCheckBox("Linear Plot")
        self.show_linear_check.setChecked(True)
        self.show_linear_check.toggled.connect(self.toggle_linear_plot)
        control_layout.addWidget(self.show_linear_check)

        self.show_log_check = QCheckBox("Log Plot")
        self.show_log_check.setChecked(True)
        self.show_log_check.toggled.connect(self.toggle_log_plot)
        control_layout.addWidget(self.show_log_check)

        control_layout.addStretch()

        # Sync controls
        self.sync_zoom_check = QCheckBox("Sync Zoom")
        self.sync_zoom_check.setChecked(True)
        control_layout.addWidget(self.sync_zoom_check)

        parent_layout.addLayout(control_layout)

    def toggle_linear_plot(self, visible):
        """Toggle linear plot visibility"""
        self.ax_linear.set_visible(visible)
        self.figure.canvas.draw()

    def toggle_log_plot(self, visible):
        """Toggle log plot visibility"""
        self.ax_log.set_visible(visible)
        self.figure.canvas.draw()

    def setup_plots(self):
        """Setup both linear and log plots"""
        self.setup_linear_plot()
        self.setup_log_plot()

    def setup_linear_plot(self):
        """Setup linear scale plot appearance and labels"""
        self.ax_linear.set_xlabel('Voltage (V)', fontsize=12)
        self.ax_linear.set_ylabel('Current (A)', fontsize=12)
        self.ax_linear.set_title('BJT ESD I-V Characteristics (Linear)', fontsize=14, fontweight='bold')
        self.ax_linear.grid(True, alpha=0.3)

        # Set plot limits for linear scale
        self.ax_linear.set_xlim(0, 25)
        self.ax_linear.set_ylim(-0.1, 2.0)  # Linear scale limits

    def setup_log_plot(self):
        """Setup log scale plot appearance and labels"""
        self.ax_log.set_xlabel('Voltage (V)', fontsize=12)
        self.ax_log.set_ylabel('Current (A)', fontsize=12)
        self.ax_log.set_title('BJT ESD I-V Characteristics (Log)', fontsize=14, fontweight='bold')
        self.ax_log.grid(True, alpha=0.3)
        self.ax_log.set_yscale('log')

        # Set plot limits for log scale
        self.ax_log.set_xlim(0, 25)
        self.ax_log.set_ylim(1e-12, 10)

        self.canvas.draw()

        # Connect axis synchronization
        self.ax_linear.callbacks.connect('xlim_changed', self.on_xlims_change)
        self.ax_log.callbacks.connect('xlim_changed', self.on_xlims_change)

    def plot_measurement_data(self, data):
        """Plot measurement data on both linear and log plots"""
        # Clear both plots
        self.ax_linear.clear()
        self.ax_log.clear()
        self.setup_plots()

        voltage = data['voltage']
        current = data['current']  # Keep original current values
        current_abs = np.abs(current)  # Absolute values for log scale

        # Plot on linear scale (all data points)
        self.ax_linear.plot(voltage, current, 'bo-',
                           markersize=4, linewidth=1.5,
                           label='Measurement Data', alpha=0.8)

        # Plot on log scale (only positive currents)
        valid_mask = current_abs > 0
        if np.any(valid_mask):
            voltage_valid = voltage[valid_mask]
            current_valid = current_abs[valid_mask]

            self.ax_log.semilogy(voltage_valid, current_valid, 'bo-',
                               markersize=4, linewidth=1.5,
                               label='Measurement Data', alpha=0.8)

        # Update legends and redraw
        self.ax_linear.legend()
        self.ax_log.legend()
        self.canvas.draw()

    def plot_model_curve(self, voltage, model_current):
        """Plot model curve alongside measurement data on both plots"""
        # Remove existing model curves from both plots
        for ax in [self.ax_linear, self.ax_log]:
            lines_to_remove = []
            for line in ax.lines:
                if line.get_label() in ['Model Curve', 'HSPICE Simulation']:
                    lines_to_remove.append(line)
            for line in lines_to_remove:
                line.remove()

        model_current_abs = np.abs(model_current)

        # Plot on linear scale (all data points)
        self.ax_linear.plot(voltage, model_current, 'r-',
                           linewidth=2, label='Model Curve', alpha=0.9)

        # Plot on log scale (only positive currents)
        valid_mask = model_current_abs > 0
        if np.any(valid_mask):
            self.ax_log.semilogy(voltage[valid_mask], model_current_abs[valid_mask],
                               'r-', linewidth=2, label='Model Curve', alpha=0.9)

        # Update legends and redraw
        self.ax_linear.legend()
        self.ax_log.legend()
        self.canvas.draw()

    def plot_simulation_results(self, sim_data):
        """Plot HSPICE simulation results on both plots"""
        # Remove existing simulation curves from both plots
        for ax in [self.ax_linear, self.ax_log]:
            lines_to_remove = []
            for line in ax.lines:
                if line.get_label() == 'HSPICE Simulation':
                    lines_to_remove.append(line)
            for line in lines_to_remove:
                line.remove()

        voltage = sim_data['voltage']
        current = sim_data['current']
        current_abs = np.abs(current)

        # Plot on linear scale (all data points)
        self.ax_linear.plot(voltage, current, 'g--',
                           linewidth=2, label='HSPICE Simulation', alpha=0.9)

        # Plot on log scale (only positive currents)
        valid_mask = current_abs > 0
        if np.any(valid_mask):
            self.ax_log.semilogy(voltage[valid_mask], current_abs[valid_mask],
                               'g--', linewidth=2, label='HSPICE Simulation', alpha=0.9)

        # Update legends and redraw
        self.ax_linear.legend()
        self.ax_log.legend()
        self.canvas.draw()

    def plot_exact_conversion_comparison(self, voltage, measurement_data, fitted_current,
                                       behavioral_current=None, pwl_current=None,
                                       multidiode_current=None):
        """Plot comparison of exact conversion methods on both plots"""
        # Clear both plots
        self.ax_linear.clear()
        self.ax_log.clear()
        self.setup_plots()

        # Update titles for comparison
        self.ax_linear.set_title('Exact Conversion Comparison (Linear)', fontsize=14, fontweight='bold')
        self.ax_log.set_title('Exact Conversion Comparison (Log)', fontsize=14, fontweight='bold')

        current_abs = np.abs(measurement_data)

        # Plot measurement data on both scales
        self.ax_linear.plot(voltage, measurement_data, 'ko-',
                           markersize=3, linewidth=1,
                           label='Measurement Data', alpha=0.7)

        valid_mask = current_abs > 0
        if np.any(valid_mask):
            self.ax_log.semilogy(voltage[valid_mask], current_abs[valid_mask], 'ko-',
                               markersize=3, linewidth=1,
                               label='Measurement Data', alpha=0.7)

        # Plot fitted model on both scales
        fitted_abs = np.abs(fitted_current)
        self.ax_linear.plot(voltage, fitted_current, 'r-',
                           linewidth=2, label='Fitted Model', alpha=0.9)

        valid_mask_fitted = fitted_abs > 0
        if np.any(valid_mask_fitted):
            self.ax_log.semilogy(voltage[valid_mask_fitted], fitted_abs[valid_mask_fitted],
                               'r-', linewidth=2, label='Fitted Model', alpha=0.9)

        # Plot exact conversion methods
        methods = [
            (behavioral_current, 'b-', 'Behavioral SPICE'),
            (pwl_current, 'g-', 'PWL Model'),
            (multidiode_current, 'm-', 'Multi-Diode')
        ]

        for method_current, style, label in methods:
            if method_current is not None:
                method_abs = np.abs(method_current)

                # Linear plot
                self.ax_linear.plot(voltage, method_current, style,
                                   linewidth=2, label=label, alpha=0.8)

                # Log plot
                valid_mask_method = method_abs > 0
                if np.any(valid_mask_method):
                    self.ax_log.semilogy(voltage[valid_mask_method], method_abs[valid_mask_method],
                                       style, linewidth=2, label=label, alpha=0.8)

        # Update legends and redraw
        self.ax_linear.legend()
        self.ax_log.legend()
        self.canvas.draw()

    def plot_conversion_error_analysis(self, voltage, log_errors, method_names):
        """Plot conversion error analysis on log plot"""
        self.ax_log.clear()

        # Setup error plot
        self.ax_log.set_xlabel('Voltage (V)', fontsize=12)
        self.ax_log.set_ylabel('Log Error', fontsize=12)
        self.ax_log.set_title('Conversion Error Analysis', fontsize=14, fontweight='bold')
        self.ax_log.grid(True, alpha=0.3)

        # Plot error curves
        colors = ['b', 'g', 'm', 'c', 'y']
        for i, (error, name) in enumerate(zip(log_errors, method_names)):
            color = colors[i % len(colors)]
            self.ax_log.semilogy(voltage, error, color=color, linewidth=2,
                               label='{} Error'.format(name), alpha=0.8)

        # Add tolerance line
        self.ax_log.axhline(y=0.1, color='r', linestyle='--', alpha=0.7,
                          label='Tolerance (10%)')

        self.ax_log.set_xlim(0, voltage.max())
        self.ax_log.legend()
        self.canvas.draw()

    def clear_plot(self):
        """Clear all plots"""
        self.ax_linear.clear()
        self.ax_log.clear()
        self.setup_plots()
        self.canvas.draw()

    def sync_x_axis(self):
        """Sync x-axis limits between linear and log plots"""
        if self.sync_zoom_check.isChecked():
            xlim = self.ax_linear.get_xlim()
            self.ax_log.set_xlim(xlim)
            self.canvas.draw()

    def on_xlims_change(self, ax):
        """Handle x-axis limit changes for synchronization"""
        if self.sync_zoom_check.isChecked():
            xlim = ax.get_xlim()
            if ax == self.ax_linear:
                self.ax_log.set_xlim(xlim)
            else:
                self.ax_linear.set_xlim(xlim)
            self.canvas.draw()
