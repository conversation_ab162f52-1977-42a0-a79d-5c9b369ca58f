"""
Plot Widget for BJT ESD Parameter Extractor
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import Navigation<PERSON>oolbar2QT as NavigationToolbar
from matplotlib.figure import Figure
from PyQt5.QtWidgets import QWidget, QVBoxLayout

class PlotWidget(QWidget):
    """Widget for plotting measurement data and model curves"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize user interface"""
        layout = QVBoxLayout(self)
        
        # Create matplotlib figure and canvas
        self.figure = Figure(figsize=(12, 6))
        self.canvas = FigureCanvas(self.figure)
        
        # Create navigation toolbar
        self.toolbar = NavigationToolbar(self.canvas, self)
        
        # Add widgets to layout
        layout.addWidget(self.toolbar)
        layout.addWidget(self.canvas)
        
        # Create subplot
        self.ax = self.figure.add_subplot(111)
        self.setup_plot()
        
    def setup_plot(self):
        """Setup plot appearance and labels"""
        self.ax.set_xlabel('Voltage (V)', fontsize=12)
        self.ax.set_ylabel('Current (A)', fontsize=12)
        self.ax.set_title('BJT ESD Device I-V Characteristics', fontsize=14, fontweight='bold')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_yscale('log')
        
        # Set plot limits
        self.ax.set_xlim(0, 25)
        self.ax.set_ylim(1e-12, 10)
        
        self.canvas.draw()
        
    def plot_measurement_data(self, data):
        """Plot measurement data"""
        self.ax.clear()
        self.setup_plot()
        
        voltage = data['voltage']
        current = np.abs(data['current'])  # Use absolute value for log scale
        
        # Filter out zero and negative currents for log scale
        valid_mask = current > 0
        voltage_valid = voltage[valid_mask]
        current_valid = current[valid_mask]
        
        # Plot measurement data
        self.ax.semilogy(voltage_valid, current_valid, 'bo-', 
                        markersize=4, linewidth=1.5, 
                        label='Measurement Data', alpha=0.8)
        
        self.ax.legend()
        self.canvas.draw()
        
    def plot_model_curve(self, voltage, model_current):
        """Plot model curve alongside measurement data"""
        # Remove existing model curve if present
        lines_to_remove = []
        for line in self.ax.lines:
            if line.get_label() in ['Model Curve', 'HSPICE Simulation']:
                lines_to_remove.append(line)
        
        for line in lines_to_remove:
            line.remove()
            
        # Plot model curve
        model_current_abs = np.abs(model_current)
        valid_mask = model_current_abs > 0
        
        if np.any(valid_mask):
            self.ax.semilogy(voltage[valid_mask], model_current_abs[valid_mask], 
                           'r-', linewidth=2, label='Model Curve', alpha=0.9)
        
        self.ax.legend()
        self.canvas.draw()
        
    def plot_simulation_results(self, sim_data):
        """Plot HSPICE simulation results"""
        # Remove existing simulation curve if present
        lines_to_remove = []
        for line in self.ax.lines:
            if line.get_label() == 'HSPICE Simulation':
                lines_to_remove.append(line)
        
        for line in lines_to_remove:
            line.remove()
            
        # Plot simulation results
        voltage = sim_data['voltage']
        current = np.abs(sim_data['current'])
        
        valid_mask = current > 0
        if np.any(valid_mask):
            self.ax.semilogy(voltage[valid_mask], current[valid_mask], 
                           'g--', linewidth=2, label='HSPICE Simulation', alpha=0.9)
        
        self.ax.legend()
        self.canvas.draw()
        
    def clear_plot(self):
        """Clear all plots"""
        self.ax.clear()
        self.setup_plot()
        self.canvas.draw()
