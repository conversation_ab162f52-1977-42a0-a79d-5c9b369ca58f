"""
Plot widget for displaying I-V characteristics
"""

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QCheckBox
from PyQt5.QtCore import pyqtSignal
import logging

logger = logging.getLogger(__name__)

class PlotWidget(QWidget):
    """
    Widget for plotting I-V characteristics with matplotlib
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_plot()
        
        # Data storage
        self.experimental_data = None
        self.fitted_data = None
        self.hspice_data = None
        
    def setup_ui(self):
        """
        Setup UI layout
        """
        layout = QVBoxLayout(self)
        
        # Create matplotlib figure and canvas
        self.figure = Figure(figsize=(10, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # Control panel
        control_layout = QHBoxLayout()
        
        # Plot options
        self.show_experimental_cb = QCheckBox("Experimental Data")
        self.show_experimental_cb.setChecked(True)
        self.show_experimental_cb.toggled.connect(self.update_plot)
        control_layout.addWidget(self.show_experimental_cb)
        
        self.show_fitted_cb = QCheckBox("Fitted Model")
        self.show_fitted_cb.setChecked(True)
        self.show_fitted_cb.toggled.connect(self.update_plot)
        control_layout.addWidget(self.show_fitted_cb)
        
        self.show_hspice_cb = QCheckBox("HSPICE Simulation")
        self.show_hspice_cb.setChecked(True)
        self.show_hspice_cb.toggled.connect(self.update_plot)
        control_layout.addWidget(self.show_hspice_cb)
        
        # Scale options
        self.log_scale_cb = QCheckBox("Log Scale")
        self.log_scale_cb.toggled.connect(self.update_plot)
        control_layout.addWidget(self.log_scale_cb)
        
        control_layout.addStretch()
        
        # Clear plot button
        clear_btn = QPushButton("Clear Plot")
        clear_btn.clicked.connect(self.clear_plot)
        control_layout.addWidget(clear_btn)
        
        layout.addLayout(control_layout)
        
    def setup_plot(self):
        """
        Setup initial plot
        """
        self.ax = self.figure.add_subplot(111)
        self.ax.set_xlabel('Voltage (V)')
        self.ax.set_ylabel('Current (A)')
        self.ax.set_title('BJT ESD Device I-V Characteristics')
        self.ax.grid(True, alpha=0.3)
        
        # Enable interactive features
        self.canvas.mpl_connect('button_press_event', self.on_plot_click)
        
        self.canvas.draw()
    
    def plot_experimental_data(self, voltage, current):
        """
        Plot experimental I-V data
        
        Args:
            voltage: Voltage array
            current: Current array
        """
        self.experimental_data = (voltage, current)
        self.update_plot()
        logger.info(f"Plotted experimental data with {len(voltage)} points")
    
    def plot_fitted_data(self, voltage, current):
        """
        Plot fitted model data
        
        Args:
            voltage: Voltage array
            current: Current array
        """
        self.fitted_data = (voltage, current)
        self.update_plot()
        logger.info(f"Plotted fitted data with {len(voltage)} points")
    
    def plot_hspice_data(self, voltage, current):
        """
        Plot HSPICE simulation data
        
        Args:
            voltage: Voltage array
            current: Current array
        """
        self.hspice_data = (voltage, current)
        self.update_plot()
        logger.info(f"Plotted HSPICE data with {len(voltage)} points")
    
    def update_plot(self):
        """
        Update plot with current data and settings
        """
        self.ax.clear()
        
        # Set labels and title
        self.ax.set_xlabel('Voltage (V)')
        self.ax.set_ylabel('Current (A)')
        self.ax.set_title('BJT ESD Device I-V Characteristics')
        self.ax.grid(True, alpha=0.3)
        
        # Plot experimental data
        if (self.experimental_data is not None and 
            self.show_experimental_cb.isChecked()):
            voltage, current = self.experimental_data
            self.ax.plot(voltage, current, 'bo', markersize=3, alpha=0.7,
                        label='Experimental Data')
        
        # Plot fitted data
        if (self.fitted_data is not None and 
            self.show_fitted_cb.isChecked()):
            voltage, current = self.fitted_data
            self.ax.plot(voltage, current, 'r-', linewidth=2,
                        label='Fitted Model')
        
        # Plot HSPICE data
        if (self.hspice_data is not None and 
            self.show_hspice_cb.isChecked()):
            voltage, current = self.hspice_data
            self.ax.plot(voltage, current, 'g--', linewidth=2,
                        label='HSPICE Simulation')
        
        # Set scale
        if self.log_scale_cb.isChecked():
            self.ax.set_yscale('log')
            # Ensure positive values for log scale
            self.ax.set_ylim(bottom=1e-12)
        else:
            self.ax.set_yscale('linear')
        
        # Add legend if there's data
        if (self.experimental_data is not None or 
            self.fitted_data is not None or 
            self.hspice_data is not None):
            self.ax.legend()
        
        # Adjust layout and redraw
        self.figure.tight_layout()
        self.canvas.draw()
    
    def clear_plot(self):
        """
        Clear all plot data
        """
        self.experimental_data = None
        self.fitted_data = None
        self.hspice_data = None
        self.update_plot()
        logger.info("Plot cleared")
    
    def on_plot_click(self, event):
        """
        Handle plot click events
        
        Args:
            event: Matplotlib mouse event
        """
        if event.inaxes != self.ax:
            return
        
        if event.button == 1 and event.dblclick:  # Double left click
            # Show coordinates
            x, y = event.xdata, event.ydata
            logger.info(f"Plot coordinates: V={x:.3f}V, I={y:.3e}A")
    
    def export_plot(self, filename):
        """
        Export plot to file
        
        Args:
            filename: Output filename
        """
        try:
            self.figure.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"Plot exported to {filename}")
        except Exception as e:
            logger.error(f"Failed to export plot: {str(e)}")
            raise
    
    def get_plot_data(self):
        """
        Get current plot data
        
        Returns:
            Dictionary with plot data
        """
        data = {}
        
        if self.experimental_data is not None:
            data['experimental'] = {
                'voltage': self.experimental_data[0],
                'current': self.experimental_data[1]
            }
        
        if self.fitted_data is not None:
            data['fitted'] = {
                'voltage': self.fitted_data[0],
                'current': self.fitted_data[1]
            }
        
        if self.hspice_data is not None:
            data['hspice'] = {
                'voltage': self.hspice_data[0],
                'current': self.hspice_data[1]
            }
        
        return data
    
    def set_plot_style(self, style='seaborn-v0_8'):
        """
        Set matplotlib style
        
        Args:
            style: Matplotlib style name
        """
        try:
            plt.style.use(style)
            self.update_plot()
        except Exception as e:
            logger.warning(f"Failed to set plot style {style}: {str(e)}")
    
    def zoom_to_region(self, voltage_range=None, current_range=None):
        """
        Zoom to specific voltage/current range
        
        Args:
            voltage_range: Tuple of (min_voltage, max_voltage)
            current_range: Tuple of (min_current, max_current)
        """
        if voltage_range:
            self.ax.set_xlim(voltage_range)
        
        if current_range:
            self.ax.set_ylim(current_range)
        
        self.canvas.draw()
    
    def auto_scale(self):
        """
        Auto-scale plot to fit all data
        """
        self.ax.relim()
        self.ax.autoscale()
        self.canvas.draw()
