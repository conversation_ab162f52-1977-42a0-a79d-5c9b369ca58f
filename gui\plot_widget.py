"""
Plot Widget for BJT ESD Parameter Extractor
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import Navigation<PERSON>oolbar2QT as NavigationToolbar
from matplotlib.figure import Figure
from PyQt5.QtWidgets import QWidget, QVBoxLayout

class PlotWidget(QWidget):
    """Widget for plotting measurement data and model curves"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize user interface"""
        layout = QVBoxLayout(self)

        # Create matplotlib figure and canvas
        self.figure = Figure(figsize=(12, 6))
        self.canvas = FigureCanvas(self.figure)

        # Create navigation toolbar
        self.toolbar = NavigationToolbar(self.canvas, self)

        # Add widgets to layout
        layout.addWidget(self.toolbar)
        layout.addWidget(self.canvas)

        # Create subplot
        self.ax = self.figure.add_subplot(111)
        self.setup_plot()

    def setup_plot(self):
        """Setup plot appearance and labels"""
        self.ax.set_xlabel('Voltage (V)', fontsize=12)
        self.ax.set_ylabel('Current (A)', fontsize=12)
        self.ax.set_title('BJT ESD Device I-V Characteristics', fontsize=14, fontweight='bold')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_yscale('log')

        # Set plot limits
        self.ax.set_xlim(0, 25)
        self.ax.set_ylim(1e-12, 10)

        self.canvas.draw()

    def plot_measurement_data(self, data):
        """Plot measurement data"""
        self.ax.clear()
        self.setup_plot()

        voltage = data['voltage']
        current = np.abs(data['current'])  # Use absolute value for log scale

        # Filter out zero and negative currents for log scale
        valid_mask = current > 0
        voltage_valid = voltage[valid_mask]
        current_valid = current[valid_mask]

        # Plot measurement data
        self.ax.semilogy(voltage_valid, current_valid, 'bo-',
                        markersize=4, linewidth=1.5,
                        label='Measurement Data', alpha=0.8)

        self.ax.legend()
        self.canvas.draw()

    def plot_model_curve(self, voltage, model_current):
        """Plot model curve alongside measurement data"""
        # Remove existing model curve if present
        lines_to_remove = []
        for line in self.ax.lines:
            if line.get_label() in ['Model Curve', 'HSPICE Simulation']:
                lines_to_remove.append(line)

        for line in lines_to_remove:
            line.remove()

        # Plot model curve
        model_current_abs = np.abs(model_current)
        valid_mask = model_current_abs > 0

        if np.any(valid_mask):
            self.ax.semilogy(voltage[valid_mask], model_current_abs[valid_mask],
                           'r-', linewidth=2, label='Model Curve', alpha=0.9)

        self.ax.legend()
        self.canvas.draw()

    def plot_simulation_results(self, sim_data):
        """Plot HSPICE simulation results"""
        # Remove existing simulation curve if present
        lines_to_remove = []
        for line in self.ax.lines:
            if line.get_label() == 'HSPICE Simulation':
                lines_to_remove.append(line)

        for line in lines_to_remove:
            line.remove()

        # Plot simulation results
        voltage = sim_data['voltage']
        current = np.abs(sim_data['current'])

        valid_mask = current > 0
        if np.any(valid_mask):
            self.ax.semilogy(voltage[valid_mask], current[valid_mask],
                           'g--', linewidth=2, label='HSPICE Simulation', alpha=0.9)

        self.ax.legend()
        self.canvas.draw()

    def plot_exact_conversion_comparison(self, voltage, measurement_data, fitted_current,
                                       behavioral_current=None, pwl_current=None,
                                       multidiode_current=None):
        """Plot comparison of exact conversion methods"""
        self.ax.clear()
        self.setup_plot()

        # Plot measurement data
        current = np.abs(measurement_data)
        valid_mask = current > 0
        voltage_valid = voltage[valid_mask]
        current_valid = current[valid_mask]

        self.ax.semilogy(voltage_valid, current_valid, 'ko-',
                        markersize=3, linewidth=1,
                        label='Measurement Data', alpha=0.7)

        # Plot fitted model
        fitted_valid = np.abs(fitted_current)
        valid_mask_fitted = fitted_valid > 0
        if np.any(valid_mask_fitted):
            self.ax.semilogy(voltage[valid_mask_fitted], fitted_valid[valid_mask_fitted],
                           'r-', linewidth=2, label='Fitted Model', alpha=0.9)

        # Plot exact conversion methods
        if behavioral_current is not None:
            behavioral_valid = np.abs(behavioral_current)
            valid_mask_beh = behavioral_valid > 0
            if np.any(valid_mask_beh):
                self.ax.semilogy(voltage[valid_mask_beh], behavioral_valid[valid_mask_beh],
                               'b-', linewidth=2, label='Behavioral SPICE', alpha=0.8)

        if pwl_current is not None:
            pwl_valid = np.abs(pwl_current)
            valid_mask_pwl = pwl_valid > 0
            if np.any(valid_mask_pwl):
                self.ax.semilogy(voltage[valid_mask_pwl], pwl_valid[valid_mask_pwl],
                               'g-', linewidth=2, label='PWL Model', alpha=0.8)

        if multidiode_current is not None:
            multi_valid = np.abs(multidiode_current)
            valid_mask_multi = multi_valid > 0
            if np.any(valid_mask_multi):
                self.ax.semilogy(voltage[valid_mask_multi], multi_valid[valid_mask_multi],
                               'm-', linewidth=2, label='Multi-Diode', alpha=0.8)

        self.ax.set_title('Exact Conversion Comparison', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.canvas.draw()

    def plot_conversion_error_analysis(self, voltage, log_errors, method_names):
        """Plot conversion error analysis"""
        self.ax.clear()

        # Setup error plot
        self.ax.set_xlabel('Voltage (V)', fontsize=12)
        self.ax.set_ylabel('Log Error', fontsize=12)
        self.ax.set_title('Conversion Error Analysis', fontsize=14, fontweight='bold')
        self.ax.grid(True, alpha=0.3)

        # Plot error curves
        colors = ['b', 'g', 'm', 'c', 'y']
        for i, (error, name) in enumerate(zip(log_errors, method_names)):
            color = colors[i % len(colors)]
            self.ax.semilogy(voltage, error, color=color, linewidth=2,
                           label='{} Error'.format(name), alpha=0.8)

        # Add tolerance line
        self.ax.axhline(y=0.1, color='r', linestyle='--', alpha=0.7,
                       label='Tolerance (10%)')

        self.ax.set_xlim(0, voltage.max())
        self.ax.legend()
        self.canvas.draw()

    def clear_plot(self):
        """Clear all plots"""
        self.ax.clear()
        self.setup_plot()
        self.canvas.draw()
