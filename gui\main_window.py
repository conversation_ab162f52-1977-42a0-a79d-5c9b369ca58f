"""
Main window for BJT ESD Parameter Extraction Tool
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QSplitter, QMenuBar, QToolBar, QAction, QFileDialog,
                            QMessageBox, QStatusBar, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QKeySequence
import sys
import os
import logging

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.plot_widget import PlotWidget
from gui.parameter_panel import ParameterPanel
from models.bjt_esd_model import BJTESDModel
from utils.data_loader import DataLoader
from utils.hspice_interface import HSPICEInterface
from config.settings import WINDOW_SIZE

logger = logging.getLogger(__name__)

class ParameterExtractionWorker(QThread):
    """
    Worker thread for parameter extraction to avoid GUI freezing
    """
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    progress = pyqtSignal(int)
    
    def __init__(self, model, voltage, current):
        super().__init__()
        self.model = model
        self.voltage = voltage
        self.current = current
    
    def run(self):
        try:
            self.progress.emit(50)
            fitted_params = self.model.fit_parameters(self.voltage, self.current)
            self.progress.emit(100)
            self.finished.emit(fitted_params)
        except Exception as e:
            self.error.emit(str(e))

class MainWindow(QMainWindow):
    """
    Main application window
    """
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("BJT ESD Parameter Extractor")
        self.setGeometry(100, 100, WINDOW_SIZE[0], WINDOW_SIZE[1])
        
        # Initialize components
        self.model = BJTESDModel()
        self.data_loader = DataLoader()
        self.hspice_interface = HSPICEInterface()
        
        # Data storage
        self.experimental_data = None
        self.fitted_data = None
        
        # Setup UI
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_tool_bar()
        self.setup_status_bar()
        
        # Connect signals
        self.connect_signals()
        
        logger.info("Main window initialized")
    
    def setup_ui(self):
        """
        Setup main UI layout
        """
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create splitter for plot and parameters
        splitter = QSplitter(Qt.Vertical)
        
        # Plot widget (top)
        self.plot_widget = PlotWidget()
        splitter.addWidget(self.plot_widget)
        
        # Parameter panel (bottom)
        self.parameter_panel = ParameterPanel(self.model)
        splitter.addWidget(self.parameter_panel)
        
        # Set splitter proportions
        splitter.setSizes([500, 300])
        
        main_layout.addWidget(splitter)
    
    def setup_menu_bar(self):
        """
        Setup menu bar
        """
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        # Open data file
        open_action = QAction('Open Data File...', self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.open_data_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # Export results
        export_action = QAction('Export Results...', self)
        export_action.setShortcut(QKeySequence.SaveAs)
        export_action.triggered.connect(self.export_results)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # Exit
        exit_action = QAction('Exit', self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Analysis menu
        analysis_menu = menubar.addMenu('Analysis')
        
        # Fit parameters
        fit_action = QAction('Fit Parameters', self)
        fit_action.setShortcut('Ctrl+F')
        fit_action.triggered.connect(self.fit_parameters)
        analysis_menu.addAction(fit_action)
        
        # Run HSPICE simulation
        hspice_action = QAction('Run HSPICE Simulation', self)
        hspice_action.setShortcut('Ctrl+H')
        hspice_action.triggered.connect(self.run_hspice_simulation)
        analysis_menu.addAction(hspice_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_tool_bar(self):
        """
        Setup tool bar
        """
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # Open file
        open_action = QAction('Open', self)
        open_action.setToolTip('Open data file')
        open_action.triggered.connect(self.open_data_file)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # Fit parameters
        fit_action = QAction('Fit', self)
        fit_action.setToolTip('Fit model parameters')
        fit_action.triggered.connect(self.fit_parameters)
        toolbar.addAction(fit_action)
        
        # HSPICE simulation
        hspice_action = QAction('HSPICE', self)
        hspice_action.setToolTip('Run HSPICE simulation')
        hspice_action.triggered.connect(self.run_hspice_simulation)
        toolbar.addAction(hspice_action)
        
        toolbar.addSeparator()
        
        # Reset parameters
        reset_action = QAction('Reset', self)
        reset_action.setToolTip('Reset parameters to default')
        reset_action.triggered.connect(self.reset_parameters)
        toolbar.addAction(reset_action)
    
    def setup_status_bar(self):
        """
        Setup status bar
        """
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        self.status_bar.showMessage("Ready")
    
    def connect_signals(self):
        """
        Connect signals between components
        """
        # Parameter panel signals
        self.parameter_panel.parameters_changed.connect(self.on_parameters_changed)
        self.parameter_panel.fit_requested.connect(self.fit_parameters)
        self.parameter_panel.reset_requested.connect(self.reset_parameters)
    
    def open_data_file(self):
        """
        Open and load experimental data file
        """
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Data File", "", 
            "CSV Files (*.csv);;Text Files (*.txt);;Data Files (*.dat);;All Files (*)"
        )
        
        if file_path:
            try:
                voltage, current, metadata = self.data_loader.load_data(file_path)
                
                if not self.data_loader.validate_data(voltage, current):
                    QMessageBox.warning(self, "Data Error", "Invalid data format or values")
                    return
                
                # Preprocess data
                voltage, current = self.data_loader.preprocess_data(voltage, current)
                
                # Store data
                self.experimental_data = (voltage, current)
                
                # Update plot
                self.plot_widget.plot_experimental_data(voltage, current)
                
                # Update parameters from metadata if available
                if metadata:
                    self.parameter_panel.update_parameters_from_metadata(metadata)
                
                self.status_bar.showMessage(f"Loaded {len(voltage)} data points from {os.path.basename(file_path)}")
                logger.info(f"Loaded data from {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load data file:\n{str(e)}")
                logger.error(f"Failed to load data file: {str(e)}")
    
    def fit_parameters(self):
        """
        Fit model parameters to experimental data
        """
        if self.experimental_data is None:
            QMessageBox.warning(self, "No Data", "Please load experimental data first")
            return
        
        voltage, current = self.experimental_data
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_bar.showMessage("Fitting parameters...")
        
        # Start parameter extraction in worker thread
        self.worker = ParameterExtractionWorker(self.model, voltage, current)
        self.worker.finished.connect(self.on_fitting_finished)
        self.worker.error.connect(self.on_fitting_error)
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.start()
    
    def on_fitting_finished(self, fitted_params):
        """
        Handle parameter fitting completion
        """
        self.progress_bar.setVisible(False)
        
        # Update model parameters
        self.model.update_parameters(fitted_params)
        
        # Update parameter panel
        self.parameter_panel.update_parameter_values(fitted_params)
        
        # Update plot with fitted curve
        if self.experimental_data:
            voltage, current = self.experimental_data
            fitted_current = self.model.current_equation(voltage, **fitted_params)
            self.plot_widget.plot_fitted_data(voltage, fitted_current)
        
        r_squared = fitted_params.get('r_squared', 0)
        self.status_bar.showMessage(f"Parameter fitting completed (R² = {r_squared:.4f})")
        logger.info(f"Parameter fitting completed with R² = {r_squared:.4f}")
    
    def on_fitting_error(self, error_msg):
        """
        Handle parameter fitting error
        """
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "Fitting Error", f"Parameter fitting failed:\n{error_msg}")
        self.status_bar.showMessage("Parameter fitting failed")
        logger.error(f"Parameter fitting failed: {error_msg}")
    
    def on_parameters_changed(self, parameters):
        """
        Handle parameter changes from parameter panel
        """
        # Update model
        self.model.update_parameters(parameters)
        
        # Update plot if experimental data is available
        if self.experimental_data:
            voltage, current = self.experimental_data
            model_current = self.model.current_equation(voltage, **parameters)
            self.plot_widget.plot_fitted_data(voltage, model_current)
    
    def run_hspice_simulation(self):
        """
        Run HSPICE simulation with current parameters
        """
        if not self.hspice_interface.verify_hspice_installation():
            QMessageBox.warning(self, "HSPICE Not Found", 
                              "HSPICE is not installed or not in PATH")
            return
        
        parameters = self.model.get_parameters()
        
        self.status_bar.showMessage("Running HSPICE simulation...")
        
        try:
            result = self.hspice_interface.run_simulation(parameters)
            
            if result:
                voltage, current = result
                self.plot_widget.plot_hspice_data(voltage, current)
                self.status_bar.showMessage("HSPICE simulation completed")
                logger.info("HSPICE simulation completed successfully")
            else:
                QMessageBox.warning(self, "Simulation Error", "HSPICE simulation failed")
                self.status_bar.showMessage("HSPICE simulation failed")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"HSPICE simulation error:\n{str(e)}")
            self.status_bar.showMessage("HSPICE simulation error")
            logger.error(f"HSPICE simulation error: {str(e)}")
    
    def reset_parameters(self):
        """
        Reset parameters to default values
        """
        self.parameter_panel.reset_parameters()
        self.status_bar.showMessage("Parameters reset to default values")
    
    def export_results(self):
        """
        Export fitting results
        """
        if self.experimental_data is None:
            QMessageBox.warning(self, "No Data", "No data to export")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Results", "", "CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            try:
                # Export implementation would go here
                self.status_bar.showMessage(f"Results exported to {os.path.basename(file_path)}")
                logger.info(f"Results exported to {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export results:\n{str(e)}")
                logger.error(f"Export error: {str(e)}")
    
    def show_about(self):
        """
        Show about dialog
        """
        QMessageBox.about(self, "About", 
                         "BJT ESD Parameter Extractor v1.0\n\n"
                         "A tool for extracting parameters from BJT ESD device I-V characteristics.\n\n"
                         "Features:\n"
                         "• Parameter fitting with curve optimization\n"
                         "• HSPICE simulation integration\n"
                         "• Interactive parameter adjustment\n"
                         "• Data visualization and export")
    
    def closeEvent(self, event):
        """
        Handle window close event
        """
        # Cleanup HSPICE interface
        if hasattr(self, 'hspice_interface'):
            self.hspice_interface.cleanup()
        
        event.accept()
