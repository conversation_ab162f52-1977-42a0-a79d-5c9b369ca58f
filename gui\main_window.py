"""
Main Window for BJT ESD Parameter Extractor
"""

import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QSplitter, QMenuBar, QMenu, QAction, QFileDialog,
                             QMessageBox, QStatusBar, QToolBar)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon

from .plot_widget import PlotWidget
from .parameter_widget import ParameterWidget
from data.data_loader import DataLoader
from data.parameter_extractor import ParameterExtractor
from models.spice_generator import SpiceGenerator
from models.exact_parameter_converter import ExactParameterConverter

class MainWindow(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.data_loader = DataLoader()
        self.parameter_extractor = ParameterExtractor()
        self.spice_generator = SpiceGenerator()
        self.exact_converter = ExactParameterConverter()

        self.current_data = None
        self.current_parameters = None
        self.conversion_results = None

        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("BJT ESD Parameter Extractor v1.0")
        self.setGeometry(100, 100, 1200, 800)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)

        # Create splitter for plot and parameters
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)

        # Create plot widget (top)
        self.plot_widget = PlotWidget()
        splitter.addWidget(self.plot_widget)

        # Create parameter widget (bottom)
        self.parameter_widget = ParameterWidget()
        splitter.addWidget(self.parameter_widget)

        # Set splitter proportions (70% plot, 30% parameters)
        splitter.setSizes([560, 240])

        # Create menu bar
        self.create_menu_bar()

        # Create toolbar
        self.create_toolbar()

        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Load data file to begin")

    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('File')

        # Load data action
        load_action = QAction('Load Data...', self)
        load_action.setShortcut('Ctrl+O')
        load_action.triggered.connect(self.load_data_file)
        file_menu.addAction(load_action)

        file_menu.addSeparator()

        # Save model action
        save_model_action = QAction('Save SPICE Model...', self)
        save_model_action.setShortcut('Ctrl+S')
        save_model_action.triggered.connect(self.save_spice_model)
        file_menu.addAction(save_model_action)

        # Save exact models action
        save_exact_models_action = QAction('Save Exact Models...', self)
        save_exact_models_action.setShortcut('Ctrl+Shift+S')
        save_exact_models_action.triggered.connect(self.save_exact_models)
        file_menu.addAction(save_exact_models_action)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        # Extract parameters action
        extract_action = QAction('Extract Parameters', self)
        extract_action.setShortcut('F5')
        extract_action.triggered.connect(self.extract_parameters)
        tools_menu.addAction(extract_action)

        # Run HSPICE simulation action
        simulate_action = QAction('Run HSPICE Simulation', self)
        simulate_action.setShortcut('F6')
        simulate_action.triggered.connect(self.run_hspice_simulation)
        tools_menu.addAction(simulate_action)

        tools_menu.addSeparator()

        # Exact conversion action
        exact_conversion_action = QAction('Exact Parameter Conversion', self)
        exact_conversion_action.setShortcut('F7')
        exact_conversion_action.triggered.connect(self.show_exact_conversion_dialog)
        tools_menu.addAction(exact_conversion_action)

        # Validate conversion action
        validate_conversion_action = QAction('Validate Model Conversion', self)
        validate_conversion_action.setShortcut('F8')
        validate_conversion_action.triggered.connect(self.validate_model_conversion)
        tools_menu.addAction(validate_conversion_action)

    def create_toolbar(self):
        """Create application toolbar"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)

        # Load data button
        load_action = QAction('Load Data', self)
        load_action.triggered.connect(self.load_data_file)
        toolbar.addAction(load_action)

        toolbar.addSeparator()

        # Extract parameters button
        extract_action = QAction('Extract Parameters', self)
        extract_action.triggered.connect(self.extract_parameters)
        toolbar.addAction(extract_action)

        # Save model button
        save_action = QAction('Save Model', self)
        save_action.triggered.connect(self.save_spice_model)
        toolbar.addAction(save_action)

        toolbar.addSeparator()

        # Simulate button
        simulate_action = QAction('HSPICE Simulation', self)
        simulate_action.triggered.connect(self.run_hspice_simulation)
        toolbar.addAction(simulate_action)

        toolbar.addSeparator()

        # Exact conversion button
        exact_conversion_action = QAction('Exact Conversion', self)
        exact_conversion_action.triggered.connect(self.show_exact_conversion_dialog)
        toolbar.addAction(exact_conversion_action)

        # Validate conversion button
        validate_action = QAction('Validate', self)
        validate_action.triggered.connect(self.validate_model_conversion)
        toolbar.addAction(validate_action)

    def connect_signals(self):
        """Connect widget signals"""
        # Connect parameter changes to plot updates
        self.parameter_widget.parameters_changed.connect(self.update_model_plot)

        # Connect exact conversion request
        self.parameter_widget.exact_conversion_requested.connect(self.show_exact_conversion_dialog)

    def load_data_file(self):
        """Load measurement data from CSV file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Load Measurement Data",
            "",
            "CSV Files (*.csv);;All Files (*)"
        )

        if file_path:
            try:
                self.current_data = self.data_loader.load_csv(file_path)
                self.plot_widget.plot_measurement_data(self.current_data)
                self.status_bar.showMessage(f"Loaded data from: {os.path.basename(file_path)}")

                # Auto-extract parameters
                self.extract_parameters()

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load data file:\n{str(e)}")

    def extract_parameters(self):
        """Extract BJT ESD parameters from measurement data"""
        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "Please load measurement data first")
            return

        try:
            # Extract parameters
            self.current_parameters = self.parameter_extractor.extract_parameters(self.current_data)

            # Update parameter widget
            self.parameter_widget.set_parameters(self.current_parameters)

            # Update plot with model
            self.update_model_plot()

            self.status_bar.showMessage("Parameters extracted successfully")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to extract parameters:\n{str(e)}")

    def update_model_plot(self):
        """Update plot with current model parameters"""
        if self.current_data is None:
            return

        try:
            # Get current parameters from widget
            parameters = self.parameter_widget.get_parameters()

            # Generate model curve
            voltage_range = self.current_data['voltage']
            model_current = self.parameter_extractor.calculate_model_current(voltage_range, parameters)

            # Update plot
            self.plot_widget.plot_model_curve(voltage_range, model_current)

        except Exception as e:
            print(f"Error updating model plot: {e}")

    def save_spice_model(self):
        """Save SPICE model to .ckt file"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save SPICE Model",
            "bjt_esd_model.ckt",
            "SPICE Circuit Files (*.ckt);;All Files (*)"
        )

        if file_path:
            try:
                # Get current parameters
                parameters = self.parameter_widget.get_parameters()

                # Generate SPICE model
                spice_content = self.spice_generator.generate_bjt_esd_model(parameters)

                # Save to file
                with open(file_path, 'w') as f:
                    f.write(spice_content)

                self.status_bar.showMessage(f"SPICE model saved to: {os.path.basename(file_path)}")
                QMessageBox.information(self, "Success", f"SPICE model saved successfully to:\n{file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save SPICE model:\n{str(e)}")

    def run_hspice_simulation(self):
        """Run HSPICE simulation with current model"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        try:
            from simulation.hspice_runner import HspiceRunner

            # Get current parameters
            parameters = self.parameter_widget.get_parameters()

            # Create HSPICE runner
            hspice_runner = HspiceRunner()

            # Run simulation
            sim_results = hspice_runner.run_simulation(parameters)

            if sim_results:
                # Plot simulation results
                self.plot_widget.plot_simulation_results(sim_results)
                self.status_bar.showMessage("HSPICE simulation completed successfully")
            else:
                QMessageBox.warning(self, "Warning", "HSPICE simulation failed or returned no results")

        except ImportError:
            QMessageBox.warning(self, "Warning", "HSPICE simulation module not available")
        except Exception as e:
            QMessageBox.critical(self, "Error", "HSPICE simulation failed:\n{}".format(str(e)))

    def show_exact_conversion_dialog(self):
        """Show exact parameter conversion dialog"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "Please load measurement data first")
            return

        try:
            from .exact_conversion_dialog import ExactConversionDialog

            dialog = ExactConversionDialog(
                self.current_data,
                self.current_parameters,
                parent=self
            )

            if dialog.exec_() == dialog.Accepted:
                # Get conversion results
                self.conversion_results = dialog.get_conversion_results()

                # Update status
                self.status_bar.showMessage("Exact conversion completed successfully")

                # Ask if user wants to plot comparison
                reply = QMessageBox.question(
                    self,
                    "Plot Comparison",
                    "Would you like to plot the conversion comparison?",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.plot_conversion_comparison()

        except ImportError:
            # Fallback to simple conversion
            self.run_simple_exact_conversion()
        except Exception as e:
            QMessageBox.critical(self, "Error", "Exact conversion failed:\n{}".format(str(e)))

    def run_simple_exact_conversion(self):
        """Run simple exact conversion without dialog"""
        try:
            # Set up exact converter
            voltage = self.current_data['voltage']
            current = self.current_data['current']

            self.exact_converter.set_target_data(voltage, current, self.current_parameters)

            # Generate all conversion methods
            results = self.exact_converter.validate_all_methods()

            # Show results
            success_count = sum(1 for r in results.values() if r['status'] == 'success')

            QMessageBox.information(
                self,
                "Exact Conversion Results",
                "Generated {} exact conversion models:\n\n"
                "Method 1 (Behavioral): {}\n"
                "Method 2 (PWL): {}\n"
                "Method 3 (Multi-diode): {}\n\n"
                "Check the generated .ckt files for details.".format(
                    success_count,
                    "✓" if results.get('method1', {}).get('status') == 'success' else "✗",
                    "✓" if results.get('method2', {}).get('status') == 'success' else "✗",
                    "✓" if results.get('method3', {}).get('status') == 'success' else "✗"
                )
            )

            self.conversion_results = results
            self.status_bar.showMessage("Exact conversion completed - {} methods successful".format(success_count))

        except Exception as e:
            QMessageBox.critical(self, "Error", "Simple exact conversion failed:\n{}".format(str(e)))

    def validate_model_conversion(self):
        """Validate model conversion quality"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "Please load measurement data first")
            return

        try:
            # Set up exact converter
            voltage = self.current_data['voltage']
            current = self.current_data['current']

            self.exact_converter.set_target_data(voltage, current, self.current_parameters)

            # Calculate fitted model current (for validation)
            _ = self.exact_converter._calculate_fitted_current_safe(voltage)

            # Calculate simple SPICE model current (from current implementation)
            from models.parameter_converter import ParameterConverter
            simple_converter = ParameterConverter()
            simple_converter.set_fitted_model(self.current_parameters, voltage, current)

            # Get direct conversion parameters
            spice_params = simple_converter.convert_to_spice_parameters(method='direct')
            validation = simple_converter.validate_conversion(spice_params)

            # Show validation results
            QMessageBox.information(
                self,
                "Model Conversion Validation",
                "Conversion Quality Assessment:\n\n"
                "Max Log Error: {:.4f}\n"
                "Mean Log Error: {:.4f}\n"
                "Within Tolerance: {}\n\n"
                "Quality Rating: {}\n\n"
                "Recommendation:\n{}".format(
                    validation['max_log_error'],
                    validation['mean_log_error'],
                    "Yes" if validation['within_tolerance'] else "No",
                    self._get_quality_rating(validation['max_log_error']),
                    self._get_quality_recommendation(validation['max_log_error'])
                )
            )

        except Exception as e:
            QMessageBox.critical(self, "Error", "Validation failed:\n{}".format(str(e)))

    def _get_quality_rating(self, max_log_error):
        """Get quality rating based on error"""
        if max_log_error < 0.05:
            return "Excellent (< 5%)"
        elif max_log_error < 0.1:
            return "Good (< 10%)"
        elif max_log_error < 0.2:
            return "Acceptable (< 20%)"
        else:
            return "Poor (> 20%)"

    def _get_quality_recommendation(self, max_log_error):
        """Get recommendation based on error"""
        if max_log_error < 0.05:
            return "Current conversion is excellent. Ready for use."
        elif max_log_error < 0.1:
            return "Good conversion quality. Consider exact conversion for critical applications."
        elif max_log_error < 0.2:
            return "Acceptable for general use. Recommend exact conversion for better accuracy."
        else:
            return "Poor conversion quality. Strongly recommend using exact conversion methods."

    def save_exact_models(self):
        """Save all exact conversion models"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "Please load measurement data first")
            return

        # Choose directory
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Directory for Exact Models",
            "",
            QFileDialog.ShowDirsOnly
        )

        if directory:
            try:
                # Set up exact converter
                voltage = self.current_data['voltage']
                current = self.current_data['current']

                self.exact_converter.set_target_data(voltage, current, self.current_parameters)

                # Generate all models
                results = self.exact_converter.validate_all_methods()

                # Move generated files to selected directory
                import shutil
                files_moved = []

                for method, result in results.items():
                    if result['status'] == 'success' and 'file' in result:
                        src_file = result['file']
                        if os.path.exists(src_file):
                            dst_file = os.path.join(directory, src_file)
                            shutil.move(src_file, dst_file)
                            files_moved.append(dst_file)

                # Also move comparison script if it exists
                script_file = "compare_all_methods.bat"
                if os.path.exists(script_file):
                    dst_script = os.path.join(directory, script_file)
                    shutil.move(script_file, dst_script)
                    files_moved.append(dst_script)

                # Show success message
                QMessageBox.information(
                    self,
                    "Success",
                    "Exact models saved successfully!\n\n"
                    "Files saved to: {}\n\n"
                    "Generated files:\n{}".format(
                        directory,
                        "\n".join([os.path.basename(f) for f in files_moved])
                    )
                )

                self.status_bar.showMessage("Exact models saved to: {}".format(directory))

            except Exception as e:
                QMessageBox.critical(self, "Error", "Failed to save exact models:\n{}".format(str(e)))

    def plot_conversion_comparison(self):
        """Plot comparison of different conversion methods"""
        if self.conversion_results is None:
            QMessageBox.warning(self, "Warning", "No conversion results available")
            return

        try:
            # Get data for comparison
            voltage = self.current_data['voltage']
            current = self.current_data['current']

            # Calculate fitted model current
            fitted_current = self.exact_converter._calculate_fitted_current_safe(voltage)

            # Plot the comparison
            self.plot_widget.plot_exact_conversion_comparison(
                voltage, current, fitted_current
            )

            # Update status
            self.status_bar.showMessage("Conversion comparison plotted")

            # Show information about the comparison
            QMessageBox.information(
                self,
                "Conversion Comparison",
                "Exact conversion comparison displayed in plot area.\n\n"
                "The plot shows:\n"
                "• Original measurement data (black circles)\n"
                "• Fitted mathematical model (red line)\n"
                "• Exact conversion methods (colored lines)\n\n"
                "All exact conversion methods should perfectly\n"
                "match the fitted model within their domains!"
            )

        except Exception as e:
            QMessageBox.critical(self, "Error", "Failed to plot comparison:\n{}".format(str(e)))
