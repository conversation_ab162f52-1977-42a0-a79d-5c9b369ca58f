"""
Main Window for BJT ESD Parameter Extractor
"""

import os
import numpy as np
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QSplitter, QMenuBar, QMenu, QAction, QFileDialog,
                             QMessageBox, QStatusBar, QToolBar, QProgressDialog,
                             QApplication)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon

from .plot_widget import PlotWidget
from .parameter_widget import ParameterWidget
from data.data_loader import DataLoader
from data.parameter_extractor import ParameterExtractor
from data.improved_parameter_extractor import ImprovedParameterExtractor
from models.spice_generator import SpiceGenerator
from models.fixed_spice_generator import FixedSpiceGenerator
from models.hspice_compatible_generator import HspiceCompatibleGenerator
from models.exact_parameter_converter import ExactParameterConverter

class MainWindow(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.data_loader = DataLoader()
        self.parameter_extractor = ParameterExtractor()
        self.improved_extractor = ImprovedParameterExtractor()
        self.spice_generator = SpiceGenerator()
        self.fixed_spice_generator = FixedSpiceGenerator()
        self.hspice_generator = HspiceCompatibleGenerator()
        self.exact_converter = ExactParameterConverter()

        self.current_data = None
        self.current_parameters = None
        self.conversion_results = None

        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("BJT ESD Parameter Extractor v1.0")
        self.setGeometry(100, 100, 1200, 800)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)

        # Create splitter for plot and parameters
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)

        # Create plot widget (top)
        self.plot_widget = PlotWidget()
        splitter.addWidget(self.plot_widget)

        # Create parameter widget (bottom)
        self.parameter_widget = ParameterWidget()
        splitter.addWidget(self.parameter_widget)

        # Set splitter proportions (70% plot, 30% parameters)
        splitter.setSizes([560, 240])

        # Create menu bar
        self.create_menu_bar()

        # Create toolbar
        self.create_toolbar()

        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Load data file to begin")

    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('File')

        # Load data action
        load_action = QAction('Load Data...', self)
        load_action.setShortcut('Ctrl+O')
        load_action.triggered.connect(self.load_data_file)
        file_menu.addAction(load_action)

        file_menu.addSeparator()

        # Save model action
        save_model_action = QAction('Save SPICE Model...', self)
        save_model_action.setShortcut('Ctrl+S')
        save_model_action.triggered.connect(self.save_spice_model)
        file_menu.addAction(save_model_action)

        # Save exact models action
        save_exact_models_action = QAction('Save Exact Models...', self)
        save_exact_models_action.setShortcut('Ctrl+Shift+S')
        save_exact_models_action.triggered.connect(self.save_exact_models)
        file_menu.addAction(save_exact_models_action)

        # Save fixed SPICE model action
        save_fixed_model_action = QAction('Save Fixed SPICE Model...', self)
        save_fixed_model_action.setShortcut('Ctrl+Alt+S')
        save_fixed_model_action.triggered.connect(self.save_fixed_spice_model)
        file_menu.addAction(save_fixed_model_action)

        # Save HSPICE compatible model action
        save_hspice_model_action = QAction('Save HSPICE Compatible Model...', self)
        save_hspice_model_action.setShortcut('Ctrl+Shift+H')
        save_hspice_model_action.triggered.connect(self.save_hspice_compatible_model)
        file_menu.addAction(save_hspice_model_action)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        # Auto-fit parameters action
        autofit_action = QAction('Auto-Fit Parameters', self)
        autofit_action.setShortcut('F5')
        autofit_action.triggered.connect(self.auto_fit_parameters)
        tools_menu.addAction(autofit_action)

        # Run HSPICE simulation action
        simulate_action = QAction('Run HSPICE Simulation', self)
        simulate_action.setShortcut('F6')
        simulate_action.triggered.connect(self.run_hspice_simulation)
        tools_menu.addAction(simulate_action)

        # Run HSPICE with custom file action
        simulate_custom_action = QAction('Run HSPICE with Custom File...', self)
        simulate_custom_action.setShortcut('Ctrl+F6')
        simulate_custom_action.triggered.connect(self.run_hspice_custom_file)
        tools_menu.addAction(simulate_custom_action)

        # Compare HSPICE models action
        compare_models_action = QAction('Compare HSPICE Models...', self)
        compare_models_action.setShortcut('Shift+F6')
        compare_models_action.triggered.connect(self.compare_hspice_models)
        tools_menu.addAction(compare_models_action)

        tools_menu.addSeparator()

        # Exact conversion action
        exact_conversion_action = QAction('Exact Parameter Conversion', self)
        exact_conversion_action.setShortcut('F7')
        exact_conversion_action.triggered.connect(self.show_exact_conversion_dialog)
        tools_menu.addAction(exact_conversion_action)

        # Validate conversion action
        validate_conversion_action = QAction('Validate Model Conversion', self)
        validate_conversion_action.setShortcut('F8')
        validate_conversion_action.triggered.connect(self.validate_model_conversion)
        tools_menu.addAction(validate_conversion_action)

    def create_toolbar(self):
        """Create application toolbar"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)

        # Load data button
        load_action = QAction('Load Data', self)
        load_action.triggered.connect(self.load_data_file)
        toolbar.addAction(load_action)

        toolbar.addSeparator()

        # Auto-fit parameters button
        autofit_action = QAction('Auto-Fit Parameters', self)
        autofit_action.triggered.connect(self.auto_fit_parameters)
        toolbar.addAction(autofit_action)

        # Save model button
        save_action = QAction('Save Model', self)
        save_action.triggered.connect(self.save_spice_model)
        toolbar.addAction(save_action)

        toolbar.addSeparator()

        # Simulate button
        simulate_action = QAction('HSPICE Simulation', self)
        simulate_action.triggered.connect(self.run_hspice_simulation)
        toolbar.addAction(simulate_action)

        # Custom HSPICE file button
        custom_simulate_action = QAction('HSPICE Custom', self)
        custom_simulate_action.triggered.connect(self.run_hspice_custom_file)
        toolbar.addAction(custom_simulate_action)

        # Compare models button
        compare_action = QAction('Compare Models', self)
        compare_action.triggered.connect(self.compare_hspice_models)
        toolbar.addAction(compare_action)

        toolbar.addSeparator()

        # Exact conversion button
        exact_conversion_action = QAction('Exact Conversion', self)
        exact_conversion_action.triggered.connect(self.show_exact_conversion_dialog)
        toolbar.addAction(exact_conversion_action)

        # Validate conversion button
        validate_action = QAction('Validate', self)
        validate_action.triggered.connect(self.validate_model_conversion)
        toolbar.addAction(validate_action)

    def connect_signals(self):
        """Connect widget signals"""
        # Connect parameter changes to plot updates
        self.parameter_widget.parameters_changed.connect(self.update_model_plot)

        # Connect exact conversion request
        self.parameter_widget.exact_conversion_requested.connect(self.show_exact_conversion_dialog)

        # Connect auto-fit request
        self.parameter_widget.auto_fit_requested.connect(self.auto_fit_parameters)

    def load_data_file(self):
        """Load measurement data from CSV file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Load Measurement Data",
            "",
            "CSV Files (*.csv);;All Files (*)"
        )

        if file_path:
            try:
                self.current_data = self.data_loader.load_csv(file_path)
                self.plot_widget.plot_measurement_data(self.current_data)
                self.status_bar.showMessage(f"Loaded data from: {os.path.basename(file_path)}")

                # Auto-fit parameters
                self.auto_fit_parameters()

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load data file:\n{str(e)}")

    def auto_fit_parameters(self):
        """Auto-fit BJT ESD parameters from measurement data with intelligent optimization"""
        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "Please load measurement data first")
            return

        try:
            # Show progress dialog for auto-fitting

            progress = QProgressDialog("Auto-fitting parameters...", "Cancel", 0, 100, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # Step 1: Initial parameter extraction
            progress.setValue(20)
            progress.setLabelText("Extracting initial parameters...")
            QApplication.processEvents()

            # Try improved extractor first, fallback to original if needed
            try:
                self.current_parameters = self.improved_extractor.extract_parameters(self.current_data)
                print("Using improved parameter extractor")
            except Exception as e:
                print(f"Improved extractor failed: {e}, falling back to original")
                self.current_parameters = self.parameter_extractor.extract_parameters(self.current_data)

            # Step 2: Parameter optimization
            progress.setValue(50)
            progress.setLabelText("Optimizing parameters...")
            QApplication.processEvents()

            # Enhanced parameter optimization
            optimized_params = self._optimize_parameters(self.current_parameters)
            if optimized_params:
                self.current_parameters = optimized_params

            # Step 3: Update UI
            progress.setValue(80)
            progress.setLabelText("Updating interface...")
            QApplication.processEvents()

            # Update parameter widget
            self.parameter_widget.set_parameters(self.current_parameters)

            # Update plot with model
            self.update_model_plot()

            progress.setValue(100)
            progress.close()

            # Show results summary
            self._show_autofit_results()

            self.status_bar.showMessage("Parameters auto-fitted successfully")

        except Exception as e:
            if 'progress' in locals():
                progress.close()
            QMessageBox.critical(self, "Error", "Failed to auto-fit parameters:\n{}".format(str(e)))

    def _optimize_parameters(self, initial_params):
        """Optimize parameters using advanced fitting techniques"""
        try:
            # This could include more sophisticated optimization
            # For now, return the initial parameters
            # Future enhancement: implement scipy.optimize or similar

            # Basic parameter validation and adjustment
            optimized = initial_params.copy()

            # Ensure reasonable parameter ranges
            if optimized.get('I_leak', 0) <= 0:
                optimized['I_leak'] = 1e-9
            if optimized.get('Vt1', 0) <= 0:
                optimized['Vt1'] = 12.0
            if optimized.get('k', 0) <= 0:
                optimized['k'] = 3.0
            if optimized.get('Ron', 0) <= 0:
                optimized['Ron'] = 2.0

            return optimized

        except Exception as e:
            print("Parameter optimization failed:", e)
            return initial_params

    def _show_autofit_results(self):
        """Show auto-fit results summary"""
        if not self.current_parameters:
            return

        # Calculate fitting quality
        try:
            voltage = self.current_data['voltage']
            current = self.current_data['current']
            try:
                model_current = self.improved_extractor.calculate_model_current(voltage, self.current_parameters)
            except:
                model_current = self.parameter_extractor.calculate_model_current(voltage, self.current_parameters)

            # Calculate R-squared or similar metric
            correlation = np.corrcoef(current, model_current)[0, 1]
            r_squared = correlation ** 2 if not np.isnan(correlation) else 0.0

            # Format parameters for display
            param_text = ""
            for param, value in self.current_parameters.items():
                if 'I_' in param or 'Isb' in param:
                    param_text += "  {}: {:.3e}\n".format(param, value)
                else:
                    param_text += "  {}: {:.3f}\n".format(param, value)

            QMessageBox.information(
                self,
                "Auto-Fit Results",
                "Parameter auto-fitting completed successfully!\n\n"
                "Extracted Parameters:\n{}\n"
                "Fitting Quality: R² = {:.4f}\n\n"
                "You can now:\n"
                "• Fine-tune parameters using sliders\n"
                "• Generate SPICE models\n"
                "• Run exact parameter conversion\n"
                "• Validate with HSPICE simulation".format(
                    param_text, r_squared
                )
            )

        except Exception as e:
            print("Error showing autofit results:", e)

    def update_model_plot(self):
        """Update plot with current model parameters"""
        if self.current_data is None:
            return

        try:
            # Get current parameters from widget
            parameters = self.parameter_widget.get_parameters()

            # Generate model curve using improved extractor if available
            voltage_range = self.current_data['voltage']
            try:
                model_current = self.improved_extractor.calculate_model_current(voltage_range, parameters)
            except:
                model_current = self.parameter_extractor.calculate_model_current(voltage_range, parameters)

            # Update plot
            self.plot_widget.plot_model_curve(voltage_range, model_current)

        except Exception as e:
            print(f"Error updating model plot: {e}")

    def save_spice_model(self):
        """Save SPICE model to .ckt file"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save SPICE Model",
            "bjt_esd_model.ckt",
            "SPICE Circuit Files (*.ckt);;All Files (*)"
        )

        if file_path:
            try:
                # Get current parameters
                parameters = self.parameter_widget.get_parameters()

                # Generate SPICE model
                spice_content = self.spice_generator.generate_bjt_esd_model(parameters)

                # Save to file
                with open(file_path, 'w') as f:
                    f.write(spice_content)

                self.status_bar.showMessage(f"SPICE model saved to: {os.path.basename(file_path)}")
                QMessageBox.information(self, "Success", f"SPICE model saved successfully to:\n{file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save SPICE model:\n{str(e)}")

    def run_hspice_simulation(self):
        """Run HSPICE simulation with current model"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        try:
            from simulation.hspice_runner import HspiceRunner

            # Get current parameters
            parameters = self.parameter_widget.get_parameters()

            # Create HSPICE runner
            hspice_runner = HspiceRunner()

            # Run simulation
            sim_results = hspice_runner.run_simulation(parameters)

            if sim_results:
                # Plot simulation results
                self.plot_widget.plot_simulation_results(sim_results)
                self.status_bar.showMessage("HSPICE simulation completed successfully")
            else:
                QMessageBox.warning(self, "Warning", "HSPICE simulation failed or returned no results")

        except ImportError:
            QMessageBox.warning(self, "Warning", "HSPICE simulation module not available")
        except Exception as e:
            QMessageBox.critical(self, "Error", "HSPICE simulation failed:\n{}".format(str(e)))

    def show_exact_conversion_dialog(self):
        """Show exact parameter conversion dialog"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "Please load measurement data first")
            return

        try:
            from .exact_conversion_dialog import ExactConversionDialog

            dialog = ExactConversionDialog(
                self.current_data,
                self.current_parameters,
                parent=self
            )

            if dialog.exec_() == dialog.Accepted:
                # Get conversion results
                self.conversion_results = dialog.get_conversion_results()

                # Update status
                self.status_bar.showMessage("Exact conversion completed successfully")

                # Ask if user wants to plot comparison
                reply = QMessageBox.question(
                    self,
                    "Plot Comparison",
                    "Would you like to plot the conversion comparison?",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.plot_conversion_comparison()

        except ImportError:
            # Fallback to simple conversion
            self.run_simple_exact_conversion()
        except Exception as e:
            QMessageBox.critical(self, "Error", "Exact conversion failed:\n{}".format(str(e)))

    def run_simple_exact_conversion(self):
        """Run simple exact conversion without dialog"""
        try:
            # Set up exact converter
            voltage = self.current_data['voltage']
            current = self.current_data['current']

            self.exact_converter.set_target_data(voltage, current, self.current_parameters)

            # Generate all conversion methods
            results = self.exact_converter.validate_all_methods()

            # Show results
            success_count = sum(1 for r in results.values() if r['status'] == 'success')

            QMessageBox.information(
                self,
                "Exact Conversion Results",
                "Generated {} exact conversion models:\n\n"
                "Method 1 (Behavioral): {}\n"
                "Method 2 (PWL): {}\n"
                "Method 3 (Multi-diode): {}\n\n"
                "Check the generated .ckt files for details.".format(
                    success_count,
                    "✓" if results.get('method1', {}).get('status') == 'success' else "✗",
                    "✓" if results.get('method2', {}).get('status') == 'success' else "✗",
                    "✓" if results.get('method3', {}).get('status') == 'success' else "✗"
                )
            )

            self.conversion_results = results
            self.status_bar.showMessage("Exact conversion completed - {} methods successful".format(success_count))

        except Exception as e:
            QMessageBox.critical(self, "Error", "Simple exact conversion failed:\n{}".format(str(e)))

    def validate_model_conversion(self):
        """Validate model conversion quality"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "Please load measurement data first")
            return

        try:
            # Set up exact converter
            voltage = self.current_data['voltage']
            current = self.current_data['current']

            self.exact_converter.set_target_data(voltage, current, self.current_parameters)

            # Calculate fitted model current (for validation)
            _ = self.exact_converter._calculate_fitted_current_safe(voltage)

            # Calculate simple SPICE model current (from current implementation)
            from models.parameter_converter import ParameterConverter
            simple_converter = ParameterConverter()
            simple_converter.set_fitted_model(self.current_parameters, voltage, current)

            # Get direct conversion parameters
            spice_params = simple_converter.convert_to_spice_parameters(method='direct')
            validation = simple_converter.validate_conversion(spice_params)

            # Show validation results
            QMessageBox.information(
                self,
                "Model Conversion Validation",
                "Conversion Quality Assessment:\n\n"
                "Max Log Error: {:.4f}\n"
                "Mean Log Error: {:.4f}\n"
                "Within Tolerance: {}\n\n"
                "Quality Rating: {}\n\n"
                "Recommendation:\n{}".format(
                    validation['max_log_error'],
                    validation['mean_log_error'],
                    "Yes" if validation['within_tolerance'] else "No",
                    self._get_quality_rating(validation['max_log_error']),
                    self._get_quality_recommendation(validation['max_log_error'])
                )
            )

        except Exception as e:
            QMessageBox.critical(self, "Error", "Validation failed:\n{}".format(str(e)))

    def _get_quality_rating(self, max_log_error):
        """Get quality rating based on error"""
        if max_log_error < 0.05:
            return "Excellent (< 5%)"
        elif max_log_error < 0.1:
            return "Good (< 10%)"
        elif max_log_error < 0.2:
            return "Acceptable (< 20%)"
        else:
            return "Poor (> 20%)"

    def _get_quality_recommendation(self, max_log_error):
        """Get recommendation based on error"""
        if max_log_error < 0.05:
            return "Current conversion is excellent. Ready for use."
        elif max_log_error < 0.1:
            return "Good conversion quality. Consider exact conversion for critical applications."
        elif max_log_error < 0.2:
            return "Acceptable for general use. Recommend exact conversion for better accuracy."
        else:
            return "Poor conversion quality. Strongly recommend using exact conversion methods."

    def save_exact_models(self):
        """Save all exact conversion models"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "Please load measurement data first")
            return

        # Choose directory
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Directory for Exact Models",
            "",
            QFileDialog.ShowDirsOnly
        )

        if directory:
            try:
                # Set up exact converter
                voltage = self.current_data['voltage']
                current = self.current_data['current']

                self.exact_converter.set_target_data(voltage, current, self.current_parameters)

                # Generate all models
                results = self.exact_converter.validate_all_methods()

                # Move generated files to selected directory
                import shutil
                files_moved = []

                for method, result in results.items():
                    if result['status'] == 'success' and 'file' in result:
                        src_file = result['file']
                        if os.path.exists(src_file):
                            dst_file = os.path.join(directory, src_file)
                            shutil.move(src_file, dst_file)
                            files_moved.append(dst_file)

                # Also move comparison script if it exists
                script_file = "compare_all_methods.bat"
                if os.path.exists(script_file):
                    dst_script = os.path.join(directory, script_file)
                    shutil.move(script_file, dst_script)
                    files_moved.append(dst_script)

                # Show success message
                QMessageBox.information(
                    self,
                    "Success",
                    "Exact models saved successfully!\n\n"
                    "Files saved to: {}\n\n"
                    "Generated files:\n{}".format(
                        directory,
                        "\n".join([os.path.basename(f) for f in files_moved])
                    )
                )

                self.status_bar.showMessage("Exact models saved to: {}".format(directory))

            except Exception as e:
                QMessageBox.critical(self, "Error", "Failed to save exact models:\n{}".format(str(e)))

    def plot_conversion_comparison(self):
        """Plot comparison of different conversion methods"""
        if self.conversion_results is None:
            QMessageBox.warning(self, "Warning", "No conversion results available")
            return

        try:
            # Get data for comparison
            voltage = self.current_data['voltage']
            current = self.current_data['current']

            # Calculate fitted model current
            fitted_current = self.exact_converter._calculate_fitted_current_safe(voltage)

            # Plot the comparison
            self.plot_widget.plot_exact_conversion_comparison(
                voltage, current, fitted_current
            )

            # Update status
            self.status_bar.showMessage("Conversion comparison plotted")

            # Show information about the comparison
            QMessageBox.information(
                self,
                "Conversion Comparison",
                "Exact conversion comparison displayed in plot area.\n\n"
                "The plot shows:\n"
                "• Original measurement data (black circles)\n"
                "• Fitted mathematical model (red line)\n"
                "• Exact conversion methods (colored lines)\n\n"
                "All exact conversion methods should perfectly\n"
                "match the fitted model within their domains!"
            )

        except Exception as e:
            QMessageBox.critical(self, "Error", "Failed to plot comparison:\n{}".format(str(e)))

    def save_fixed_spice_model(self):
        """Save fixed SPICE model with exact parameter transfer"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        try:
            # Choose model type
            from PyQt5.QtWidgets import QInputDialog

            model_types = ["behavioral", "hybrid", "diode"]
            model_type, ok = QInputDialog.getItem(
                self,
                "Select Model Type",
                "Choose SPICE model type for exact parameter transfer:",
                model_types,
                0,  # Default to behavioral
                False
            )

            if not ok:
                return

            # Set model type
            self.fixed_spice_generator.set_model_type(model_type)

            # Choose filename
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Save Fixed SPICE Model",
                f"bjt_esd_fixed_{model_type}.ckt",
                "SPICE Circuit Files (*.ckt);;All Files (*)"
            )

            if filename:
                # Generate fixed SPICE model
                self.fixed_spice_generator.generate_spice_model(
                    self.current_parameters,
                    filename
                )

                # Compare models to verify exact transfer
                error = self.fixed_spice_generator.compare_models(self.current_parameters)

                # Show success message with quality info
                QMessageBox.information(
                    self,
                    "Fixed SPICE Model Saved",
                    f"Fixed SPICE model saved successfully!\n\n"
                    f"File: {filename}\n"
                    f"Model type: {model_type}\n"
                    f"Parameter transfer error: {error:.2e}\n\n"
                    f"This model ensures exact parameter transfer\n"
                    f"from fitted model to SPICE simulation.\n\n"
                    f"Use this file for accurate HSPICE simulation!"
                )

                self.status_bar.showMessage(f"Fixed SPICE model saved: {filename}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save fixed SPICE model:\n{str(e)}")

    def save_hspice_compatible_model(self):
        """Save HSPICE compatible model with proper syntax"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return

        try:
            # Choose model type
            from PyQt5.QtWidgets import QInputDialog

            model_types = ["pwl", "diode_corrected", "table", "simple"]
            model_descriptions = [
                "pwl - Piecewise Linear (Exact I-V curve)",
                "diode_corrected - Fixed Diode Model",
                "table - Table Lookup (Exact)",
                "simple - Basic Resistor+Diode"
            ]

            model_type, ok = QInputDialog.getItem(
                self,
                "Select HSPICE Model Type",
                "Choose HSPICE-compatible model type:\n\n" +
                "\n".join(model_descriptions) +
                "\n\nRecommended: PWL for exact matching",
                model_types,
                0,  # Default to PWL
                False
            )

            if not ok:
                return

            # Set model type
            self.hspice_generator.set_model_type(model_type)

            # Choose filename
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Save HSPICE Compatible Model",
                f"bjt_esd_hspice_{model_type}.ckt",
                "SPICE Circuit Files (*.ckt);;All Files (*)"
            )

            if filename:
                # Generate HSPICE compatible model
                self.hspice_generator.generate_hspice_model(
                    self.current_parameters,
                    filename
                )

                # Compare with fitted model
                self.hspice_generator.compare_with_fitted(self.current_parameters)

                # Show success message
                QMessageBox.information(
                    self,
                    "HSPICE Compatible Model Saved",
                    f"HSPICE compatible model saved successfully!\n\n"
                    f"File: {filename}\n"
                    f"Model type: {model_type}\n\n"
                    f"This model uses HSPICE-compatible syntax:\n"
                    f"• No behavioral if() expressions\n"
                    f"• Fixed parameter scaling issues\n"
                    f"• Proper HSPICE syntax\n\n"
                    f"Use this file for HSPICE simulation!"
                )

                self.status_bar.showMessage(f"HSPICE compatible model saved: {filename}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save HSPICE compatible model:\n{str(e)}")

    def run_hspice_custom_file(self):
        """Run HSPICE simulation with user-selected .ckt file"""
        # Select .ckt file
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select HSPICE Circuit File",
            "",
            "SPICE Circuit Files (*.ckt *.sp *.cir);;All Files (*)"
        )

        if not file_path:
            return

        try:
            from simulation.hspice_runner import HspiceRunner

            # Create HSPICE runner
            hspice_runner = HspiceRunner()

            # Check HSPICE installation
            if not hspice_runner.check_hspice_installation():
                QMessageBox.critical(
                    self,
                    "HSPICE Error",
                    "HSPICE not found or not properly installed.\n"
                    "Please check your HSPICE installation and PATH settings."
                )
                return

            # Show simulation options dialog
            options = self.show_hspice_options_dialog(file_path)
            if not options:
                return

            # Run simulation
            self.status_bar.showMessage("Running HSPICE simulation...")

            # Use custom simulation method
            sim_results = self.run_custom_hspice_simulation(
                file_path,
                options['output_file'],
                options['keep_files']
            )

            if sim_results:
                # Plot simulation results
                self.plot_widget.plot_simulation_results(sim_results)
                self.status_bar.showMessage("HSPICE simulation completed successfully")

                # Show results summary
                QMessageBox.information(
                    self,
                    "Simulation Complete",
                    "HSPICE simulation completed successfully!\n\n"
                    "Input file: {}\n"
                    "Output file: {}\n"
                    "Data points: {}\n\n"
                    "Results are plotted in the main window.".format(
                        os.path.basename(file_path),
                        options['output_file'],
                        len(sim_results.get('voltage', []))
                    )
                )
            else:
                QMessageBox.warning(
                    self,
                    "Simulation Failed",
                    "HSPICE simulation failed or returned no results.\n"
                    "Please check the circuit file and HSPICE output."
                )

        except ImportError:
            QMessageBox.warning(self, "Warning", "HSPICE simulation module not available")
        except Exception as e:
            QMessageBox.critical(self, "Error", "HSPICE simulation failed:\n{}".format(str(e)))

    def show_hspice_options_dialog(self, input_file):
        """Show HSPICE simulation options dialog"""
        from .hspice_options_dialog import HspiceOptionsDialog

        try:
            dialog = HspiceOptionsDialog(input_file, parent=self)
            if dialog.exec_() == dialog.Accepted:
                return dialog.get_options()
            else:
                return None
        except ImportError:
            # Fallback to simple options
            return self.get_simple_hspice_options(input_file)

    def get_simple_hspice_options(self, input_file):
        """Get simple HSPICE options without dialog"""
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = "{}_results.lis".format(base_name)

        return {
            'output_file': output_file,
            'keep_files': True,
            'analysis_type': 'dc',
            'voltage_range': (0, 20),
            'voltage_step': 0.1
        }

    def run_custom_hspice_simulation(self, input_file, output_file, keep_files=True):
        """Run custom HSPICE simulation in current directory"""
        try:
            from simulation.hspice_runner import HspiceRunner

            hspice_runner = HspiceRunner()

            # Run HSPICE command in current directory
            import subprocess
            import os

            # Use current directory for simulation
            work_dir = os.getcwd()

            # Ensure output file is in current directory
            if not os.path.isabs(output_file):
                output_file = os.path.join(work_dir, output_file)

            try:
                # Run HSPICE directly in current directory
                cmd = [hspice_runner.hspice_path, input_file, "-o", output_file]

                print(f"Running HSPICE command: {' '.join(cmd)}")
                print(f"Working directory: {work_dir}")
                print(f"Input file: {input_file}")
                print(f"Output file: {output_file}")

                result = subprocess.run(cmd, capture_output=True, text=True,
                                      cwd=work_dir, timeout=60)

                if result.returncode != 0:
                    print("HSPICE simulation failed:")
                    print("STDOUT:", result.stdout)
                    print("STDERR:", result.stderr)

                    # Save error log in current directory
                    error_file = os.path.join(work_dir, "hspice_custom_error.log")
                    with open(error_file, 'w') as f:
                        f.write("HSPICE Custom Simulation Error Log\n")
                        f.write("=" * 50 + "\n")
                        f.write(f"Command: {' '.join(cmd)}\n")
                        f.write(f"Return code: {result.returncode}\n\n")
                        f.write("STDOUT:\n")
                        f.write(result.stdout)
                        f.write("\n\nSTDERR:\n")
                        f.write(result.stderr)
                    print(f"Error log saved: {error_file}")

                    return None

                # Parse results
                sim_results = hspice_runner.parse_hspice_output(output_file)

                if sim_results:
                    sim_results['netlist_file'] = input_file
                    sim_results['output_file'] = output_file
                    print(f"✓ Custom HSPICE simulation completed successfully")
                    print(f"✓ Results available in: {output_file}")

                return sim_results

            finally:
                # No cleanup needed since we're working in current directory
                pass

        except Exception as e:
            print("Custom HSPICE simulation error:", e)
            return None

    def compare_hspice_models(self):
        """Compare multiple HSPICE models"""
        # Select multiple .ckt files
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "Select HSPICE Circuit Files to Compare",
            "",
            "SPICE Circuit Files (*.ckt *.sp *.cir);;All Files (*)"
        )

        if not file_paths or len(file_paths) < 2:
            QMessageBox.warning(
                self,
                "Selection Required",
                "Please select at least 2 circuit files to compare."
            )
            return

        try:
            from .hspice_comparison_dialog import HspiceComparisonDialog

            dialog = HspiceComparisonDialog(file_paths, parent=self)
            dialog.exec_()

        except ImportError:
            # Fallback to simple comparison
            self.run_simple_model_comparison(file_paths)

    def run_simple_model_comparison(self, file_paths):
        """Run simple model comparison without dialog"""
        try:
            from simulation.hspice_runner import HspiceRunner

            hspice_runner = HspiceRunner()

            if not hspice_runner.check_hspice_installation():
                QMessageBox.critical(
                    self,
                    "HSPICE Error",
                    "HSPICE not found. Please check installation."
                )
                return

            self.status_bar.showMessage("Running comparison simulations...")

            # Run simulations for all files
            results = []
            for i, file_path in enumerate(file_paths):
                try:
                    base_name = os.path.splitext(os.path.basename(file_path))[0]
                    output_file = "{}_comparison.lis".format(base_name)

                    sim_result = self.run_custom_hspice_simulation(
                        file_path, output_file, keep_files=True
                    )

                    if sim_result:
                        sim_result['file_name'] = os.path.basename(file_path)
                        sim_result['model_name'] = base_name
                        results.append(sim_result)

                except Exception as e:
                    print("Error simulating {}: {}".format(file_path, e))

            if results:
                # Plot comparison
                self.plot_model_comparison(results)

                # Show summary
                QMessageBox.information(
                    self,
                    "Comparison Complete",
                    "Model comparison completed!\n\n"
                    "Successfully simulated {} out of {} models.\n"
                    "Results are plotted in the main window.".format(
                        len(results), len(file_paths)
                    )
                )
            else:
                QMessageBox.warning(
                    self,
                    "Comparison Failed",
                    "No successful simulations. Please check the circuit files."
                )

            self.status_bar.showMessage("Model comparison completed")

        except Exception as e:
            QMessageBox.critical(self, "Error", "Model comparison failed:\n{}".format(str(e)))

    def plot_model_comparison(self, results):
        """Plot comparison of multiple models"""
        try:
            self.plot_widget.ax.clear()
            self.plot_widget.setup_plot()

            # Plot measurement data if available
            if self.current_data is not None:
                voltage = self.current_data['voltage']
                current = self.current_data['current']
                valid_mask = current > 0

                self.plot_widget.ax.semilogy(
                    voltage[valid_mask], current[valid_mask],
                    'ko-', markersize=3, linewidth=1,
                    label='Measurement Data', alpha=0.7
                )

            # Plot each model result
            colors = ['r', 'b', 'g', 'm', 'c', 'y']
            line_styles = ['-', '--', '-.', ':']

            for i, result in enumerate(results):
                voltage = result['voltage']
                current = np.abs(result['current'])

                valid_mask = current > 0
                if np.any(valid_mask):
                    color = colors[i % len(colors)]
                    style = line_styles[i % len(line_styles)]

                    self.plot_widget.ax.semilogy(
                        voltage[valid_mask], current[valid_mask],
                        color=color, linestyle=style, linewidth=2,
                        label=result['model_name'], alpha=0.8
                    )

            self.plot_widget.ax.set_title('HSPICE Model Comparison',
                                        fontsize=14, fontweight='bold')
            self.plot_widget.ax.legend()
            self.plot_widget.canvas.draw()

        except Exception as e:
            print("Error plotting model comparison:", e)
