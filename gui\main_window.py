"""
Main Window for BJT ESD Parameter Extractor
"""

import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QSplitter, QMenuBar, QMenu, QAction, QFileDialog,
                             QMessageBox, QStatusBar, QToolBar)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon

from .plot_widget import PlotWidget
from .parameter_widget import ParameterWidget
from data.data_loader import DataLoader
from data.parameter_extractor import ParameterExtractor
from models.spice_generator import SpiceGenerator

class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        self.data_loader = DataLoader()
        self.parameter_extractor = ParameterExtractor()
        self.spice_generator = SpiceGenerator()
        
        self.current_data = None
        self.current_parameters = None
        
        self.init_ui()
        self.connect_signals()
        
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("BJT ESD Parameter Extractor v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create splitter for plot and parameters
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)
        
        # Create plot widget (top)
        self.plot_widget = PlotWidget()
        splitter.addWidget(self.plot_widget)
        
        # Create parameter widget (bottom)
        self.parameter_widget = ParameterWidget()
        splitter.addWidget(self.parameter_widget)
        
        # Set splitter proportions (70% plot, 30% parameters)
        splitter.setSizes([560, 240])
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Load data file to begin")
        
    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        # Load data action
        load_action = QAction('Load Data...', self)
        load_action.setShortcut('Ctrl+O')
        load_action.triggered.connect(self.load_data_file)
        file_menu.addAction(load_action)
        
        file_menu.addSeparator()
        
        # Save model action
        save_model_action = QAction('Save SPICE Model...', self)
        save_model_action.setShortcut('Ctrl+S')
        save_model_action.triggered.connect(self.save_spice_model)
        file_menu.addAction(save_model_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('Tools')
        
        # Extract parameters action
        extract_action = QAction('Extract Parameters', self)
        extract_action.setShortcut('F5')
        extract_action.triggered.connect(self.extract_parameters)
        tools_menu.addAction(extract_action)
        
        # Run HSPICE simulation action
        simulate_action = QAction('Run HSPICE Simulation', self)
        simulate_action.setShortcut('F6')
        simulate_action.triggered.connect(self.run_hspice_simulation)
        tools_menu.addAction(simulate_action)
        
    def create_toolbar(self):
        """Create application toolbar"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # Load data button
        load_action = QAction('Load Data', self)
        load_action.triggered.connect(self.load_data_file)
        toolbar.addAction(load_action)
        
        toolbar.addSeparator()
        
        # Extract parameters button
        extract_action = QAction('Extract Parameters', self)
        extract_action.triggered.connect(self.extract_parameters)
        toolbar.addAction(extract_action)
        
        # Save model button
        save_action = QAction('Save Model', self)
        save_action.triggered.connect(self.save_spice_model)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # Simulate button
        simulate_action = QAction('HSPICE Simulation', self)
        simulate_action.triggered.connect(self.run_hspice_simulation)
        toolbar.addAction(simulate_action)
        
    def connect_signals(self):
        """Connect widget signals"""
        # Connect parameter changes to plot updates
        self.parameter_widget.parameters_changed.connect(self.update_model_plot)
        
    def load_data_file(self):
        """Load measurement data from CSV file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "Load Measurement Data", 
            "", 
            "CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            try:
                self.current_data = self.data_loader.load_csv(file_path)
                self.plot_widget.plot_measurement_data(self.current_data)
                self.status_bar.showMessage(f"Loaded data from: {os.path.basename(file_path)}")
                
                # Auto-extract parameters
                self.extract_parameters()
                
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load data file:\n{str(e)}")
                
    def extract_parameters(self):
        """Extract BJT ESD parameters from measurement data"""
        if self.current_data is None:
            QMessageBox.warning(self, "Warning", "Please load measurement data first")
            return
            
        try:
            # Extract parameters
            self.current_parameters = self.parameter_extractor.extract_parameters(self.current_data)
            
            # Update parameter widget
            self.parameter_widget.set_parameters(self.current_parameters)
            
            # Update plot with model
            self.update_model_plot()
            
            self.status_bar.showMessage("Parameters extracted successfully")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to extract parameters:\n{str(e)}")
            
    def update_model_plot(self):
        """Update plot with current model parameters"""
        if self.current_data is None:
            return
            
        try:
            # Get current parameters from widget
            parameters = self.parameter_widget.get_parameters()
            
            # Generate model curve
            voltage_range = self.current_data['voltage']
            model_current = self.parameter_extractor.calculate_model_current(voltage_range, parameters)
            
            # Update plot
            self.plot_widget.plot_model_curve(voltage_range, model_current)
            
        except Exception as e:
            print(f"Error updating model plot: {e}")
            
    def save_spice_model(self):
        """Save SPICE model to .ckt file"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save SPICE Model",
            "bjt_esd_model.ckt",
            "SPICE Circuit Files (*.ckt);;All Files (*)"
        )
        
        if file_path:
            try:
                # Get current parameters
                parameters = self.parameter_widget.get_parameters()
                
                # Generate SPICE model
                spice_content = self.spice_generator.generate_bjt_esd_model(parameters)
                
                # Save to file
                with open(file_path, 'w') as f:
                    f.write(spice_content)
                    
                self.status_bar.showMessage(f"SPICE model saved to: {os.path.basename(file_path)}")
                QMessageBox.information(self, "Success", f"SPICE model saved successfully to:\n{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save SPICE model:\n{str(e)}")
                
    def run_hspice_simulation(self):
        """Run HSPICE simulation with current model"""
        if self.current_parameters is None:
            QMessageBox.warning(self, "Warning", "Please extract parameters first")
            return
            
        try:
            from simulation.hspice_runner import HspiceRunner
            
            # Get current parameters
            parameters = self.parameter_widget.get_parameters()
            
            # Create HSPICE runner
            hspice_runner = HspiceRunner()
            
            # Run simulation
            sim_results = hspice_runner.run_simulation(parameters)
            
            if sim_results:
                # Plot simulation results
                self.plot_widget.plot_simulation_results(sim_results)
                self.status_bar.showMessage("HSPICE simulation completed successfully")
            else:
                QMessageBox.warning(self, "Warning", "HSPICE simulation failed or returned no results")
                
        except ImportError:
            QMessageBox.warning(self, "Warning", "HSPICE simulation module not available")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"HSPICE simulation failed:\n{str(e)}")
