#!/usr/bin/env python3
"""
Test GUI Integration for Exact Parameter Conversion
"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test all GUI imports"""
    print("Testing GUI imports...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5 available")
        
        from gui.main_window import MainWindow
        print("✓ MainWindow import OK")
        
        from gui.exact_conversion_dialog import ExactConversionDialog
        print("✓ ExactConversionDialog import OK")
        
        from models.exact_parameter_converter import ExactParameterConverter
        print("✓ ExactParameterConverter import OK")
        
        return True
        
    except Exception as e:
        print("✗ Import error: {}".format(e))
        return False

def test_gui_creation():
    """Test GUI creation without showing"""
    print("\nTesting GUI creation...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import MainWindow
        
        # Create QApplication (required for Qt widgets)
        app = QApplication([])
        
        # Create main window
        main_window = MainWindow()
        print("✓ Main window created successfully")
        
        # Test if exact converter is initialized
        if hasattr(main_window, 'exact_converter'):
            print("✓ Exact converter initialized")
        else:
            print("✗ Exact converter not found")
            
        # Test if conversion results attribute exists
        if hasattr(main_window, 'conversion_results'):
            print("✓ Conversion results attribute exists")
        else:
            print("✗ Conversion results attribute missing")
            
        # Test menu actions
        menubar = main_window.menuBar()
        if menubar:
            print("✓ Menu bar created")
        else:
            print("✗ Menu bar missing")
            
        # Test toolbar
        toolbar = main_window.findChild(main_window.__class__, 'toolbar')
        print("✓ Toolbar integration checked")
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print("✗ GUI creation error: {}".format(e))
        return False

def test_exact_conversion_dialog():
    """Test exact conversion dialog creation"""
    print("\nTesting exact conversion dialog...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.exact_conversion_dialog import ExactConversionDialog
        import numpy as np
        
        # Create QApplication
        app = QApplication([])
        
        # Create test data
        voltage = np.linspace(0, 20, 100)
        current = np.abs(np.random.random(100) * 1e-6)
        data = {'voltage': voltage, 'current': current}
        
        # Create test parameters
        parameters = {
            'I_leak': 1e-9,
            'Vt1': 12.0,
            'k': 3.0,
            'Ron': 2.0,
            'Vh': 14.0,
            'I_offset': 0.05,
            'Isb': 0.04,
            'Vsb': 14.0
        }
        
        # Create dialog
        dialog = ExactConversionDialog(data, parameters)
        print("✓ Exact conversion dialog created")
        
        # Test dialog components
        if hasattr(dialog, 'tab_widget'):
            print("✓ Tab widget exists")
        if hasattr(dialog, 'progress_bar'):
            print("✓ Progress bar exists")
        if hasattr(dialog, 'results_table'):
            print("✓ Results table exists")
            
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print("✗ Dialog creation error: {}".format(e))
        return False

def test_parameter_widget_integration():
    """Test parameter widget integration"""
    print("\nTesting parameter widget integration...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.parameter_widget import ParameterWidget
        
        # Create QApplication
        app = QApplication([])
        
        # Create parameter widget
        param_widget = ParameterWidget()
        print("✓ Parameter widget created")
        
        # Test if exact conversion signal exists
        if hasattr(param_widget, 'exact_conversion_requested'):
            print("✓ Exact conversion signal exists")
        else:
            print("✗ Exact conversion signal missing")
            
        # Test parameter controls
        if hasattr(param_widget, 'parameter_controls'):
            print("✓ Parameter controls exist")
        else:
            print("✗ Parameter controls missing")
            
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print("✗ Parameter widget error: {}".format(e))
        return False

def test_plot_widget_integration():
    """Test plot widget integration"""
    print("\nTesting plot widget integration...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.plot_widget import PlotWidget
        import numpy as np
        
        # Create QApplication
        app = QApplication([])
        
        # Create plot widget
        plot_widget = PlotWidget()
        print("✓ Plot widget created")
        
        # Test if exact conversion plot methods exist
        if hasattr(plot_widget, 'plot_exact_conversion_comparison'):
            print("✓ Exact conversion comparison method exists")
        else:
            print("✗ Exact conversion comparison method missing")
            
        if hasattr(plot_widget, 'plot_conversion_error_analysis'):
            print("✓ Error analysis plot method exists")
        else:
            print("✗ Error analysis plot method missing")
            
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print("✗ Plot widget error: {}".format(e))
        return False

def main():
    """Main test function"""
    print("BJT ESD GUI Integration Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_gui_creation,
        test_exact_conversion_dialog,
        test_parameter_widget_integration,
        test_plot_widget_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print("Test failed with exception: {}".format(e))
    
    print("\n" + "=" * 40)
    print("Integration Test Results: {}/{} tests passed".format(passed, total))
    
    if passed == total:
        print("✓ All integration tests passed!")
        print("✓ GUI is ready for exact parameter conversion")
        print("\nFeatures integrated:")
        print("  • Exact conversion menu items")
        print("  • Exact conversion toolbar buttons")
        print("  • Exact conversion dialog")
        print("  • Parameter widget integration")
        print("  • Plot widget enhancements")
        print("  • Validation and error analysis")
    else:
        print("✗ Some integration tests failed")
        print("Check the errors above for details")
    
    print("\nTo test the full GUI:")
    print("  python main.py")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
